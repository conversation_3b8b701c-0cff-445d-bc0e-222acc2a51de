# ============================================
# Chef Stage – use Ubuntu 22.04 slim for best Rust compatibility
# ============================================
FROM ubuntu:22.04 AS chef
WORKDIR /app

# Install Rust toolchain & cargo‑chef (one layer)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl ca-certificates build-essential pkg-config \
        libssl-dev libpq-dev libsqlite3-dev postgresql-server-dev-all && \
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --profile minimal && \
    . $HOME/.cargo/env && cargo install cargo-chef --locked && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

ENV PATH="/root/.cargo/bin:${PATH}"

# ============================================
# Planner Stage – generate recipe.json
# ============================================
FROM chef AS planner
COPY . .
RUN cargo chef prepare --recipe-path recipe.json

# ============================================
# Builder Stage – build deps & app, then strip
# ============================================
FROM chef AS builder
ARG DATABASE_URL
ENV DATABASE_URL=${DATABASE_URL}
COPY --from=planner /app/recipe.json recipe.json
RUN cargo chef cook --release --recipe-path recipe.json

COPY . .
RUN cargo build --release --bin platform-rust && \
    strip --strip-unneeded target/release/platform-rust

# ============================================
# Deps Stage – install runtime packages & tzdata
# ============================================
FROM debian:bookworm-slim AS deps
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        libssl3 libpq5 libsqlite3-0 tzdata && \
    # Clean & slim tzdata
    rm -rf /usr/share/zoneinfo/{posix,right} /usr/share/doc/tzdata /usr/share/locale && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /var/cache/apt/archives/*

# ============================================
# bin-libs Stage – collect only libraries needed by the binary
# ============================================
FROM deps AS bin-libs
COPY --from=builder /app/target/release/platform-rust /tmp/platform-rust
RUN set -eux; mkdir -p /out && \
    ldd /tmp/platform-rust | awk '{print $3}' | grep '^/' | xargs -I '{}' cp --parents -v '{}' /out/

# ============================================
# Runtime Stage – Distroless base‑debian12 (~15 MB)
# ============================================
FROM gcr.io/distroless/base-debian12

# Copy only needed shared libs and tzdata zoneinfo
COPY --from=bin-libs /out/ /
COPY --from=deps /usr/share/zoneinfo /usr/share/zoneinfo

# Copy stripped Rust binary and assets
WORKDIR /app
COPY --from=builder /app/target/release/platform-rust /app/platform-rust
COPY --from=builder /app/sqlx-migrations /app/sqlx-migrations

# Distroless has a built‑in non‑root user (UID 65532, "nonroot")
USER nonroot

# Environment (override via compose/runtime as needed)
ENV DATABASE_URL="**************************************/platform_rust" \
    SERVER_HOST=0.0.0.0 \
    SERVER_PORT=8386 \
    RUST_LOG=platform_rust=info,tower_http=info \
    DB_MAX_CONNECTIONS=16 \
    DB_MIN_IDLE_CONNECTIONS=4 \
    DB_CONNECTION_TIMEOUT_SECS=10 \
    DB_IDLE_TIMEOUT_SECS=300 \
    DB_MAX_LIFETIME_SECS=1800

EXPOSE 8386

CMD ["/app/platform-rust"]
