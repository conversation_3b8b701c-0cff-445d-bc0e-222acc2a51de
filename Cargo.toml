[package]
name = "platform-rust"
version = "0.1.0"
edition = "2024"

[dependencies]
# --- Web / Async stack ---
axum = "0.8"
tokio = { version = "1.41", features = ["full"] }
tower = "0.5"
tower-http = { version = "0.6", features = ["compression-full", "cors", "fs"] }
# tower_governor = "0.4.2" # Removed due to complex API

# --- Serialization ---
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# --- Config & utils ---
dotenvy = "0.15"
anyhow = "1.0"
thiserror = "2.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "fmt"] }
tracing-appender = "0.2"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.11", features = ["v4", "serde"] }
validator = { version = "0.20", features = ["derive"] }
async-trait = "0.1"
regex = "1.10"
lazy_static = "1.4"

# --- Database ---
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json", "rust_decimal"] }

# --- Redis ---
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
bb8-redis = "0.14"

# --- Security ---
argon2 = "0.5"
jsonwebtoken = "9.3"
oauth2 = "4.4"
reqwest = { version = "0.11", features = ["json"] }

# --- 📄 API Documentation ---
utoipa = { version = "5", features = ["axum_extras", "chrono", "uuid", "decimal_float"] }
utoipa-swagger-ui = { version = "9", features = ["axum"] }

# --- GraphQL ---
async-graphql = { version = "7.0", features = ["chrono"] }
async-graphql-axum = "7.0"

# Email dependencies
lettre = { version = "0.11", features = ["tokio1", "smtp-transport", "tokio1-native-tls"] }
handlebars = "4.5"
mime = "0.3"
rust_decimal = { version = "1.37.2", features = ["db-postgres"] }
lru = "0.16.0"
futures = "0.3.31"
tokio-stream = "0.1.17"
base64 = "0.22.1"
sha1 = "0.10"
hex = "0.4"
