# 🎙️ PODCAST: Rust Journey - Tập 1 (Extended)
## "Từ Zero đến Hero: Những viên gạch đầu tiên trong Platform Rust"

### 🎬 INTRO (45 giây)
**[Nhạc nền nhẹ nhàng, progressive house style]**

**Host:** Chào mừng các bạn đến với podcast "Rust Journey" - nơi chúng ta cùng nhau khám phá hành trình xây dựng một hệ thống backend hoàn chỉnh bằng Rust. Tô<PERSON> là [Tên host], và hôm nay chúng ta sẽ bắt đầu từ con số 0 - từ một người hoàn toàn không biết gì về Rust, đến việc đặt những viên gạch đầu tiên cho một platform phức tạp.

Trong suốt series này, chúng ta sẽ không chỉ học Rust một cách isolated, mà sẽ constantly compare với những gì bạn đã biết - JavaScript/TypeScript cho web developers, và Go cho những ai đã từng làm backend. Bởi vì thực tế là, học một ngôn ngữ mới không phải là bắt đầu từ con số 0, mà là mapping những concept quen thuộc sang paradigm mới.

---

### 🏁 PHẦN 1: ĐIỂM XUẤT PHÁT - "Tại sao Rust?" (5 phút)

**Host:** Hãy tưởng tượng bạn là một developer có kinh nghiệm với JavaScript, Python, hoặc Java. Bạn nghe nhiều về Rust - ngôn ngữ "an toàn", "nhanh", "không có garbage collector". Nhưng khi bắt đầu, bạn sẽ gặp phải cú sốc văn hóa đầu tiên: **Borrow Checker**.

**So sánh với JavaScript/TypeScript:**

Trong JavaScript, bạn viết code như thế này và không cần lo về memory:
```javascript
// JavaScript - Carefree memory management
let users = [{name: "Alice"}, {name: "Bob"}];
function processUsers(userList) {
    userList.push({name: "Charlie"}); // Mutate freely
    return userList.map(u => u.name);
}
let result = processUsers(users); // users is also modified
```

TypeScript thêm type safety:
```typescript
// TypeScript - Type safety at compile time
interface User { name: string; }
let users: User[] = [{name: "Alice"}, {name: "Bob"}];
function processUsers(userList: User[]): string[] {
    userList.push({name: "Charlie"});
    return userList.map(u => u.name);
}
```

Nhưng TypeScript vẫn không prevent mutations, không báo khi bạn accidentally modify shared data. V8 garbage collector sẽ clean up memory, nhưng GC pauses có thể impact performance.

**Trong Rust, story hoàn toàn khác:**
```rust
// Rust - Ownership and borrowing
let mut users = vec![User{name: "Alice".to_string()}];

fn process_users(user_list: &mut Vec<User>) -> Vec<String> {
    user_list.push(User{name: "Charlie".to_string()});
    user_list.iter().map(|u| u.name.clone()).collect()
}

let result = process_users(&mut users); // Explicit mutable borrow
```

Borrow checker ép bạn declare intentions: `&` for immutable borrow, `&mut` for mutable borrow. Không thể có data races vì chỉ một `&mut` reference tại một thời điểm.

**So sánh với Go:**

Go cũng compiled, cũng strong typing, nhưng approach khác:
```go
// Go - Simpler but less safe
type User struct {
    Name string
}

func processUsers(users []User) []string {
    users = append(users, User{Name: "Charlie"}) // This doesn't modify original!
    names := make([]string, len(users))
    for i, u := range users {
        names[i] = u.Name
    }
    return names
}
```

Go có garbage collector (như JS), đơn giản hơn Rust nhưng ít control hơn về performance. Go slice semantics khó đoán - append có thể tạo copy mới, không modify original. Rust ownership rules explicit hơn, ít surprising behavior.

**Đây chính là câu chuyện của codebase mà chúng ta đang phân tích.** Nhìn vào file `Cargo.toml`:

```toml
[package]
name = "platform-rust"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
axum = "0.7"
sqlx = { version = "0.7", features = ["postgres", "runtime-tokio-rustls"] }
```

**Performance comparison thực tế:**
- **JavaScript/Node.js**: V8 JIT compiler nhanh, nhưng GC pauses và single-threaded event loop limit scaling
- **Go**: Compiled binary, GC tuned cho low latency, excellent cho microservices
- **Rust**: Zero-cost abstractions, no GC, control memory layout. Fastest among three nhưng steepest learning curve.

**Concurrency models:**
- **JS**: Single-threaded event loop, async/await syntax sugar over promises
- **Go**: Goroutines với channels, "Don't communicate by sharing memory; share memory by communicating"
- **Rust**: Multiple async runtimes (Tokio, async-std), ownership prevents data races, có thể share memory safely

---

### 🧱 PHẦN 2: VIÊN GẠCH ĐẦU TIÊN - Configuration Management (6 phút)

**Host:** Mọi ứng dụng backend đều bắt đầu từ việc quản lý cấu hình. Và đây là bài học đầu tiên về sự khác biệt giữa dynamic typing và static typing with ownership.

**JavaScript/Node.js approach:**
```javascript
// JavaScript - Runtime configuration
const config = {
    database_url: process.env.DATABASE_URL || "postgres://localhost/mydb",
    server_port: parseInt(process.env.PORT) || 3000, // Runtime parsing
    debug: process.env.NODE_ENV !== "production"
};

// Có thể modify runtime
config.server_port = "8080"; // Oops! String instead of number
console.log(config.server_port + 100); // "8080100" instead of 8180
```

**TypeScript cải thiện nhưng vẫn có holes:**
```typescript
// TypeScript - Better but not perfect
interface Config {
    database_url: string;
    server_port: number;
    debug: boolean;
}

const config: Config = {
    database_url: process.env.DATABASE_URL || "postgres://localhost/mydb",
    server_port: parseInt(process.env.PORT || "3000"), // Still runtime parsing
    debug: process.env.NODE_ENV !== "production"
};

// TypeScript can't prevent this at runtime:
(config as any).server_port = "8080"; // Type assertion bypass
```

**Go approach - Better structure:**
```go
// Go - Struct-based configuration
type Config struct {
    DatabaseURL string
    ServerPort  int
    Debug      bool
}

func LoadConfig() (*Config, error) {
    port, err := strconv.Atoi(getEnv("PORT", "3000"))
    if err != nil {
        return nil, fmt.Errorf("invalid port: %w", err)
    }
    
    return &Config{
        DatabaseURL: getEnv("DATABASE_URL", "postgres://localhost/mydb"),
        ServerPort:  port,
        Debug:      getEnv("NODE_ENV", "") != "production",
    }, nil
}
```

Go có explicit error handling, nhưng vẫn có thể mutate struct fields nếu không careful với exported fields.

**Rust approach - Type safety và immutability by default:**

Nhìn vào `src/config.rs` của codebase:
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub server_host: String,
    pub server_port: u16, // Specific type, không thể âm
    pub max_connections: u32,
    pub token_config: TokenConfig,
    pub performance_config: DatabasePerformanceConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenConfig {
    pub secret_key: String,
    pub expires_in: u64, // seconds
    pub refresh_expires_in: u64,
}

impl Config {
    pub fn from_env() -> Result<Self, ConfigError> {
        let server_port = std::env::var("SERVER_PORT")
            .unwrap_or_else(|_| "8386".to_string())
            .parse::<u16>() // Compile-time guaranteed type
            .map_err(|_| ConfigError::InvalidPort)?;
            
        // Nested config with validation
        let token_config = TokenConfig {
            secret_key: std::env::var("JWT_SECRET")
                .map_err(|_| ConfigError::MissingSecret)?,
            expires_in: std::env::var("JWT_EXPIRES_IN")
                .unwrap_or_else(|_| "3600".to_string())
                .parse()
                .map_err(|_| ConfigError::InvalidDuration)?,
            refresh_expires_in: std::env::var("JWT_REFRESH_EXPIRES_IN")
                .unwrap_or_else(|_| "604800".to_string())
                .parse()
                .map_err(|_| ConfigError::InvalidDuration)?,
        };
        
        Ok(Self {
            database_url: std::env::var("DATABASE_URL")
                .map_err(|_| ConfigError::MissingDatabaseUrl)?,
            server_host: std::env::var("SERVER_HOST")
                .unwrap_or_else(|_| "127.0.0.1".to_string()),
            server_port,
            max_connections: std::env::var("MAX_DB_CONNECTIONS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            token_config,
            performance_config: DatabasePerformanceConfig::default(),
        })
    }
}
```

**Những điểm khác biệt quan trọng:**

1. **Derive macros** - `#[derive(Debug, Clone)]` auto-generate code, tương tự decorators trong TS nhưng compile-time:
   - JS: Không có tương đương native
   - Go: Struct tags cho JSON marshaling, nhưng ít powerful hơn
   - Rust: Procedural macros có thể generate arbitrary code

2. **Strong typing with ranges** - `u16` cho port (0-65535), `u32` cho connections:
   - JS: `number` type cho everything, có thể âm hoặc float
   - Go: `int` có thể âm, phải manual validation
   - Rust: Type system prevent invalid values from the start

3. **Error handling strategy**:
   ```rust
   #[derive(thiserror::Error, Debug)]
   pub enum ConfigError {
       #[error("Missing database URL")]
       MissingDatabaseUrl,
       #[error("Invalid port number")]
       InvalidPort,
       #[error("Missing JWT secret")]
       MissingSecret,
   }
   ```
   - JS: Throw strings hoặc Error objects, không structured
   - Go: Error interface với string messages, ít context
   - Rust: Enum-based errors với structured information

4. **Result type propagation**:
   - JS: Try/catch blocks, có thể miss exceptions
   - Go: `if err != nil` everywhere, verbose nhưng explicit
   - Rust: `?` operator cho error propagation, concise và explicit

**Nested configuration complexity:**
```rust
#[derive(Debug, Clone)]
pub struct DatabasePerformanceConfig {
    pub statement_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

impl Default for DatabasePerformanceConfig {
    fn default() -> Self {
        Self {
            statement_timeout: Duration::from_secs(30),
            idle_timeout: Duration::from_secs(600),
            max_lifetime: Duration::from_secs(1800),
        }
    }
}
```

**Bài học thực tế:** Rust configuration management force bạn think about:
- What can go wrong (explicit error types)
- Data validation at parse time (not runtime)
- Immutability by default (fields are private unless `pub`)
- Type-safe duration handling (không phải milliseconds/seconds confusion)

---

### ⚡ PHẦN 3: ASYNC VÀ TOKIO - Cuộc chiến của các paradigms (7 phút)

**Host:** Đây là nơi mà complexity của Rust async thực sự show up. Let's compare async programming across our three languages:

**JavaScript - Event Loop Heaven:**
```javascript
// JavaScript - Single-threaded async
async function fetchUserData(userId) {
    try {
        const user = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
        const posts = await db.query('SELECT * FROM posts WHERE user_id = ?', [userId]);
        return { user, posts };
    } catch (error) {
        throw new Error(`Failed to fetch user data: ${error.message}`);
    }
}

// Concurrent requests
const results = await Promise.all([
    fetchUserData(1),
    fetchUserData(2),
    fetchUserData(3)
]);
```

JavaScript async programming đơn giản: single thread, event loop handle scheduling, garbage collector manage memory. Promises chain naturally, error handling với try/catch.

**Go - Goroutines và Channels:**
```go
// Go - Goroutines with channels
func fetchUserData(ctx context.Context, userId int) (*UserData, error) {
    userChan := make(chan *User, 1)
    postsChan := make(chan []*Post, 1)
    errChan := make(chan error, 2)
    
    // Concurrent database queries
    go func() {
        user, err := db.QueryUser(ctx, userId)
        if err != nil {
            errChan <- err
            return
        }
        userChan <- user
    }()
    
    go func() {
        posts, err := db.QueryPosts(ctx, userId)
        if err != nil {
            errChan <- err
            return
        }
        postsChan <- posts
    }()
    
    // Wait for results
    var user *User
    var posts []*Post
    for i := 0; i < 2; i++ {
        select {
        case u := <-userChan:
            user = u
        case p := <-postsChan:
            posts = p
        case err := <-errChan:
            return nil, err
        case <-ctx.Done():
            return nil, ctx.Err()
        }
    }
    
    return &UserData{User: user, Posts: posts}, nil
}
```

Go goroutines lightweight (2KB stack), communicate qua channels. Context cho cancellation. Simple model nhưng verbose, và goroutine leaks có thể occur nếu không careful.

**Rust/Tokio - Ownership meets Async:**

Từ `src/main.rs` của codebase:
```rust
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::init();
    
    // Load configuration
    let config = Config::from_env()
        .map_err(|e| anyhow::anyhow!("Failed to load config: {}", e))?;
    
    // Initialize database with connection pooling
    let database = Database::new(&config.database_url).await
        .map_err(|e| anyhow::anyhow!("Database connection failed: {}", e))?;
    
    // Run migrations
    database.migrate().await
        .map_err(|e| anyhow::anyhow!("Migration failed: {}", e))?;
    
    // Initialize service container - this is where DI complexity shows
    let container = ServiceContainer::new(database, config.clone()).await
        .map_err(|e| anyhow::anyhow!("Service initialization failed: {}", e))?;
    
    // Build and run server
    let app = build_router(container);
    let listener = tokio::net::TcpListener::bind(&format!("{}:{}", 
        config.server_host, config.server_port)).await?;
    
    tracing::info!("Server listening on {}:{}", config.server_host, config.server_port);
    axum::serve(listener, app).await?;
    
    Ok(())
}
```

**Điểm khác biệt quan trọng:**

1. **Runtime Selection:**
   - JS: Built-in event loop, no choice
   - Go: Native goroutines, no external runtime
   - Rust: Choose runtime (Tokio, async-std, smol), each with trade-offs

2. **Future Traits:**
   ```rust
   // Rust futures must be Send + Sync for multi-threaded runtime
   async fn fetch_user_data(user_id: i32) -> Result<UserData, AppError> {
       let db_pool = get_db_pool(); // Must be Clone + Send + Sync
       
       let (user, posts) = tokio::try_join!(
           db_pool.query_user(user_id),    // Future must be Send
           db_pool.query_posts(user_id)    // Future must be Send
       )?;
       
       Ok(UserData { user, posts })
   }
   ```
   - JS: No concept của Send/Sync, single thread
   - Go: Goroutines automatically shareable
   - Rust: Compiler verify futures can cross thread boundaries

3. **Error Propagation trong Async Context:**
   ```rust
   // Multiple error types need conversion
   impl From<sqlx::Error> for AppError {
       fn from(err: sqlx::Error) -> Self {
           match err {
               sqlx::Error::RowNotFound => AppError::NotFound("User not found".to_string()),
               sqlx::Error::Database(db_err) if db_err.code() == Some("23505") => {
                   AppError::Conflict("User already exists".to_string())
               }
               _ => AppError::Database(err.to_string()),
           }
       }
   }
   ```

4. **Connection Pooling Patterns:**
   - **JS/Node.js:**
   ```javascript
   const pool = new Pool({
       connectionString: process.env.DATABASE_URL,
       max: 20 // Simple number
   });
   ```
   
   - **Go:**
   ```go
   db.SetMaxOpenConns(25)
   db.SetMaxIdleConns(5)
   db.SetConnMaxLifetime(5 * time.Minute)
   ```
   
   - **Rust/SQLx:**
   ```rust
   let pool = PgPoolOptions::new()
       .max_connections(config.max_connections)
       .acquire_timeout(Duration::from_secs(30))
       .idle_timeout(Duration::from_secs(600))
       .max_lifetime(Duration::from_secs(1800))
       .connect(&config.database_url)
       .await?;
   ```

**Tokio Runtime Configuration:**
```rust
// Custom runtime tuning
let rt = tokio::runtime::Builder::new_multi_thread()
    .worker_threads(num_cpus::get())
    .enable_all()
    .build()?;

rt.block_on(async {
    // Your async code here
});
```

**Challenges cho developers:**

1. **Lifetime Issues với Async:**
   ```rust
   // This won't compile - lifetime issues
   async fn process_data(data: &str) -> Result<String, Error> {
       let processed = expensive_operation(data).await?;
       // 'data' reference might not live long enough
       Ok(processed)
   }
   
   // Solution: Take ownership
   async fn process_data(data: String) -> Result<String, Error> {
       let processed = expensive_operation(&data).await?;
       Ok(processed)
   }
   ```

2. **Async Closures Complexity:**
   ```rust
   // JS: Easy async iteration
   const results = await Promise.all(
       users.map(async user => await processUser(user))
   );
   
   // Rust: More complex due to ownership
   let results: Vec<ProcessedUser> = futures::future::try_join_all(
       users.into_iter().map(|user| {
           let db_pool = db_pool.clone(); // Clone for each future
           async move { process_user(user, &db_pool).await }
       })
   ).await?;
   ```

**Performance Implications:**
- **JS**: Single-threaded, good cho I/O intensive, bad cho CPU intensive
- **Go**: M:N scheduling, excellent cho concurrent I/O, simple scaling
- **Rust**: Zero-cost async, fine-grained control, highest performance ceiling nhưng phức tạp nhất

---

### 🗃️ PHẦN 4: DATABASE - SQLx vs Diesel vs ORM Wars (8 phút)

**Host:** Đây là plot twist lớn nhất trong codebase này! Migration từ Diesel sang SQLx represent một fundamental shift trong Rust database ecosystem. Let's understand why through lens của các approaches khác nhau:

**JavaScript/TypeScript ORM Landscape:**

**Prisma (Modern favorite):**
```typescript
// Prisma - Schema-driven with code generation
// schema.prisma
model User {
  id       Int      @id @default(autoincrement())
  email    String   @unique
  name     String?
  posts    Post[]
  createdAt DateTime @default(now())
}

// Generated client usage
const user = await prisma.user.create({
  data: {
    email: "<EMAIL>",
    name: "John Doe",
    posts: {
      create: [
        { title: "First Post", content: "Hello World" }
      ]
    }
  },
  include: {
    posts: true
  }
});
```

**Sequelize (Traditional ORM):**
```javascript
// Sequelize - ActiveRecord pattern
const User = sequelize.define('User', {
  email: { type: DataTypes.STRING, unique: true },
  name: DataTypes.STRING
});

const Post = sequelize.define('Post', {
  title: DataTypes.STRING,
  content: DataTypes.TEXT
});

User.hasMany(Post);
Post.belongsTo(User);

// Usage
const user = await User.create({
  email: "<EMAIL>",
  name: "John Doe"
});
```

**Go Database Approaches:**

**GORM (Popular ORM):**
```go
// GORM - Reflection-based ORM
type User struct {
    ID        uint      `gorm:"primarykey"`
    Email     string    `gorm:"uniqueIndex"`
    Name      string
    Posts     []Post
    CreatedAt time.Time
}

type Post struct {
    ID     uint   `gorm:"primarykey"`
    Title  string
    Content string
    UserID uint
}

// Usage
var user User
db.Preload("Posts").First(&user, "email = ?", "<EMAIL>")

// Create with association
db.Create(&User{
    Email: "<EMAIL>",
    Name:  "John Doe",
    Posts: []Post{
        {Title: "First Post", Content: "Hello World"},
    },
})
```

**sqlx (Go) - Raw SQL:**
```go
// sqlx - Raw SQL with struct scanning
type User struct {
    ID        int       `db:"id"`
    Email     string    `db:"email"`
    Name      string    `db:"name"`
    CreatedAt time.Time `db:"created_at"`
}

// Query
var user User
err := db.Get(&user, "SELECT * FROM users WHERE email = $1", "<EMAIL>")

// Insert
result, err := db.Exec(`
    INSERT INTO users (email, name, created_at) 
    VALUES ($1, $2, $3)`,
    "<EMAIL>", "John Doe", time.Now())
```

**Rust Database Evolution Story:**

**Phase 1 - Diesel Era (ORM Approach):**
```rust
// Diesel - Compile-time ORM
// schema.rs (generated from migrations)
table! {
    users (id) {
        id -> Int4,
        email -> Varchar,
        name -> Nullable<Varchar>,
        created_at -> Timestamp,
    }
}

table! {
    posts (id) {
        id -> Int4,
        title -> Varchar,
        content -> Text,
        user_id -> Int4,
        created_at -> Timestamp,
    }
}

// Model structs
#[derive(Queryable, Identifiable)]
pub struct User {
    pub id: i32,
    pub email: String,
    pub name: Option<String>,
    pub created_at: chrono::NaiveDateTime,
}

#[derive(Insertable)]
#[table_name = "users"]
pub struct NewUser<'a> {
    pub email: &'a str,
    pub name: Option<&'a str>,
}

// Usage - Type-safe query building
use diesel::prelude::*;

fn create_user(conn: &PgConnection, email: &str, name: Option<&str>) -> QueryResult<User> {
    let new_user = NewUser { email, name };
    
    diesel::insert_into(users::table)
        .values(&new_user)
        .get_result(conn)
}

fn find_users_with_posts(conn: &PgConnection) -> QueryResult<Vec<(User, Vec<Post>)>> {
    users::table
        .inner_join(posts::table)
        .load::<(User, Post)>(conn)
        .map(|results| {
            // Group by user - complex logic needed
            // ...
        })
}
```

**Diesel Problems trong Async World:**
1. **Synchronous only** - phải wrap trong `spawn_blocking`
2. **Connection management** - không integrate tốt với connection pools
3. **Complex async patterns:**
   ```rust
   // Awkward async wrapper
   pub async fn create_user_async(
       pool: &PgPool, 
       email: &str
   ) -> Result<User, Error> {
       let email = email.to_string();
       tokio::task::spawn_blocking(move || {
           let conn = pool.get()?;
           create_user(&conn, &email, None)
       }).await??
   }
   ```

**Phase 2 - SQLx Era (Current Approach):**

Từ codebase analysis, migration này massive:
```rust
// SQLx - Async-first with compile-time SQL verification
use sqlx::{PgPool, query_as};

#[derive(sqlx::FromRow, Debug, Clone)]
pub struct User {
    pub id: i32,
    pub email: String,
    pub name: Option<String>,
    pub created_at: chrono::NaiveDateTime,
}

#[derive(Debug)]
pub struct CreateUserRequest {
    pub email: String,
    pub name: Option<String>,
    pub password_hash: String,
}

impl SqlxUserRepository {
    pub async fn create(&self, user: &CreateUserRequest) -> Result<User, sqlx::Error> {
        sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (email, name, password_hash, created_at)
            VALUES ($1, $2, $3, NOW())
            RETURNING id, email, name, created_at
            "#,
            user.email,
            user.name,
            user.password_hash
        )
        .fetch_one(&self.pool)
        .await
    }
    
    pub async fn find_by_email(&self, email: &str) -> Result<Option<User>, sqlx::Error> {
        sqlx::query_as!(
            User,
            "SELECT id, email, name, created_at FROM users WHERE email = $1",
            email
        )
        .fetch_optional(&self.pool)
        .await
    }
    
    pub async fn find_with_posts(&self, user_id: i32) -> Result<UserWithPosts, sqlx::Error> {
        // Complex queries với joins
        sqlx::query_as!(
            UserWithPostsRow,
            r#"
            SELECT 
                u.id as user_id,
                u.email,
                u.name,
                u.created_at,
                p.id as post_id,
                p.title,
                p.content,
                p.created_at as post_created_at
            FROM users u
            LEFT JOIN posts p ON u.id = p.user_id
            WHERE u.id = $1
            ORDER BY p.created_at DESC
            "#,
            user_id
        )
        .fetch_all(&self.pool)
        .await
        .map(|rows| {
            // Manual aggregation logic
            aggregate_user_posts(rows)
        })
    }
}
```

**SQLx Magic - Compile-time SQL Verification:**
```rust
// This checks against actual database schema at compile time!
sqlx::query_as!(
    User,
    "SELECT id, email, name, created_at FROM users WHERE email = $1",
    email  // Type-checked parameter
)
```

**Nếu table schema không match, compile sẽ fail:**
```rust
// Compile error if 'users' table doesn't exist
// Compile error if 'email' column doesn't exist  
// Compile error if parameter type doesn't match
sqlx::query_as!(User, "SELECT * FROM nonexistent_table WHERE email = $1", email)
//                     ^^^^^^^^^^^^^^^^^ compile-time error
```

**Repository Pattern Implementation:**

**JavaScript/TypeScript:**
```typescript
// Repository pattern trong TS
class UserRepository {
    constructor(private db: Database) {}
    
    async create(userData: CreateUserData): Promise<User> {
        const result = await this.db.query(
            'INSERT INTO users (email, name) VALUES (?, ?) RETURNING *',
            [userData.email, userData.name]
        );
        return result[0]; // Runtime type trust
    }
    
    async findByEmail(email: string): Promise<User | null> {
        const result = await this.db.query(
            'SELECT * FROM users WHERE email = ?',
            [email]
        );
        return result[0] || null;
    }
}
```

**Go:**
```go
type UserRepository struct {
    db *sqlx.DB
}

func (r *UserRepository) Create(ctx context.Context, user CreateUserRequest) (*User, error) {
    var created User
    err := r.db.QueryRowContext(ctx, `
        INSERT INTO