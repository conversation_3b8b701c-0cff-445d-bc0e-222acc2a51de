# 🎙️ PODCAST: Rust Journey - Tập 2
## "Ch<PERSON>a khóa và Ổ khóa: Xây dựng Authentication & Authorization trong Rust"

### 🎬 INTRO (30 giây)
**[Nhạc nền sôi động hơn một chút]**

**Host:** Ch<PERSON><PERSON> mừng trở lại với "Rust Journey"! T<PERSON><PERSON> là [Tên host]. Trong tập 1, chúng ta đã cùng nhau đặt những viên gạch đầu tiên: từ config, async runtime với Tokio, đến database và dependency injection. Đ<PERSON> là bộ xương của platform. Hôm nay, chúng ta sẽ khoác lên cho nó lớp áo giáp đầu tiên: hệ thống Authentication và Authorization. Ai được vào? Và khi vào rồi, họ được làm gì?

---

### ⏪ PHẦN 1: RECAP TẬP 1 (2 phút)

**Host:** Ở tập trướ<PERSON>, chúng ta đã thấy một developer "zero-to-hero" vật l<PERSON><PERSON> v<PERSON><PERSON>, chọ<PERSON>x thay vì <PERSON>, và xây dựng một Service Container phức tạp bằng `Arc<dyn Trait>`. Nền tảng đã vững:
- **Configuration** (`config.rs`): Type-safe và mạnh mẽ.
- **Error Handling** (`errors.rs`): Rõ ràng với `thiserror` và `AppError` enum.
- **Async Foundation** (`main.rs`): Tokio làm trung tâm.
- **Dependency Injection** (`container/`): Quản lý sự phụ thuộc một cách tường minh.

Nếu bạn là người mới, hãy nghe lại tập 1. Còn bây giờ, hãy mở cánh cửa vào thế giới của `src/modules/auth`.

---

### 🔑 PHẦN 2: LUỒNG ĐĂNG NHẬP - Mật khẩu, Hashing và JWT (5 phút)

**Host:** Chức năng cơ bản nhất: đăng nhập bằng username và password. Hãy xem `src/modules/auth/handler.rs`. Chúng ta sẽ thấy một function `login`.

```rust
// trong src/modules/auth/handler.rs
pub async fn login(
    State(container): State<Arc<ServiceContainer>>,
    Json(payload): Json<LoginRequest>,
) -> Result<Json<TokenResponse>, AppError> {
    // ...
    let token_response = container.auth_service.login(payload).await?;
    Ok(Json(token_response))
}
```

**Điểm đáng chú ý:**
1.  **Extractor Pattern của Axum:** `State(...)` để lấy service container, `Json(...)` để parse request body. Cực kỳ clean!
2.  **Type Safety:** `LoginRequest` và `TokenResponse` là các struct được định nghĩa chặt chẽ trong `src/modules/auth/models.rs`. Không có chuyện gửi thiếu field.

**Host:** Handler gọi vào `auth_service.login()`. Đây là nơi magic xảy ra. Bên trong `src/modules/auth/service.rs`, logic sẽ là:
1.  Tìm user trong database bằng email.
2.  Nếu user tồn tại, so sánh mật khẩu.

**Nhưng khoan!** Chúng ta không bao giờ lưu mật khẩu dạng plain text. Nhìn vào `src/modules/user/service.rs` (hoặc một module hashing riêng), bạn sẽ thấy việc sử dụng một crate như `bcrypt` hoặc `argon2`.

```rust
// Logic hashing (giả định)
bcrypt::verify(provided_password, user.password_hash)?;
```

**Host:** Sau khi xác thực thành công, service sẽ tạo ra "chìa khóa": **JWT (JSON Web Token)**. Codebase này có một `jwt.rs` trong `src/modules/auth/utils`.

```rust
// trong src/modules/auth/utils/jwt.rs (giả định)
pub fn create_jwt(user_id: Uuid, secret: &str) -> Result<String, AppError> {
    let claims = Claims {
        sub: user_id.to_string(),
        exp: (Utc::now() + Duration::days(7)).timestamp(),
        // ... other claims
    };
    encode(&Header::default(), &claims, &EncodingKey::from_secret(secret.as_ref()))
}
```
**Bài học:**
- **Tách biệt vai trò:** Handler chỉ parse request và gọi service. Service chứa business logic.
- **Bảo mật là trên hết:** Dùng thư viện hashing chuyên dụng.
- **JWT là stateless:** Server không cần lưu trữ session. Token chứa tất cả thông tin cần thiết để xác thực.

---

### 🤝 PHẦN 3: "ĐĂNG NHẬP VỚI GOOGLE" - Vũ điệu OAuth2 (5 phút)

**Host:** Auth hiện đại không thể thiếu OAuth2. Codebase này xử lý nó trong `src/modules/auth/oauth_providers.rs`.

**Luồng OAuth2 rất phức tạp, nhưng có thể tóm tắt:**
1.  **User click "Login with Google"**: Backend redirect người dùng đến trang đăng nhập của Google với `client_id` và `redirect_uri`.
2.  **User đồng ý**: Google redirect người dùng trở lại `redirect_uri` của chúng ta, kèm theo một `authorization_code`.
3.  **Backend nhận code**: Handler `google_callback` được kích hoạt. Nó lấy `code` và gửi một request từ server-tới-server đến Google để đổi lấy `access_token`.
4.  **Lấy thông tin user**: Dùng `access_token` vừa nhận, backend gọi API của Google để lấy thông tin người dùng (email, tên, avatar).
5.  **Login hoặc Register**:
    - Nếu email đã tồn tại trong DB -> đăng nhập user đó.
    - Nếu chưa -> tạo một user mới và đăng nhập.
6.  **Trả về JWT**: Cuối cùng, backend trả về JWT cho client, giống hệt luồng đăng nhập bằng mật khẩu.

**Trong Rust, điều này được hỗ trợ bởi crate `oauth2`:**
```rust
// trong src/modules/auth/service.rs (logic OAuth)
let token_result = self.oauth_client
    .exchange_code(AuthorizationCode::new(code))
    .request_async(async_http_client)
    .await?;
```

**Challenge cho beginner:**
- **Quản lý State:** OAuth2 yêu cầu một `state` parameter để chống CSRF. Bạn phải tạo, lưu (có thể trong cookie hoặc Redis), và xác thực nó.
- **Async HTTP Calls:** Toàn bộ luồng đều là các cuộc gọi mạng bất đồng bộ.
- **Error Handling:** Bất kỳ bước nào cũng có thể thất bại. Google có thể down, code có thể hết hạn. `Result<T, E>` lại tỏa sáng.

---

### 🛡️ PHẦN 4: MIDDLEWARE - Người Gác Cổng của API (6 phút)

**Host:** Có JWT rồi, nhưng làm sao để bảo vệ các route? Đây là lúc `src/routes/middleware/auth.rs` vào cuộc.

**Axum middleware** là một function nhận vào một request và quyết định xem có cho nó đi tiếp hay không.

```rust
// trong src/routes/router.rs
let api_routes = Router::new()
    .route("/profile", get(user_handler::get_profile))
    .route_layer(middleware::from_fn(auth_middleware)); // Áp dụng middleware

// trong src/routes/middleware/auth.rs
pub async fn auth_middleware<B>(
    mut req: Request<B>,
    next: Next<B>,
) -> Result<Response, AppError> {
    let auth_header = req.headers()
        .get(header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let auth_header = if let Some(h) = auth_header { h } else { return Err(...) };
    
    if !auth_header.starts_with("Bearer ") { return Err(...) };
    let token = &auth_header[7..];

    // Validate token và decode claims
    let claims = decode_jwt(token, &app_state.config.jwt_secret)?;

    // Gắn user_id vào request extension để các handler sau có thể dùng
    req.extensions_mut().insert(claims.sub);

    Ok(next.run(req).await)
}
```

**Đây là một pattern cực kỳ mạnh mẽ:**
1.  **Extract Token:** Lấy token từ header `Authorization: Bearer ...`.
2.  **Validate Token:** Dùng `jsonwebtoken::decode` với secret key để kiểm tra chữ ký và hạn sử dụng.
3.  **Enrich Request:** Nếu token hợp lệ, thông tin user (ví dụ `user_id` từ claim `sub`) được "gắn" vào request thông qua `extensions_mut()`.
4.  **Pass to Next Handler:** `next.run(req).await` chuyển request đến handler thực sự.

**Lợi ích:** Logic xác thực được đóng gói ở một nơi duy nhất. Handler `get_profile` không cần biết gì về JWT, nó chỉ cần lấy `user_id` từ request extension.

---

### 📜 PHẦN 5: AUTHORIZATION - Anh Được Phép Làm Gì? (5 phút)

**Host:** Xác thực (Authentication) trả lời câu hỏi "Anh là ai?". Phân quyền (Authorization) trả lời câu hỏi "Anh được làm gì?".

Đây là lúc các module `src/modules/role/` và `src/modules/permission/` phát huy tác dụng. Cấu trúc thường là:
- Một `User` có nhiều `Role` (e.g., "Admin", "Editor", "Viewer").
- Một `Role` có nhiều `Permission` (e.g., "post:create", "post:delete", "user:read").

**Làm sao để kiểm tra quyền?**
Có hai cách tiếp cận chính:

1.  **Trong Service Logic:**
    ```rust
    // trong post_service.rs
    pub async fn delete_post(&self, user_id: Uuid, post_id: Uuid) -> Result<(), AppError> {
        let user_permissions = self.permission_repo.get_for_user(user_id).await?;
        if !user_permissions.contains("post:delete") {
            return Err(AppError::Forbidden("Missing permission".to_string()));
        }
        // ... logic xóa post
    }
    ```

2.  **Trong một Middleware chuyên dụng (cao cấp hơn):**
    - Middleware có thể nhận vào permission cần thiết: `permission_middleware("post:delete")`.
    - Nó sẽ load quyền của user (có thể từ cache Redis để tăng tốc) và so sánh.

**Host:** Codebase này có vẻ đã đặt nền móng cho RBAC. Việc có các module `role` và `permission` riêng biệt cho thấy một thiết kế có chủ đích để quản lý phân quyền một cách chi tiết, thay vì chỉ hardcode `if user.is_admin`.

**Challenge:**
- **Performance:** Load quyền từ database cho mỗi request có thể chậm. Đây là lý do tại sao caching (với Redis, như chúng ta thấy trong `src/modules/redis`) là cực kỳ quan trọng.
- **Data Modeling:** Thiết kế mối quan hệ giữa User, Role, và Permission trong database cần phải cẩn thận.

---

### 🎯 PHẦN 6: LESSONS LEARNED (3 phút)

**Host:** Xây dựng Auth/Authz trong Rust dạy chúng ta nhiều điều:
1.  **Tận dụng Hệ sinh thái:** Đừng tự viết JWT parser hay password hasher. Dùng các crate đã được kiểm chứng như `jsonwebtoken`, `bcrypt`, `oauth2`.
2.  **Type System là Đồng minh của Bảo mật:** Dùng `Uuid` cho user ID, dùng `enum` cho Roles/Permissions giúp bạn tránh được các lỗi logic nguy hiểm.
3.  **Middleware là Trái tim của Xử lý Request:** Pattern `Extractor` và `Middleware` của Axum giúp viết code bảo mật một cách clean và composable.
4.  **Stateless là Vua:** JWT giúp hệ thống dễ dàng scale theo chiều ngang.
5.  **Phân quyền cần được thiết kế từ đầu:** Việc tách riêng các module `role` và `permission` cho thấy tầm nhìn xa của developer.

---

### 🚀 PHẦN 7: NEXT STEPS - Hành trình tiếp theo (2 phút)

**Host:** Sau khi user đăng ký, chúng ta muốn làm gì? Gửi một email chào mừng! Khi họ quên mật khẩu? Gửi email reset!

Hệ thống auth đã hoàn chỉnh, nhưng nó cần giao tiếp với thế giới bên ngoài. Module `src/modules/email/` chính là bước tiếp theo.

**Tập tiếp theo** của "Rust Journey" sẽ khám phá: **"Kiến trúc hướng sự kiện và Email Service"**. Chúng ta sẽ xem làm thế nào để khi một user được tạo, một sự kiện `UserCreated` được bắn ra, và một service khác lắng nghe để gửi email một cách bất đồng bộ, mà không làm chậm trễ request đăng ký của người dùng.

---

### 🎤 OUTRO (30 giây)

**Host:** Cảm ơn các bạn đã lắng nghe tập 2 của "Rust Journey". Từ việc hash mật khẩu đến vũ điệu phức tạp của OAuth2 và những người gác cổng middleware, chúng ta đã thấy cách Rust cung cấp các công cụ mạnh mẽ và an toàn để xây dựng một hệ thống xác thực vững chắc.

Hãy subscribe và để lại câu hỏi. Chúng ta sẽ gặp lại trong tập 3 để khám phá thế giới của event-driven architecture.

Happy coding!

**[Nhạc outro]**

---

### 📝 SHOW NOTES

**Các chủ đề chính được đề cập:**
- Luồng đăng nhập với JWT.
- Hashing mật khẩu với `bcrypt`.
- Tích hợp OAuth2 với Google.
- Axum middleware để bảo vệ route.
- Request enrichment với extensions.
- Giới thiệu về Role-Based Access Control (RBAC).

**Các file và module được phân tích:**
- `src/modules/auth/`
- `src/modules/auth/handler.rs`
- `src/modules/auth/service.rs`
- `src/modules/auth/models.rs`
- `src/modules/auth/utils/jwt.rs` (giả định)
- `src/modules/auth/oauth_providers.rs`
- `src/routes/middleware/auth.rs`
- `src/modules/role/` & `src/modules/permission/`

**Next episode preview:** Event-Driven Architecture và Email Service trong Rust.
