services:
  platform-rust:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: "1"
        DATABASE_URL: "*****************************************************************/platform_rust"
    image: platform-rust-debian
    container_name: platform-rust-app
    ports:
      - "8386:8386"
    env_file:
      - .env
    environment:
      # Override specific values for Docker environment
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8386
    volumes:
      - app_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8386/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  app_data:
    driver: local 