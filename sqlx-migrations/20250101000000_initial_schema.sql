-- Initial Platform Schema Migration for SQLx
-- Combines: Complete platform schema
-- RBAC (Role-Based Access Control) + OAuth Support + Laptop Management

-- ===== PLATFORM SETUP =====

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Platform Schema
CREATE SCHEMA IF NOT EXISTS platform;

-- ===== CORE TABLES =====

-- Users table with OAuth support
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    fullname VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255), -- Nullable for OAuth users
    email VARCHAR(100) UNIQUE NOT NULL,
    otp VARCHAR(10),
    otp_expires_at TIMESTAMPTZ,
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    email_verified_at TIMESTAMPTZ,
    oauth_provider VARCHAR(20), -- 'google', 'github', etc.
    oauth_provider_id VARCHAR(100), -- Provider-specific user ID
    oauth_avatar_url VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_login_at TIMESTAMPTZ,
    permission_version INTEGER NOT NULL DEFAULT 1, -- Added from permission version migration
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Permissions table
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User-Role junction table (many-to-many)
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),
    assigned_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Prevent duplicate assignments
    UNIQUE(user_id, role_id)
);

-- Role-Permission junction table (many-to-many)
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Prevent duplicate permissions per role
    UNIQUE(role_id, permission_id)
);

-- ===== EXP SYSTEM =====

-- User levels table for progression system
CREATE TABLE user_levels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    level INTEGER NOT NULL DEFAULT 1,
    experience BIGINT NOT NULL DEFAULT 0,
    total_experience BIGINT NOT NULL DEFAULT 0,
    current_title_id UUID, -- Will reference titles table
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- One level record per user
    UNIQUE(user_id)
);

-- Experience history for tracking exp gains
CREATE TABLE user_experience_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    exp_gained INTEGER NOT NULL,
    source VARCHAR(50) NOT NULL, -- 'laptop_view', 'daily_login', etc.
    source_id UUID, -- Reference to the source entity (laptop_id, etc.)
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== TITLE SYSTEM =====

-- Titles that users can unlock
CREATE TABLE titles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    level_required INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User unlocked titles (many-to-many)
CREATE TABLE user_unlocked_titles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title_id UUID NOT NULL REFERENCES titles(id) ON DELETE CASCADE,
    unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Prevent duplicate unlocks
    UNIQUE(user_id, title_id)
);

-- Add foreign key for current title in user_levels
ALTER TABLE user_levels ADD CONSTRAINT fk_user_levels_current_title 
    FOREIGN KEY (current_title_id) REFERENCES titles(id);

-- ===== ENUM TYPES FOR LAPTOP SYSTEM =====

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'screen_resolution_enum') THEN
        DROP TYPE screen_resolution_enum CASCADE;
    END IF;
END$$;
CREATE TYPE screen_resolution_enum AS ENUM ('FHD', '2K', '2.5K', '3K', '3.5K', '4K');

-- Create laptop status enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'laptop_status_enum') THEN
        DROP TYPE laptop_status_enum CASCADE;
    END IF;
END$$;
CREATE TYPE laptop_status_enum AS ENUM ('draft', 'published', 'archived');

-- Create RAM type enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ram_type_enum') THEN
        DROP TYPE ram_type_enum CASCADE;
    END IF;
END$$;
CREATE TYPE ram_type_enum AS ENUM ('DDR3', 'DDR4', 'DDR5', 'LPDDR4', 'LPDDR5');

-- Create market region enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'market_region_enum') THEN
        DROP TYPE market_region_enum CASCADE;
    END IF;
END$$;
CREATE TYPE market_region_enum AS ENUM ('Global', 'US', 'EU', 'Asia', 'Vietnam');

-- Create currency enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'currency_enum') THEN
        DROP TYPE currency_enum CASCADE;
    END IF;
END$$;
CREATE TYPE currency_enum AS ENUM ('USD', 'EUR', 'VND');

-- Create price region enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'price_region_enum') THEN
        DROP TYPE price_region_enum CASCADE;
    END IF;
END$$;
CREATE TYPE price_region_enum AS ENUM ('Global', 'US', 'EU', 'Asia', 'Vietnam');

-- Create category type enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'category_type_enum') THEN
        DROP TYPE category_type_enum CASCADE;
    END IF;
END$$;
CREATE TYPE category_type_enum AS ENUM ('laptop', 'accessory', 'software');

-- Create experience transaction type enum
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'experience_transaction_type_enum') THEN
        DROP TYPE experience_transaction_type_enum CASCADE;
    END IF;
END$$;
CREATE TYPE experience_transaction_type_enum AS ENUM ('earned', 'spent', 'bonus', 'penalty');

-- ===== GENERIC CATEGORIES TABLE =====

CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(60) NOT NULL,
    description TEXT,
    category_type category_type_enum NOT NULL, -- 'laptop', 'accessory', 'software'
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Unique constraints per category type (allows same name/slug across different types)
    CONSTRAINT unique_name_per_type UNIQUE(name, category_type),
    CONSTRAINT unique_slug_per_type UNIQUE(slug, category_type)
);

-- ===== LAPTOPS TABLE =====

CREATE TABLE laptops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES categories(id),
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    slug VARCHAR(250) UNIQUE NOT NULL,
    sku VARCHAR(50) UNIQUE,
    market_region market_region_enum NOT NULL DEFAULT 'Global',
    release_date DATE,
    description TEXT,
    image_urls TEXT[],
    status laptop_status_enum NOT NULL DEFAULT 'draft', -- draft, published, archived
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    view_count BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== SPECIFICATIONS TABLE =====

CREATE TABLE specifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,

    -- CPU
    cpu_brand VARCHAR(20) NOT NULL,
    cpu_model VARCHAR(100) NOT NULL,

    -- RAM với khả năng mở rộng
    ram_size INTEGER NOT NULL, -- GB
    ram_type ram_type_enum, -- DDR4, DDR5
    ram_slots_total INTEGER, -- Tổng số khe RAM
    ram_slots_available INTEGER, -- Số khe RAM còn trống
    ram_max_capacity INTEGER, -- Dung lượng RAM tối đa có thể nâng cấp
    ram_soldered BOOLEAN NOT NULL DEFAULT FALSE, -- RAM có bị hàn không

    -- Storage với khả năng mở rộng
    storage_type VARCHAR(20) NOT NULL, -- SSD, HDD, Hybrid
    storage_capacity INTEGER NOT NULL, -- GB
    storage_slots_total INTEGER, -- Tổng số khe storage (M.2, SATA)
    storage_slots_available INTEGER, -- Số khe storage còn trống
    storage_max_capacity INTEGER, -- Dung lượng storage tối đa

    -- GPU
    gpu_type VARCHAR(20) NOT NULL, -- Integrated, Dedicated
    gpu_model VARCHAR(100), -- NVIDIA RTX 4060, AMD Radeon 780M, etc.

    -- Display
    screen_size NUMERIC(4,1) NOT NULL, -- 13.3, 14.0, 15.6, 16.0, 17.3
    screen_resolution screen_resolution_enum NOT NULL,
    refresh_rate INTEGER NOT NULL, -- 60, 90, 120, 144, 165, 240 Hz

    -- Physical
    weight NUMERIC(5,2), -- kg

    -- Software
    operating_system VARCHAR(50), -- Windows 11, Ubuntu, macOS

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== PRICES TABLE =====

CREATE TABLE prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,
    min_price NUMERIC(10,2) NOT NULL,
    max_price NUMERIC(10,2) NOT NULL,
    currency currency_enum NOT NULL DEFAULT 'USD',
    source VARCHAR(100), -- 'amazon', 'bestbuy', 'local_store', etc.
    region price_region_enum NOT NULL DEFAULT 'Global',
    effective_date DATE NOT NULL,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== LAPTOP EMBEDDINGS TABLE =====

CREATE TABLE laptop_embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,
    embedding FLOAT4[] NOT NULL, -- Vector embedding for similarity search
    model_version VARCHAR(50) NOT NULL, -- 'text-embedding-ada-002', 'all-MiniLM-L6-v2'
    embedding_type VARCHAR(50) NOT NULL, -- 'description', 'specifications', 'combined'
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'inactive', 'processing'
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== INDEXES FOR PERFORMANCE =====

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_oauth_provider_id ON users(oauth_provider, oauth_provider_id);
CREATE INDEX idx_users_permission_version ON users(permission_version);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Roles indexes
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_is_active ON roles(is_active);

-- Permissions indexes
CREATE INDEX idx_permissions_name ON permissions(name);

-- User-Role junction indexes
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);

-- Role-Permission junction indexes
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

-- User levels indexes
CREATE INDEX idx_user_levels_user_id ON user_levels(user_id);
CREATE INDEX idx_user_levels_level ON user_levels(level);

-- Experience history indexes
CREATE INDEX idx_user_exp_history_user_id ON user_experience_history(user_id);
CREATE INDEX idx_user_exp_history_source ON user_experience_history(source);
CREATE INDEX idx_user_exp_history_created_at ON user_experience_history(created_at);

-- Titles indexes
CREATE INDEX idx_titles_name ON titles(name);
CREATE INDEX idx_titles_level_required ON titles(level_required);

-- User unlocked titles indexes
CREATE INDEX idx_user_unlocked_titles_user_id ON user_unlocked_titles(user_id);
CREATE INDEX idx_user_unlocked_titles_title_id ON user_unlocked_titles(title_id);

-- Categories indexes
CREATE INDEX idx_categories_type ON categories(category_type);
CREATE INDEX idx_categories_active_type ON categories(is_active, category_type);
CREATE INDEX idx_categories_slug_type ON categories(slug, category_type);
CREATE INDEX idx_categories_name_type ON categories(name, category_type);
CREATE INDEX idx_categories_created_by ON categories(created_by);

-- Laptops indexes
CREATE INDEX idx_laptops_slug ON laptops(slug);
CREATE INDEX idx_laptops_category ON laptops(category_id);
CREATE INDEX idx_laptops_brand ON laptops(brand);
CREATE INDEX idx_laptops_status ON laptops(status);
CREATE INDEX idx_laptops_market_region ON laptops(market_region);
CREATE INDEX idx_laptops_created_by ON laptops(created_by);
CREATE INDEX idx_laptops_featured ON laptops(is_featured);
CREATE INDEX idx_laptops_view_count ON laptops(view_count);

-- Specifications indexes
CREATE INDEX idx_specs_laptop ON specifications(laptop_id);
CREATE INDEX idx_specs_ram_size ON specifications(ram_size);
CREATE INDEX idx_specs_storage_capacity ON specifications(storage_capacity);
CREATE INDEX idx_specs_screen_size ON specifications(screen_size);
CREATE INDEX idx_specs_screen_resolution ON specifications(screen_resolution);
CREATE INDEX idx_specs_cpu_brand ON specifications(cpu_brand);
CREATE INDEX idx_specs_gpu_type ON specifications(gpu_type);
CREATE INDEX idx_specs_storage_type ON specifications(storage_type);

-- Prices indexes
CREATE INDEX idx_prices_laptop_current ON prices(laptop_id, is_current);
CREATE INDEX idx_prices_range ON prices(min_price, max_price);
CREATE INDEX idx_prices_region ON prices(region);
CREATE INDEX idx_prices_created_by ON prices(created_by);
CREATE INDEX idx_prices_effective_date ON prices(effective_date);

-- Laptop embeddings indexes
CREATE INDEX idx_laptop_embeddings_laptop_id ON laptop_embeddings(laptop_id);
CREATE INDEX idx_laptop_embeddings_status ON laptop_embeddings(status);
CREATE INDEX idx_laptop_embeddings_type ON laptop_embeddings(embedding_type);

-- ===== TRIGGERS FOR UPDATED_AT =====

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_levels_updated_at BEFORE UPDATE ON user_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_titles_updated_at BEFORE UPDATE ON titles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_laptops_updated_at BEFORE UPDATE ON laptops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_specifications_updated_at BEFORE UPDATE ON specifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prices_updated_at BEFORE UPDATE ON prices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_laptop_embeddings_updated_at BEFORE UPDATE ON laptop_embeddings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 