# 🚀 Kế Hoạch Triển Khai GraphQL (Phiên bản rút gọn)

## 📋 Tổng Quan

Tài liệu này mô tả kế hoạch triển khai hỗ trợ GraphQL cho *Platform-Rust API* với phạm vi **chỉ** dành cho hai thực thể công khai:

* **Category** – Danh mục sản phẩm
* **Laptop** – Thông tin laptop (public)

Các API REST hiện tại tiếp tục được duy trì. Mục tiêu là cung cấp endpoint GraphQL song song để client có thể truy vấn linh hoạt hơn mà không ảnh hưởng khả năng tương thích ngược.

---

## 🎯 Mục Tiêu & Tiêu Chí Thành Công

### Mục Tiêu Chính
1. <PERSON><PERSON> cấp endpoint GraphQL tại đường dẫn `/graphql`.
2. Cho phép truy vấn danh sách `Category` và `Laptop` với các bộ lọc cơ bản.
3. <PERSON><PERSON> trì cơ chế xác thực/uỷ quyền hiện có (chỉ cần token public, không yêu cầu quyền cao).
4. Tái sử dụng toàn bộ business logic và service hiện có cho Category & Laptop.
5. Hiệu năng tương đương hoặc tốt hơn REST cho cùng dữ liệu.

### Tiêu Chí Thành Công
- Truy vấn GraphQL trả về dữ liệu chính xác, thời gian đáp ứng < **100ms** cho 95% request.
- Tài liệu schema tự động được sinh ra và dễ hiểu.
- Viết ví dụ tích hợp (React Apollo) cho client.

---

## 🏗️ Tích Hợp Kiến Trúc

````rust
// Cấu trúc REST giữ nguyên
/api/categories  -> REST endpoints
/api/laptops     -> REST endpoints

// Thêm GraphQL
/graphql            -> GraphQL endpoint
/graphql/playground -> GraphQL Playground (chỉ DEV)
````

### GraphQL Context
```rust
pub struct GraphQLContext {
    pub laptop_service: Arc<dyn LaptopManagementServiceTrait>,
    pub category_service: Arc<dyn CategoryServiceTrait>,
}
```

---

## 📦 Phụ Thuộc (Crate)
```toml
async-graphql        = "7.0"
async-graphql-axum   = "7.0"
```

---

## 🗂️ Cấu Trúc Module
```
src/modules/graphql/
├── mod.rs          # export
├── schema.rs       # Root schema
├── context.rs      # Khởi tạo context
├── types/          # Định nghĩa GraphQL types
│   ├── laptop.rs   # Laptop type & resolver
│   ├── category.rs # Category type & resolver
│   └── mod.rs
└── query.rs        # Root query
```

---

## 🚀 Lộ Trình Triển Khai

### Giai Đoạn 1: Nền Tảng (Tuần 1)
* Thêm dependencies vào `Cargo.toml`.
* Tạo cấu trúc thư mục như trên.
* Thiết lập endpoint `/graphql` & playground.

#### Deliverables
- Endpoint GraphQL hoạt động.
- Playground truy cập được ở môi trường dev.

### Giai Đoạn 2: Định Nghĩa Schema (Tuần 2-3)
* Tạo `Category`, `Laptop` GraphQL types.
* Tạo `Query` root với các field:
  * `categories(page, size)` – trả về danh sách phân trang.
  * `laptops(filter, page, size)` – lọc theo brand, price range, v.v.
  * `laptop(id)` – chi tiết laptop.

#### Deliverables
- Schema hoàn chỉnh, có mô tả field.
- Truy vấn basic hoạt động với dữ liệu thực.

### Giai Đoạn 3: Tối Ưu & Tài Liệu (Tuần 4)
* Thêm DataLoader simple (tránh N+1 với category → laptop).
* Bổ sung cache Redis nếu cần.
* Viết tài liệu README + ví dụ React.

#### Deliverables
- Thời gian phản hồi < 100ms (95p).
- Tài liệu và ví dụ client.

---

## 🧪 Chiến Lược Kiểm Thử

* **Unit Test**: kiểm thử resolver cho từng service.
* **Integration Test**: test endpoint `/graphql` với filter phổ biến.

Ví dụ:
```rust
#[tokio::test]
async fn test_laptop_query() {
    let schema = create_test_schema().await;
    let query = "{ laptop(id: \"1\") { fullName brand } }";
    let res = schema.execute(query).await;
    assert!(res.errors.is_empty());
}
```

---

## 📚 Tài Liệu
- Schema GraphQL tự sinh qua introspection (`/graphql?sdl`).
- Hướng dẫn client (Apollo, urql).

---

## 🚦 Rủi Ro & Giảm Thiểu
| Rủi ro | Giảm thiểu |
|--------|------------|
| N+1 query | Sử dụng DataLoader |
| Truy vấn quá lớn | Giới hạn độ phức tạp truy vấn |
| Hiệu năng | Caching + index DB |

---

## 📈 Chỉ Số Thành Công
- Tỉ lệ lỗi request < **1%**
- Cache hit rate > **80%**
- Adoption client >= **2** client chính trong 3 tháng

---

## 📞 Liên Hệ
- Tech Lead: [Tên bạn]
- PM: [Tên PM]

**Cập nhật lần cuối**: Tháng 1 2025
