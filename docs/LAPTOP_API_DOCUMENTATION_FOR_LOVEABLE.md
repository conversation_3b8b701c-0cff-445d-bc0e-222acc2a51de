  # 🚀 Laptop API Documentation for Loveable UI Design

  > **Thiết kế cho GenZ** - API documentation để hỗ trợ Loveable tạo UI hiện đại, thân thiện với thế hệ GenZ

  ## 📋 Tổng quan

  API Laptop cung cấp đầy đủ các endpoints để quản lý thông tin laptop, bao gồm:
  - **Public API**: Dành cho người dùng cuối (không cần authentication)
  - **Private API**: Dành cho admin/quản lý (cần authentication + permissions)

  ### 🎯 Base URL
  ```
  https://your-api-domain.com/api
  ```

  ### 🔑 Authentication
  - **Public API**: Không cần authentication
  - **Private API**: Cần header `Authorization: Bearer <token>` và `X-API-Type: private`

  ---

  ## 🌟 Public API Endpoints (Dành cho GenZ Users)

  ### 1. 📱 Lấy danh sách laptop (Public)

  **Endpoint**: `GET /laptops`

  **Headers**:
  ```
  X-API-Type: public (hoặc không cần header)
  ```

  **Query Parameters**:
  ```typescript
  interface LaptopFilters {
    page?: number;           // Trang hiện tại (default: 1)
    per_page?: number;       // Số item/trang (default: 25, max: 100)
    search?: string;         // Tìm kiếm theo tên, brand
    brand?: string;          // Lọc theo thương hiệu (Apple, Dell, HP, etc.)
    category_id?: string;    // Lọc theo category UUID
    is_featured?: boolean;   // Chỉ lấy laptop nổi bật
    market_region?: string;  // Lọc theo khu vực (Global, US, EU, Asia, Vietnam)
  }
  ```

  **Response Example**:
  ```json
  {
    "success": true,
    "code": "LAPTOP01",
    "message": "Laptops retrieved successfully",
    "path": "/api/laptops",
    "data": {
      "laptops": [
        {
          "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
          "brand": "Apple",
          "model": "MacBook Pro",
          "full_name": "Apple MacBook Pro 14-inch M3 Pro",
          "slug": "apple-macbook-pro-14-m3-pro",
          "market_region": "Global",
          "image_urls": [
            "https://example.com/macbook-pro-1.jpg",
            "https://example.com/macbook-pro-2.jpg"
          ],
          "status": "published",
          "view_count": 1250,
          "specification": {
            "id": "spec-uuid",
            "laptop_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "cpu_brand": "Apple",
            "cpu_model": "M3 Pro",
            "ram_size": 16,
            "ram_type": "LPDDR5",
            "storage_type": "SSD",
            "storage_capacity": 512,
            "gpu_type": "Integrated",
            "gpu_model": "Apple GPU",
            "screen_size": 14.2,
            "screen_resolution": "3K",
            "refresh_rate": 120,
            "weight": 1.6,
            "operating_system": "macOS"
          },
          "current_price": {
            "id": "price-uuid",
            "laptop_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "min_price": 1999.00,
            "max_price": 2499.00,
            "currency": "USD",
            "source": "Apple Store",
            "region": "US",
            "effective_date": "2025-01-15",
            "is_current": true
          }
        }
      ],
      "meta": {
        "page": 1,
        "per_page": 25,
        "total": 150,
        "total_pages": 6,
        "has_next": true,
        "has_prev": false
      }
    }
  }
  ```

  ### 2. 🔍 Lấy chi tiết laptop theo slug (Public)

  **Endpoint**: `GET /laptops/by-slug/{slug}`

  **Example**: `GET /laptops/by-slug/apple-macbook-pro-14-m3-pro`

  **Response**: Tương tự như trên nhưng chỉ trả về 1 laptop với đầy đủ thông tin

  ### 3. 👀 Tăng lượt xem laptop

  **Endpoint**: `POST /laptops/{id}/view`

  **Body**: Không cần body

  **Response**:
  ```json
  {
    "success": true,
    "message": "View count incremented successfully"
  }
  ```

  ### 4. 💰 Lấy giá của laptop

  **Endpoint**: `GET /laptops/{laptop_id}/prices`

  **Response**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "price-uuid",
        "min_price": 1999.00,
        "max_price": 2499.00,
        "currency": "USD",
        "source": "Apple Store",
        "region": "US",
        "effective_date": "2025-01-15",
        "is_current": true
      }
    ]
  }
  ```

  ### 5. ⚙️ Lấy thông số kỹ thuật

  **Endpoint**: `GET /laptops/{laptop_id}/specifications`

  **Response**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "spec-uuid",
        "cpu_brand": "Apple",
        "cpu_model": "M3 Pro",
        "ram_size": 16,
        "ram_type": "LPDDR5",
        "storage_type": "SSD",
        "storage_capacity": 512,
        "gpu_type": "Integrated",
        "screen_size": 14.2,
        "screen_resolution": "3K",
        "refresh_rate": 120
      }
    ]
  }
  ```

  ---

  ## 🔐 Private API Endpoints (Admin/Management)

  ### 1. ➕ Tạo laptop hoàn chỉnh

  **Endpoint**: `POST /laptops/complete`

  **Headers**:
  ```
  Authorization: Bearer <token>
  X-API-Type: private
  Content-Type: application/json
  ```

  **Body Example**:
  ```json
  {
    "brand": "Apple",
    "model": "MacBook Pro",
    "full_name": "Apple MacBook Pro 14-inch M3 Pro",
    "slug": "apple-macbook-pro-14-m3-pro",
    "category_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "market_region": "Global",
    "release_date": "2025-07-14",
    "description": "Powerful laptop for professionals",
    "image_urls": ["https://example.com/image1.jpg"],
    "is_featured": true,
    "specification": {
      "cpu_brand": "Apple",
      "cpu_model": "M3 Pro",
      "ram_size": 16,
      "ram_type": "LPDDR5",
      "storage_type": "SSD",
      "storage_capacity": 512,
      "gpu_type": "Integrated",
      "gpu_model": "Apple GPU",
      "screen_size": 14.2,
      "screen_resolution": "3K",
      "refresh_rate": 120,
      "weight": 1.6,
      "operating_system": "macOS"
    },
    "price": {
      "min_price": 1999.00,
      "max_price": 2499.00,
      "currency": "USD",
      "source": "Apple Store",
      "region": "US",
      "effective_date": "2025-07-14",
      "is_current": true
    }
  }
  ```

  ### 2. ✏️ Cập nhật laptop

  **Endpoint**: `PUT /laptops/{id}`

  **Body**: Tương tự như tạo nhưng tất cả fields đều optional

  ### 3. 🗑️ Xóa laptop

  **Endpoint**: `DELETE /laptops/{id}`

  ### 4. 📢 Publish laptop

  **Endpoint**: `PUT /laptops/{id}/publish`

  ### 5. 📦 Archive laptop

  **Endpoint**: `PUT /laptops/{id}/archive`

  ### 6. ⭐ Đặt laptop nổi bật

  **Endpoint**: `PUT /laptops/{id}/featured`

  **Body**:
  ```json
  {
    "is_featured": true
  }
  ```

  ---

  ## 📊 Data Models cho UI Design

  ### 🎨 Laptop Card Component (Public View)
  ```typescript
  interface LaptopCard {
    id: string;
    brand: string;              // Hiển thị logo brand
    model: string;              // Tên model
    full_name: string;          // Tên đầy đủ (title chính)
    slug: string;               // Dùng cho routing
    image_urls: string[];       // Carousel images
    view_count: number;         // Badge popularity
    current_price?: {
      min_price: number;
      max_price: number;
      currency: string;         // USD, EUR, VND
    };
    specification?: {
      cpu_model: string;        // Chip info
      ram_size: number;         // RAM GB
      storage_capacity: number; // Storage GB
      screen_size: number;      // Inch
      screen_resolution: string; // 3K, 4K, etc.
    };
  }
  ```

  ### 🔧 Enums cho UI

  ```typescript
  // Laptop Status
  enum LaptopStatus {
    DRAFT = "draft",
    PUBLISHED = "published", 
    ARCHIVED = "archived"
  }

  // Market Regions
  enum MarketRegion {
    GLOBAL = "Global",
    US = "US",
    EU = "EU", 
    ASIA = "Asia",
    VIETNAM = "Vietnam"
  }

  // Currency
  enum Currency {
    USD = "USD",
    EUR = "EUR",
    VND = "VND"
  }

  // Screen Resolution
  enum ScreenResolution {
    FHD = "FHD",
    TWO_K = "2K",
    TWO_POINT_FIVE_K = "2.5K", 
    THREE_K = "3K",
    THREE_POINT_FIVE_K = "3.5K",
    FOUR_K = "4K"
  }

  // RAM Type
  enum RamType {
    DDR4 = "DDR4",
    DDR5 = "DDR5",
    LPDDR4 = "LPDDR4",
    LPDDR5 = "LPDDR5"
  }

  // Storage Type
  enum StorageType {
    SSD = "SSD",
    HDD = "HDD", 
    HYBRID = "Hybrid"
  }

  // GPU Type
  enum GpuType {
    INTEGRATED = "Integrated",
    DEDICATED = "Dedicated"
  }
  ```

  ---

  ## 🎯 UI/UX Recommendations cho GenZ

  ### 🌈 Visual Design
  - **Cards**: Sử dụng rounded corners, subtle shadows
  - **Colors**: Gradient backgrounds, vibrant accent colors
  - **Typography**: Modern fonts (Inter, Poppins)
  - **Icons**: Minimalist, outline style

  ### 📱 Mobile-First Components

  #### 1. Laptop Card
  ```jsx
  <LaptopCard>
    <ImageCarousel images={laptop.image_urls} />
    <BrandBadge brand={laptop.brand} />
    <Title>{laptop.full_name}</Title>
    <SpecHighlights>
      <Chip>{laptop.specification.cpu_model}</Chip>
      <Chip>{laptop.specification.ram_size}GB RAM</Chip>
      <Chip>{laptop.specification.storage_capacity}GB SSD</Chip>
    </SpecHighlights>
    <PriceRange 
      min={laptop.current_price.min_price}
      max={laptop.current_price.max_price}
      currency={laptop.current_price.currency}
    />
    <ViewCount count={laptop.view_count} />
  </LaptopCard>
  ```

  #### 2. Filter Bar
  ```jsx
  <FilterBar>
    <SearchInput placeholder="Tìm laptop yêu thích..." />
    <FilterChips>
      <BrandFilter brands={["Apple", "Dell", "HP", "Asus"]} />
      <PriceRangeFilter />
      <SpecFilter />
    </FilterChips>
  </FilterBar>
  ```

  #### 3. Sort Options
  ```jsx
  <SortOptions>
    <SortButton value="popular">🔥 Phổ biến</SortButton>
    <SortButton value="price_low">💰 Giá thấp</SortButton>
    <SortButton value="price_high">💎 Giá cao</SortButton>
    <SortButton value="newest">✨ Mới nhất</SortButton>
  </SortOptions>
  ```

  ### 🚀 Interactive Features

  #### 1. Quick View Modal
  - Swipe gallery
  - Key specs comparison
  - Price history chart
  - "Add to Wishlist" button

  #### 2. Comparison Tool
  - Drag & drop laptops
  - Side-by-side specs
  - Highlight differences
  - Share comparison

  #### 3. Wishlist & Favorites
  - Heart animation
  - Sync across devices
  - Price drop notifications

  ### 📊 Performance Metrics

  #### Loading States
  ```jsx
  <SkeletonCard>
    <SkeletonImage />
    <SkeletonText lines={2} />
    <SkeletonChips count={3} />
    <SkeletonPrice />
  </SkeletonCard>
  ```

  #### Error States
  ```jsx
  <ErrorState>
    <EmptyIllustration />
    <Title>Oops! Không tìm thấy laptop nào</Title>
    <Subtitle>Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</Subtitle>
    <RetryButton />
  </ErrorState>
  ```

  ---

  ## 🔧 Technical Implementation

  ### API Client Setup
  ```typescript
  // api/laptops.ts
  const API_BASE = 'https://your-api-domain.com/api';

  export const laptopApi = {
    // Public endpoints
    getLaptops: (filters: LaptopFilters) => 
      fetch(`${API_BASE}/laptops?${new URLSearchParams(filters)}`),
    
    getLaptopBySlug: (slug: string) =>
      fetch(`${API_BASE}/laptops/by-slug/${slug}`),
      
    incrementView: (id: string) =>
      fetch(`${API_BASE}/laptops/${id}/view`, { method: 'POST' }),
      
    // Private endpoints (with auth)
    createLaptop: (data: CreateLaptopRequest, token: string) =>
      fetch(`${API_BASE}/laptops/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-API-Type': 'private',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
  };
  ```

  ### State Management (Zustand)
  ```typescript
  interface LaptopStore {
    laptops: LaptopCard[];
    filters: LaptopFilters;
    loading: boolean;
    error: string | null;
    
    // Actions
    fetchLaptops: () => Promise<void>;
    setFilters: (filters: Partial<LaptopFilters>) => void;
    toggleFavorite: (laptopId: string) => void;
  }
  ```

  ### Caching Strategy
  ```typescript
  // React Query setup
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  });

  // Hook usage
  const useLaptops = (filters: LaptopFilters) => {
    return useQuery({
      queryKey: ['laptops', filters],
      queryFn: () => laptopApi.getLaptops(filters),
      keepPreviousData: true,
    });
  };
  ```

  ---

  ## 🎉 Kết luận

  API này được thiết kế để hỗ trợ tối đa cho việc xây dựng UI hiện đại, thân thiện với GenZ:

  ✅ **Responsive**: Hoạt động tốt trên mọi thiết bị  
  ✅ **Fast**: Pagination, caching, lazy loading  
  ✅ **Interactive**: Real-time updates, smooth animations  
  ✅ **Accessible**: Screen reader friendly, keyboard navigation  
  ✅ **Modern**: Latest web standards, progressive enhancement  

  Chúc Loveable team tạo ra những UI tuyệt vời! 🚀✨