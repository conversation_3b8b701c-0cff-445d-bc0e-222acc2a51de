# 📊 GRAFANA INTEGRATION PLAN - PLATFORM RUST

## 🎯 **<PERSON><PERSON>c tiêu chính**
- **Application Performance Monitoring (APM)**
- **Infrastructure Monitoring** 
- **Business Metrics Tracking**
- **Real-time Alerting**
- **Service Health Monitoring**

---

## **📋 TỔNG QUAN HỆ THỐNG HIỆN TẠI**

### **Tech Stack:**
- **Backend:** Rust Axum Framework (v0.8)
- **Database:** PostgreSQL với SQLx (v0.8)
- **Cache:** Redis với bb8-redis connection pooling
- **Authentication:** JWT với RBAC system + Google OAuth
- **Container:** Docker với docker-compose
- **Logging:** tracing-subscriber với structured logging
- **Documentation:** OpenAPI/Swagger với utoipa
- **Email:** SMTP với lettre + handlebars templates
- **Image Storage:** Cloudinary integration
- **Rate Limiting:** In-memory rate limiter với cleanup

### **Architecture hiện tại:**
```
Client → Axum Router → Middleware Stack → Handlers → Services → Repository → PostgreSQL/Redis
                   ↓
            [Auth, CORS, Compression, Rate Limit, Request Tracing, Security Headers]
```

### **Modules hiện có:**
- **auth**: JWT + OAuth + RBAC
- **user**: User management với roles/permissions
- **category**: Category management
- **laptop**: Product management với SKU generation
- **image**: Cloudinary integration
- **email**: SMTP service với event bus
- **redis**: Caching layer
- **permission/role**: RBAC system
- **progression**: XP/Title system

---

## **🚀 IMPLEMENTATION PHASES**

## **PHASE 1: METRICS COLLECTION INFRASTRUCTURE**

### **1.1 Dependencies Setup**
```toml
# Thêm vào Cargo.toml (cập nhật từ codebase hiện tại)
metrics = "0.23"
metrics-exporter-prometheus = "0.15" 
axum-prometheus = "0.7"
# tower-http đã có sẵn với features ["compression-full", "cors", "fs"]
# Cần thêm feature "metrics"
tower-http = { version = "0.6", features = ["compression-full", "cors", "fs", "metrics"] }
```

### **1.1.1 Middleware hiện có cần tích hợp:**
- ✅ **request_tracing**: Đã có correlation ID và structured logging
- ✅ **rate_limit**: Đã có in-memory rate limiter với metrics potential
- ✅ **compression**: Đã có compression middleware
- ✅ **auth**: JWT + RBAC middleware
- ✅ **security_headers**: Security headers middleware

### **1.2 Metrics Middleware**
**Tạo file:** `src/routes/middleware/metrics.rs`

```rust
use axum::{extract::MatchedPath, http::Request, middleware::Next, response::Response};
use metrics::{counter, histogram, gauge};
use std::time::Instant;
use crate::routes::middleware::request_tracing::RequestInfo;

pub async fn metrics_middleware<B>(
    MatchedPath(path): MatchedPath,
    request: Request<B>,
    next: Next<B>,
) -> Response {
    let start = Instant::now();
    let method = request.method().clone();
    
    // Extract correlation ID từ existing RequestInfo middleware
    let correlation_id = request.extensions()
        .get::<RequestInfo>()
        .map(|info| info.correlation_id.clone());
    
    let response = next.run(request).await;
    
    let duration = start.elapsed();
    let status = response.status().as_u16();
    
    // HTTP Request metrics với enhanced labels
    counter!("http_requests_total", 
        "method" => method.to_string(), 
        "path" => path.clone(), 
        "status" => status.to_string(),
        "status_class" => format!("{}xx", status / 100)
    ).increment(1);
    
    histogram!("http_request_duration_seconds", 
        "method" => method.to_string(), 
        "path" => path.clone(),
        "status_class" => format!("{}xx", status / 100)
    ).record(duration.as_secs_f64());
    
    // Track slow requests (>1s) với correlation ID logging
    if duration.as_secs() > 1 {
        counter!("http_slow_requests_total",
            "method" => method.to_string(),
            "path" => path.clone()
        ).increment(1);
        
        if let Some(corr_id) = correlation_id {
            tracing::warn!(
                correlation_id = %corr_id,
                duration_ms = duration.as_millis(),
                method = %method,
                path = %path,
                "Slow request detected"
            );
        }
    }
    
    response
}

// Business metrics helpers tích hợp với modules hiện có
pub fn track_user_action(action: &str, user_id: Option<&str>) {
    counter!("user_actions_total", "action" => action.to_string()).increment(1);
    
    if let Some(uid) = user_id {
        counter!("user_specific_actions_total", 
            "action" => action.to_string(),
            "user_id" => uid.to_string()
        ).increment(1);
    }
}

// Database metrics tích hợp với existing Database struct
pub fn track_database_operation(operation: &str, table: &str, duration: std::time::Duration) {
    histogram!("database_operation_duration_seconds",
        "operation" => operation.to_string(),
        "table" => table.to_string()
    ).record(duration.as_secs_f64());
    
    counter!("database_operations_total",
        "operation" => operation.to_string(),
        "table" => table.to_string()
    ).increment(1);
}

pub fn set_db_pool_metrics(active: u32, idle: u32, total: u32) {
    gauge!("database_pool_active_connections").set(active as f64);
    gauge!("database_pool_idle_connections").set(idle as f64);
    gauge!("database_pool_total_connections").set(total as f64);
}

// Redis/Cache metrics tích hợp với existing RedisManager
pub fn track_cache_operation(operation: &str, hit: bool) {
    counter!("cache_operations_total",
        "operation" => operation.to_string(),
        "result" => if hit { "hit" } else { "miss" }
    ).increment(1);
}

pub fn track_redis_pool_status(active: u32, idle: u32) {
    gauge!("redis_pool_active_connections").set(active as f64);
    gauge!("redis_pool_idle_connections").set(idle as f64);
}

// Authentication metrics tích hợp với existing auth module
pub fn track_auth_attempt(success: bool, method: &str, user_id: Option<&str>) {
    counter!("auth_attempts_total", 
        "success" => success.to_string(), 
        "method" => method.to_string()
    ).increment(1);
    
    if let Some(uid) = user_id {
        counter!("auth_user_attempts_total",
            "success" => success.to_string(),
            "user_id" => uid.to_string()
        ).increment(1);
    }
}

// Rate limiting metrics từ existing rate_limit middleware
pub fn track_rate_limit_hit(ip: &str, endpoint: &str) {
    counter!("rate_limit_hits_total",
        "ip" => ip.to_string(),
        "endpoint" => endpoint.to_string()
    ).increment(1);
}

// Email service metrics từ existing email module
pub fn track_email_sent(template: &str, success: bool) {
    counter!("emails_sent_total",
        "template" => template.to_string(),
        "success" => success.to_string()
    ).increment(1);
}

// Image service metrics từ existing Cloudinary integration
pub fn track_image_operation(operation: &str, success: bool, duration: std::time::Duration) {
    counter!("image_operations_total",
        "operation" => operation.to_string(),
        "success" => success.to_string()
    ).increment(1);
    
    histogram!("image_operation_duration_seconds",
        "operation" => operation.to_string()
    ).record(duration.as_secs_f64());
}

// Business metrics
pub fn record_user_registration() {
    counter!("user_registrations_total").increment(1);
}

pub fn record_permission_check(permission: &str, granted: bool) {
    counter!("permission_checks_total", 
        "permission" => permission, 
        "granted" => granted.to_string()
    ).increment(1);
}
```

### **1.3 Metrics Export Setup**
**Update src/main.rs:**

```rust
use metrics_exporter_prometheus::{Matcher, PrometheusBuilder, PrometheusHandle};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // ... existing setup ...

    // Prometheus metrics setup
    let prometheus_handle = setup_metrics();

    // ... existing router setup ...

    // Add metrics endpoint
    app = app.route("/metrics", axum::routing::get(move || async move {
        prometheus_handle.render()
    }));

    // ... rest of main ...
}

fn setup_metrics() -> PrometheusHandle {
    PrometheusBuilder::new()
        .with_http_listener(([0, 0, 0, 0], 9090))
        .install()
        .expect("Failed to install Prometheus recorder")
}
```

---

## **PHASE 2: LOGGING INFRASTRUCTURE**

### **2.1 Enhanced Logging Dependencies**
```toml
# Update Cargo.toml
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "fmt"] }
tracing-appender = "0.2"
serde_json = "1.0"
uuid = { version = "1.11", features = ["v4", "serde"] }
```

### **2.2 Enhanced Structured Logging**
**✅ Đã có sẵn trong codebase - Cần enhance:**

**Current implementation trong `src/main.rs`:**
```rust
// Hiện tại
tracing_subscriber::registry()
    .with(
        tracing_subscriber::EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| "platform_rust=debug,tower_http=debug".into()),
    )
    .with(tracing_subscriber::fmt::layer())
    .init();
```

**Enhanced version với JSON logging và file output:**
```rust
// Update src/main.rs hoặc tạo src/utils/logging.rs
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};
use tracing_appender::rolling::{RollingFileAppender, Rotation};

pub fn init_enhanced_logging() -> anyhow::Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "platform_rust=info,tower_http=info,sqlx=warn".into());

    // Console layer cho development
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(false)
        .with_ansi(true)
        .with_file(true)
        .with_line_number(true);

    // JSON file layer cho production
    let file_appender = RollingFileAppender::new(Rotation::DAILY, "logs", "platform-rust.log");
    let file_layer = tracing_subscriber::fmt::layer()
        .with_writer(file_appender)
        .json()
        .with_current_span(false)
        .with_span_list(true)
        .with_target(true);

    tracing_subscriber::registry()
        .with(env_filter)
        .with(console_layer)
        .with(file_layer)
        .init();

    Ok(())
}
```

**✅ Request tracing đã có sẵn trong `src/routes/middleware/request_tracing.rs`:**
- Correlation ID tracking
- Structured logging với RequestInfo
- Client IP tracking
- User agent extraction
- Request duration measurement

**Cần thêm metrics integration vào existing middleware:**
```rust
// Trong existing request_tracing.rs, thêm metrics calls
use crate::routes::middleware::metrics::track_user_action;

// Trong enhanced_request_tracing_middleware function
if let Some(user_id) = extract_user_id_from_request(&request) {
    track_user_action("api_request", Some(&user_id));
}
```

---

## **PHASE 3: INFRASTRUCTURE SETUP**

### **3.1 Docker Compose Setup - Tách riêng biệt**

**✅ Giữ nguyên `docker-compose.yml` hiện tại cho platform-rust**

**🆕 Tạo `docker-compose.monitoring.yml` riêng cho monitoring stack:**

```yaml
# docker-compose.monitoring.yml - Chạy trên máy monitoring riêng
version: '3.8'

services:
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alerts:/etc/prometheus/alerts
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--alertmanager.url=http://alertmanager:9093'
    restart: unless-stopped
    networks:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    restart: unless-stopped
    networks:
      - monitoring

  # Loki for log aggregation
  loki:
    image: grafana/loki:latest
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
    networks:
      - monitoring

  # Alertmanager for alert handling
  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    restart: unless-stopped
    networks:
      - monitoring

  # Node Exporter for system metrics (optional)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    networks:
      - monitoring

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  alertmanager_data:
    driver: local

networks:
  monitoring:
    driver: bridge
```

**🔧 Update `docker-compose.yml` cho platform-rust (minimal changes):**

```yaml
# docker-compose.yml - Platform Rust application
services:
  platform-rust:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: "1"
        DATABASE_URL: "*****************************************************************/platform_rust"
    image: platform-rust-debian
    container_name: platform-rust-app
    ports:
      - "8386:8386"
      # 🆕 Expose metrics endpoint cho Prometheus
      - "9090:9090"  # Metrics endpoint
    env_file:
      - .env
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8386
      # 🆕 Enable metrics
      - METRICS_ENABLED=true
      - PROMETHEUS_ENDPOINT=/metrics
    volumes:
      - app_data:/app/data
      # 🆕 Logs volume cho remote log shipping
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8386/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  app_data:
    driver: local
```

### **3.2 Configuration Files cho Monitoring Stack**

**Tạo thư mục:** `monitoring/` (trên máy monitoring)

**File:** `monitoring/prometheus.yml`
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/alerts/*.yml"

scrape_configs:
  # Platform Rust API metrics - Remote target
  - job_name: 'platform-rust'
    static_configs:
      # 🔧 Thay đổi IP này thành IP của máy chạy platform-rust
      - targets: ['<PLATFORM_RUST_SERVER_IP>:9090']
    scrape_interval: 5s
    metrics_path: /metrics
    
  # Platform Rust health check - Remote target  
  - job_name: 'platform-rust-health'
    static_configs:
      # 🔧 Thay đổi IP này thành IP của máy chạy platform-rust
      - targets: ['<PLATFORM_RUST_SERVER_IP>:8386']
    metrics_path: /health/detailed
    scrape_interval: 30s
    
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node exporter for monitoring server metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # 🆕 Multiple platform-rust instances (nếu có)
  # - job_name: 'platform-rust-staging'
  #   static_configs:
  #     - targets: ['<STAGING_SERVER_IP>:9090']
  #   scrape_interval: 15s
```

**File:** `monitoring/loki.yml`
```yaml
auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_scheduler:
  max_outstanding_requests_per_tenant: 32768

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://localhost:9093

analytics:
  reporting_enabled: false
```

**File:** `monitoring/alertmanager.yml`
```yaml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: ''
  smtp_auth_password: ''

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    - match:
        severity: critical
      receiver: 'critical-receiver'
    - match:
        severity: warning
      receiver: 'warning-receiver'

receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: 'Platform Rust Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  - name: 'critical-receiver'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT TRIGGERED!
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}

  - name: 'warning-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ WARNING: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
```

### **3.3 Log Shipping Setup (Optional)**

**Nếu muốn ship logs từ platform-rust server đến monitoring server:**

**Option 1: Promtail trên platform-rust server**
```yaml
# promtail.yml - Cài trên máy platform-rust
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  # 🔧 Thay đổi IP này thành IP của máy monitoring
  - url: http://<MONITORING_SERVER_IP>:3100/loki/api/v1/push

scrape_configs:
  - job_name: platform-rust
    static_configs:
      - targets:
          - localhost
        labels:
          job: platform-rust
          server: production
          __path__: /app/logs/*.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            correlation_id: correlation_id
            module: target
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          level:
          correlation_id:
          module:
```

**Option 2: Syslog forwarding (Simpler)**
```bash
# Trên platform-rust server, forward logs qua syslog
# Thêm vào rsyslog.conf:
*.* @@<MONITORING_SERVER_IP>:514
```

---

## **🚀 IMPLEMENTATION STEPS - Tách riêng biệt**

### **Phase 1: Platform-Rust Application (Week 1)**

**Trên máy chạy platform-rust:**

1. **Update Cargo.toml dependencies**
   ```bash
   # Thêm metrics dependencies
   metrics = "0.23"
   metrics-exporter-prometheus = "0.15"
   tower-http = { version = "0.6", features = ["compression-full", "cors", "fs", "metrics"] }
   ```

2. **Tạo metrics middleware** (`src/routes/middleware/metrics.rs`)
3. **Update existing middleware** để integrate metrics
4. **Add metrics endpoint** trong router (`/metrics`)
5. **Update docker-compose.yml** để expose port 9090
6. **Test metrics locally**:
   ```bash
   curl http://localhost:8386/metrics
   curl http://localhost:8386/health/detailed
   ```

### **Phase 2: Monitoring Server Setup (Week 2)**

**Trên máy monitoring riêng:**

1. **Tạo monitoring directory structure**:
   ```bash
   mkdir -p monitoring/{grafana/{provisioning,dashboards},alerts}
   ```

2. **Tạo configuration files**:
   - `docker-compose.monitoring.yml`
   - `monitoring/prometheus.yml` (với IP của platform-rust server)
   - `monitoring/loki.yml`
   - `monitoring/alertmanager.yml`

3. **Deploy monitoring stack**:
   ```bash
   docker-compose -f docker-compose.monitoring.yml up -d
   ```

4. **Verify connectivity**:
   ```bash
   # Test Prometheus targets
   curl http://localhost:9090/targets
   
   # Test Grafana
   curl http://localhost:3000/api/health
   ```

5. **Configure Prometheus targets** với IP thực của platform-rust server

### **Phase 3: Network & Security (Week 2-3)**

1. **Firewall configuration**:
   ```bash
   # Trên platform-rust server - mở port cho metrics
   sudo ufw allow from <MONITORING_SERVER_IP> to any port 9090
   sudo ufw allow from <MONITORING_SERVER_IP> to any port 8386
   
   # Trên monitoring server - mở ports cho team access
   sudo ufw allow 3000  # Grafana
   sudo ufw allow 9090  # Prometheus (optional, for debugging)
   ```

2. **SSL/TLS setup** (production):
   - Reverse proxy với nginx
   - Let's Encrypt certificates
   - Basic auth cho Prometheus

3. **Test cross-server connectivity**:
   ```bash
   # Từ monitoring server
   curl http://<PLATFORM_RUST_IP>:9090/metrics
   curl http://<PLATFORM_RUST_IP>:8386/health
   ```

### **Phase 4: Dashboards & Alerting (Week 3-4)**

1. **Import dashboards** vào Grafana
2. **Configure alert rules** trong Prometheus
3. **Setup notification channels** (email, Slack, etc.)
4. **Load testing** để verify metrics collection
5. **Documentation** và team training

---

## **📊 DASHBOARD EXAMPLES**

### **System Health Dashboard**
- HTTP request rate và latency
- Database connection pool status
- Redis connection metrics
- Error rates by endpoint
- Memory và CPU usage

### **Business Metrics Dashboard**
- User registration trends
- Authentication success/failure rates
- API usage by endpoint
- Email delivery rates
- Image upload success rates

### **Security Dashboard**
- Rate limiting hits
- Failed authentication attempts
- Suspicious IP addresses
- Permission escalation attempts

---

## **🚨 ALERTING RULES**

### **Critical Alerts**
- Database connection failures
- High error rates (>5%)
- Response time > 2 seconds
- Memory usage > 80%

### **Warning Alerts**
- Slow queries (>1 second)
- High authentication failures
- Cache miss rate > 50%
- Disk space < 20%

---

## **🔧 ENVIRONMENT VARIABLES**

**Platform-Rust Server (`.env`):**
```bash
# Existing variables (giữ nguyên)
DATABASE_URL=*****************************************************************/platform_rust
REDIS_URL=redis://:123456789@***************:6379
SERVER_HOST=0.0.0.0
SERVER_PORT=8386

# 🆕 Metrics Configuration
METRICS_ENABLED=true
PROMETHEUS_ENDPOINT=/metrics

# 🆕 Enhanced Logging
RUST_LOG=platform_rust=info,tower_http=info,sqlx=warn
LOG_LEVEL=info
LOG_FORMAT=json
```

**Monitoring Server (`.env`):**
```bash
# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123
GF_SECURITY_ADMIN_PASSWORD=admin123

# Platform-Rust Server IPs
PLATFORM_RUST_PRODUCTION_IP=*************
PLATFORM_RUST_STAGING_IP=*************

# Alert Configuration
ALERT_EMAIL_TO=<EMAIL>
ALERT_EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=587

# Retention Settings
PROMETHEUS_RETENTION=200h
LOKI_RETENTION=720h
```

---

## **🧪 TESTING & DEPLOYMENT**

### **Deployment Commands**

**Trên Platform-Rust Server:**
```bash
# 1. Update application với metrics support
git pull origin main
docker-compose build
docker-compose up -d

# 2. Verify metrics endpoint
curl http://localhost:8386/metrics
curl http://localhost:8386/health/detailed

# 3. Check logs
docker logs platform-rust-app
```

**Trên Monitoring Server:**
```bash
# 1. Setup monitoring stack
git clone <monitoring-config-repo>
cd monitoring

# 2. Update IPs trong prometheus.yml
sed -i 's/<PLATFORM_RUST_SERVER_IP>/*************/g' monitoring/prometheus.yml

# 3. Deploy monitoring stack
docker-compose -f docker-compose.monitoring.yml up -d

# 4. Verify services
docker-compose -f docker-compose.monitoring.yml ps
```

### **Cross-Server Testing**
```bash
# Từ monitoring server test connectivity
curl http://<PLATFORM_RUST_IP>:9090/metrics
curl http://<PLATFORM_RUST_IP>:8386/health

# Test Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test Grafana datasources
curl -u admin:admin123 http://localhost:3000/api/datasources

# Load test để generate metrics
wrk -t12 -c400 -d30s http://<PLATFORM_RUST_IP>:8386/api/health
```

### **Metrics Validation**
- ✅ HTTP request metrics được collect
- ✅ Database connection pool metrics
- ✅ Redis connection metrics
- ✅ Authentication metrics
- ✅ Business logic metrics
- ✅ Error tracking metrics

---

## **📚 NEXT STEPS**

1. **Implement metrics middleware** trong codebase
2. **Setup monitoring infrastructure** với Docker Compose
3. **Create Grafana dashboards** cho visualization
4. **Configure alerting rules** cho production monitoring
5. **Document runbooks** cho incident response
6. **Train team** on monitoring tools và best practices

---

## **🔗 USEFUL LINKS**

### **Platform-Rust Server:**
- **Application Health**: http://\<PLATFORM_RUST_IP\>:8386/health
- **Detailed Health**: http://\<PLATFORM_RUST_IP\>:8386/health/detailed
- **Application Metrics**: http://\<PLATFORM_RUST_IP\>:9090/metrics
- **API Documentation**: http://\<PLATFORM_RUST_IP\>:8386/swagger-ui

### **Monitoring Server:**
- **Grafana Dashboards**: http://\<MONITORING_SERVER_IP\>:3000
- **Prometheus Web UI**: http://\<MONITORING_SERVER_IP\>:9090
- **Prometheus Targets**: http://\<MONITORING_SERVER_IP\>:9090/targets
- **Alertmanager**: http://\<MONITORING_SERVER_IP\>:9093
- **Loki**: http://\<MONITORING_SERVER_IP\>:3100

### **Default Credentials:**
- **Grafana**: admin / admin123
- **Prometheus**: No auth (internal use)
- **Alertmanager**: No auth (internal use)

---

## **📋 SUMMARY - Setup tách riêng**

### **✅ Advantages của setup này:**
1. **Tách biệt concerns**: Application và monitoring độc lập
2. **Scalability**: Monitoring server có thể monitor nhiều applications
3. **Resource isolation**: Monitoring không ảnh hưởng performance của app
4. **Security**: Monitoring server có thể ở private network
5. **Maintenance**: Update monitoring stack không affect application

### **🔧 Key Configuration Points:**
1. **Network connectivity** giữa 2 servers
2. **Firewall rules** cho ports 8386, 9090
3. **IP addresses** trong prometheus.yml
4. **Log shipping** (optional) qua promtail hoặc syslog
5. **Alert routing** qua email/Slack

### **📊 Monitoring Coverage:**
- ✅ HTTP request metrics (rate, latency, errors)
- ✅ Database connection pool status
- ✅ Redis connection metrics  
- ✅ Authentication success/failure rates
- ✅ Business metrics (user actions, email delivery, etc.)
- ✅ System health checks
- ✅ Rate limiting metrics
- ✅ Error tracking với correlation IDs

---

**📝 Document Status**: Updated for separated deployment architecture  
**🔄 Last Updated**: 2024  
**👥 Maintainer**: Platform Rust Team  
**🏗️ Architecture**: Platform-Rust (Production Server) + Monitoring Stack (Separate Server)
