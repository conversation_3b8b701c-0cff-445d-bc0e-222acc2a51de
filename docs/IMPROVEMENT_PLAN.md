# 📈 Platform Rust - Improvement Plan

Dưới đây là các hạng mục cần làm để nâng cấp chất lượng codebase, tăng độ tin cậy, bảo mật và khả năng mở rộng cho dự án.

---

## 1. 🟠 SQLx Migration & Consistency
- [ ] Hoàn tất migration từ Diesel sang SQLx cho tất cả các module (đặc biệt là `progression`, `role`).
- [ ] Đảm bảo mọi repository sử dụng async SQLx, không còn code Diesel cũ.
- [ ] Refactor các model còn lại để đồng bộ kiểu dữ liệu (DateTime, enums, v.v.).
- [ ] Viết migration scripts bổ sung nếu cần.

## 2. 🟡 Testing & Coverage
- [ ] Mở rộng unit test cho tất cả các service, handler ch<PERSON>h (auth, user, laptop, email, v.v.).
- [ ] Thêm integration test (end-to-end) cho các flow quan trọng: đăng ký, đăng nhập, refresh token, RBAC, email, image upload.
- [ ] Sử dụng testcontainers hoặc docker-compose cho test Postgres/Redis thực tế.
- [ ] Đo coverage với cargo-tarpaulin, đặt mục tiêu >80%.
- [ ] Thêm test cho các edge case (Redis down, DB lỗi, token hết hạn, v.v.).

## 3. 🟢 Module Completion & Refactoring
- [ ] Hoàn thiện các module còn thiếu hoặc rỗng (`search`, `embedding`).
- [ ] Review lại các module nhỏ, chuẩn hóa cấu trúc (database/services/handlers/models/traits).
- [ ] Refactor code cũ, loại bỏ dead code, comment thừa.
- [ ] Đảm bảo mọi module đều có trait interface rõ ràng (DIP).

## 4. 🟣 Security & Edge Cases
- [ ] Thêm rate limit riêng cho OAuth callback/flow để chống brute-force.
- [ ] Review lại toàn bộ error handling cho các trường hợp đặc biệt (OAuth code reuse, token invalid, permission version mismatch).
- [ ] Thêm fuzz testing cho các API nhận input phức tạp.
- [ ] Kiểm tra lại các middleware bảo mật (security headers, CORS, request tracing).
- [ ] Thực hiện security audit (có thể dùng cargo-audit, manual review).

## 5. 🔵 Monitoring & DevOps
- [ ] Triển khai metrics Prometheus (theo Grafana Integration Plan).
- [ ] Thêm healthcheck chi tiết cho từng service (DB, Redis, Email, Cloudinary).
- [ ] Thiết lập alerting (Prometheus Alertmanager, Grafana).
- [ ] Tối ưu Dockerfile, thêm multi-arch build nếu cần.
- [ ] Thêm scripts tự động migrate DB khi deploy.

## 6. 🟤 Documentation & CI/CD
- [ ] Cập nhật README, bổ sung hướng dẫn test, deploy, troubleshooting.
- [ ] Viết docs chi tiết cho từng module (usage, API, flow diagram).
- [ ] Thêm badge coverage, build status vào README.
- [ ] Thiết lập GitHub Actions (hoặc CI khác) để tự động test, lint, build, deploy.
- [ ] Đảm bảo mọi PR đều được review và chạy test tự động.

---

> **Ưu tiên:**
> 1. SQLx migration hoàn tất trước.
> 2. Mở rộng test và tăng coverage.
> 3. Bổ sung monitoring, CI/CD, security. 