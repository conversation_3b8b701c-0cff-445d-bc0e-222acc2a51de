# 📝 Hướng dẫn tích hợp API Update Laptop

## 🎯 Tổng quan

Document này cung cấp hướng dẫn chi tiết cho team Frontend để tích hợp API cập nhật thông tin laptop trong hệ thống.

## 🔗 Endpoint chính

### Cập nhật thông tin laptop
```
PUT /api/laptops/{id}
```

## 🔐 Xác thực và phân quyền

### Headers bắt buộc
```http
Content-Type: application/json
Authorization: Bearer {access_token}
X-API-Type: private
```

### Quyền cần thiết
- **Permission**: `laptops:update`
- **Role**: Admin hoặc role có quyền quản lý laptop

## 📋 Request Body

Tất cả các trường đều là **optional** - chỉ gửi những trường cần cập nhật:

```json
{
  "brand": "Apple",
  "model": "MacBook Pro", 
  "full_name": "Apple MacBook Pro 14-inch M3 Pro (Updated)",
  "slug": "apple-macbook-pro-14-m3-pro-updated",
  "category_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "sku": "MBP14-M3-512",
  "market_region": "Global",
  "release_date": "2024-11-01",
  "description": "Laptop chuyên nghiệp với chip M3 Pro mạnh mẽ",
  "image_urls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "is_featured": true
}
```

## 📊 Validation Rules

| Trường | Kiểu dữ liệu | Bắt buộc | Validation |
|--------|-------------|----------|------------|
| `brand` | String | ❌ | 1-50 ký tự |
| `model` | String | ❌ | 1-100 ký tự |
| `full_name` | String | ❌ | 1-200 ký tự |
| `slug` | String | ❌ | 1-250 ký tự, unique |
| `category_id` | UUID | ❌ | Phải tồn tại trong hệ thống |
| `sku` | String | ❌ | Tối đa 50 ký tự, unique |
| `market_region` | Enum | ❌ | `Global`, `Vietnam`, `US`, `EU`, `Asia` |
| `release_date` | Date | ❌ | Format: YYYY-MM-DD |
| `description` | String | ❌ | Không giới hạn |
| `image_urls` | Array[String] | ❌ | Mảng URL hình ảnh |
| `is_featured` | Boolean | ❌ | true/false |

## ✅ Response thành công (200 OK)

```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 200,
  "code": "LAPTOP04",
  "message": "Laptop updated successfully",
  "data": {
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "brand": "Apple",
    "model": "MacBook Pro",
    "full_name": "Apple MacBook Pro 14-inch M3 Pro (Updated)",
    "slug": "apple-macbook-pro-14-m3-pro-updated",
    "category_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "sku": "MBP14-M3-512",
    "market_region": "Global",
    "release_date": "2024-11-01",
    "description": "Laptop chuyên nghiệp với chip M3 Pro mạnh mẽ",
    "image_urls": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ],
    "status": "draft",
    "is_featured": true,
    "view_count": 0,
    "created_at": "2025-07-25T09:00:00Z",
    "updated_at": "2025-07-25T10:30:00Z",
    "created_by": "admin-user-id",
    "updated_by": "admin-user-id"
  },
  "error": null
}
```

## ❌ Error Responses

### 400 Bad Request - Validation Error
```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 400,
  "code": "ERR_VALIDATION",
  "message": "Error occurred",
  "data": null,
  "error": "Brand must be between 1 and 50 characters"
}
```

### 401 Unauthorized
```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 401,
  "code": "ERR_UNAUTHORIZED",
  "message": "Error occurred",
  "data": null,
  "error": "Invalid or expired token"
}
```

### 403 Forbidden
```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 403,
  "code": "ERR_FORBIDDEN",
  "message": "Error occurred",
  "data": null,
  "error": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 404,
  "code": "ERR_NOT_FOUND",
  "message": "Error occurred",
  "data": null,
  "error": "Laptop not found"
}
```

### 409 Conflict
```json
{
  "timestamp": "2025-07-25T10:30:00Z",
  "path": "/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef",
  "status": 409,
  "code": "ERR_CONFLICT",
  "message": "Error occurred",
  "data": null,
  "error": "Laptop slug already exists"
}
```

## 💻 Code Examples

### JavaScript/TypeScript (Axios)
```typescript
interface UpdateLaptopRequest {
  brand?: string;
  model?: string;
  full_name?: string;
  slug?: string;
  category_id?: string;
  sku?: string;
  market_region?: 'Global' | 'Vietnam' | 'US' | 'EU' | 'Asia';
  release_date?: string;
  description?: string;
  image_urls?: string[];
  is_featured?: boolean;
}

const updateLaptop = async (laptopId: string, data: UpdateLaptopRequest) => {
  try {
    const response = await axios.put(
      `/api/laptops/${laptopId}`,
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          'X-API-Type': 'private'
        }
      }
    );
    
    return response.data;
  } catch (error) {
    if (error.response) {
      // Server responded with error status
      console.error('Update failed:', error.response.data.error);
      throw new Error(error.response.data.error);
    }
    throw error;
  }
};
```

### React Hook Example
```typescript
import { useState } from 'react';

const useUpdateLaptop = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateLaptop = async (laptopId: string, data: UpdateLaptopRequest) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await updateLaptop(laptopId, data);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { updateLaptop, loading, error };
};
```

## 🔄 Workflow tích hợp

1. **Lấy thông tin laptop hiện tại** (GET `/api/laptops/{id}`)
2. **Hiển thị form với dữ liệu có sẵn**
3. **User chỉnh sửa các trường cần thiết**
4. **Gửi request PUT với chỉ những trường đã thay đổi**
5. **Xử lý response và cập nhật UI**

## ⚠️ Lưu ý quan trọng

- **Partial Update**: Chỉ gửi những trường cần cập nhật, không cần gửi toàn bộ object
- **Slug Validation**: Slug phải unique trong toàn hệ thống
- **SKU Validation**: SKU phải unique nếu được cung cấp
- **Category Validation**: category_id phải tồn tại trong hệ thống
- **Permission Check**: User phải có quyền `laptops:update` và `admin:all`
- **Rate Limiting**: Áp dụng rate limiting cho API này

## 🧪 Testing

### Test Cases cần kiểm tra:
1. ✅ Update thành công với dữ liệu hợp lệ
2. ❌ Update với slug đã tồn tại
3. ❌ Update với category_id không tồn tại  
4. ❌ Update với dữ liệu validation lỗi
5. ❌ Update không có quyền
6. ❌ Update laptop không tồn tại

## 📞 Hỗ trợ

Nếu có vấn đề trong quá trình tích hợp, vui lòng liên hệ team Backend
