# 🏗️ Architecture Documentation

This document outlines the software architecture of the Platform-Rust API, a comprehensive backend service built with Rust.

## 💡 Design Principles

The architecture is guided by the following principles:
- **Layered Architecture**: Clear separation of concerns between web, business logic, and data access layers.
- **Modularity & Single Responsibility**: Each module has a distinct and clear purpose.
- **Dependency Injection**: Promotes loose coupling and testability through a central service container.
- **Asynchronous & Non-Blocking**: Built on Tokio and Axum for high performance and concurrency.
- **Resilience**: Designed to handle failures gracefully (e.g., Redis connection failure).
- **Developer Experience**: Automated API documentation and structured logging.

## 📁 Source Code Structure

The codebase is organized into modules based on features and responsibilities.

```
src/
├── 🚀 Application Core
│   ├── main.rs              # Application entry point, service initialization
│   ├── lib.rs               # Library root, module declarations
│   ├── config.rs            # Configuration management (from .env)
│   ├── init.rs              # Initial data seeding (e.g., default roles)
│   ├── errors.rs            # Centralized error types (AppError)
│   └── response.rs          # Standardized API response wrappers
│
├── 📦 Service Container (Dependency Injection)
│   ├── container/
│   │   ├── service_container.rs # DI container definition and assembly
│   │   ├── service_container_traits.rs # Traits for accessing services
│   │   └── mod.rs
│
├── 🧩 Feature Modules
│   ├── modules/
│   │   ├── auth/            # 🔐 Authentication & Authorization
│   │   │   ├── service.rs   #   Facade service for auth operations
│   │   │   ├── services/    #   Sub-services with single responsibilities
│   │   │   │   ├── authentication_service.rs # (Login/Register)
│   │   │   │   ├── authorization_service.rs # (Permission checks)
│   │   │   │   ├── token_service.rs # (JWT handling)
│   │   │   │   └── oauth_service.rs # (OAuth2 flow)
│   │   │   ├── handler.rs   #   API endpoint handlers
│   │   │   └── models.rs    #   Data models (requests, responses)
│   │   │
│   │   ├── user/            # 👤 User Management
│   │   ├── role/            # 🎭 Role Management (RBAC)
│   │   ├── permission/      # 🛡️ Permission Management (RBAC)
│   │   │   ├── handler.rs   #   (API handlers)
│   │   │   ├── service.rs   #   (Business logic)
│   │   │   ├── repository.rs#   (Database operations)
│   │   │   └── models.rs    #   (Data models)
│   │   │
│   │   ├── email/           # 📧 Email Service
│   │   │   ├── service.rs   #   Listens for events and sends emails
│   │   │   ├── event_bus.rs #   In-memory channel for email events
│   │   │   └── templates.rs #   Email templates (Handlebars)
│   │   │
│   │   └── redis/           # ⚡ Redis Caching & Utilities
│   │       ├── service.rs   #   Provides get/set/delete operations
│   │       ├── redis_manager.rs # Manages the connection pool
│   │       └── handler.rs   #   Debug/status endpoints
│
├── 🌐 Web & Network Layer
│   ├── routes/
│   │   ├── router.rs        #   Main router configuration, combines all routes
│   │   └── middleware/      #   Request middleware
│   │       ├── auth.rs      #     (JWT validation)
│   │       └── rate_limit.rs#     (IP-based rate limiting)
│   │
│   └── handlers/
│       └── fallback.rs      #   404 Not Found handler
│
└── 🗄️ Data Persistence
    ├── database.rs          # Database connection setup (SQLx PgPool)
    └── schema.rs            # SQL queries and enums
```

## 🔄 Data & Request Flow

The system employs a layered architecture. A typical request flows through the layers as follows:

```
                                     +-----------------------------+
                                     | ⚡ Redis Cache               |
                                     | (Session, Permissions, etc.)|
                                     +--------------+--------------+
                                                    ^
                                                    | (Cache Check)
                                                    v
+---------------+   +----------------+   +-----------------------+   +-----------------+   +-----------------+
| HTTP Request  |-->|   Axum Router  |-->|      Middleware       |-->|  API Handler    |-->| Service Layer   |
| (Client)      |   | (routes.rs)    |   | (Auth, Rate Limit...) |   | (modules/../)   |   | (Business Logic)|
+---------------+   +----------------+   +-----------------------+   +-----------------+   +-------+---------+
                                                                                                    | (DB Query)
                                                                                                    v
                                                                                        +-----------------+
                                                                                        | Repository Layer|
                                                                                        | (Data Access)   |
                                                                                        +-------+---------+
                                                                                                |
                                                                                                v
                                                                                        +-----------------+
                                                                                        |   PostgreSQL    |
                                                                                        |   (Database)    |
                                                                                        +-----------------+
```

## 🧩 Core Architectural Patterns

### Dependency Injection (DI)
- **Pattern**: A central `ServiceContainer` is created at startup and injected into Axum's handlers via `Extension`.
- **Benefit**: Decouples components, simplifies testing (by allowing mock services), and centralizes service management.
- **Implementation**:
  ```rust
  // container/service_container.rs
  pub struct ServiceContainer {
      pub db: Arc<Database>,
      pub user_service: Arc<dyn UserServiceTrait>,
      pub auth_service: Arc<dyn AuthServiceTrait>,
      // ... other services
      pub email_event_bus: Arc<EmailEventBus>,
      pub redis_service: Option<Arc<dyn RedisServiceTrait>>, // Optional for resilience
  }
  ```

### Event-Driven Architecture (for Decoupling)
- **Pattern**: Services communicate asynchronously via an `EmailEventBus` (an in-memory channel).
- **Example**: The `AuthService` publishes a `WelcomeEmail` event upon user registration. The `EmailService`, running in a background task, listens for this event and sends the email without blocking the API response.
- **Benefit**: Decouples services. The `AuthService` doesn't need to know how emails are sent, only that an event occurred.

### Resilient Service Integration
- **Pattern**: External services that are non-critical (like Redis) are initialized as `Option<T>` and connected in the background.
- **Benefit**: The application can start and run even if an auxiliary service is down, increasing overall system resilience. The application can gracefully degrade functionality.

## 🛡️ Security
- **Authentication**: JWT-based authentication enforced by middleware on protected routes. Supports both password-based login and OAuth2 (e.g., Google).
- **Authorization**: A comprehensive Role-Based Access Control (RBAC) system. Permissions are checked at the service layer.
- **Password Hashing**: Uses `argon2` for secure password storage.
- **Transport Security**: Assumes deployment behind a reverse proxy (e.g., Nginx) handling TLS termination.

## 📄 API Documentation
- **Tool**: `Utoipa` is used to generate an OpenAPI 3.0 specification directly from the Rust code, including models and handlers.
- **Benefit**: Documentation is always in sync with the code. Provides an interactive Swagger UI for easy API exploration and testing.
- **Access**: The Swagger UI is available at the `/swagger-ui` endpoint in development environments.

## 🔗 Key Integrations
- **Web Framework**: **Axum** (built by the Tokio team).
- **Async Runtime**: **Tokio**.
- **Database ORM**: **SQLx** with async PgPool.
- **Database**: **PostgreSQL**.
- **Caching / State**: **Redis** with `bb8-redis` for async connection pooling.
- **Email**: **Lettre** for SMTP transport and **Handlebars** for HTML templating.
- **Serialization**: **Serde** (`serde_json`).
- **Configuration**: **Dotenvy**.
- **Validation**: **Validator**.
- **Logging**: **Tracing**.
 