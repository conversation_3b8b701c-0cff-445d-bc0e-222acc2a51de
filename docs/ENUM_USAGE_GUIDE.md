# Enum Documentation - platform-rust

## 1. PostgreSQL Enum Types

### 1.1. `screen_resolution_enum`

- <PERSON><PERSON><PERSON><PERSON> định nghĩa trong migration:
  ```sql
  CREATE TYPE screen_resolution_enum AS ENUM ('FHD', '2K', '2.5K', '3K', '3.5K', '4K');
  ```
- Sử dụng cho trường `screen_resolution` trong bảng `specifications`:
  ```sql
  screen_resolution screen_resolution_enum NOT NULL
  ```

### 1.2. `laptop_status_enum`

- Đ<PERSON><PERSON>c định nghĩa trong migration:
  ```sql
  CREATE TYPE laptop_status_enum AS ENUM ('draft', 'published', 'archived');
  ```
- Sử dụng cho trường `status` trong bảng `laptops`:
  ```sql
  status laptop_status_enum NOT NULL DEFAULT 'draft'
  ```

## 2. Rust Enum Mapping (SQLx)

### 2.1. Enum Rust

```rust
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, serde::Serialize, serde::Deserialize, utoipa::ToSchema, sqlx::Type)]
#[sqlx(type_name = "screen_resolution_enum", rename_all = "UPPERCASE")]
pub enum ScreenResolution {
    FHD,
    TwoK,
    TwoPointFiveK,
    ThreeK,
    ThreePointFiveK,
    FourK,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize, utoipa::ToSchema, sqlx::Type)]
#[sqlx(type_name = "laptop_status_enum", rename_all = "lowercase")]
pub enum LaptopStatus {
    Draft,
    Published,
    Archived,
}
```

### 2.2. Mapping Rust <-> PostgreSQL

Sử dụng sqlx::Type để map enum với PostgreSQL type.
```
```

## 3. Sử dụng trong Model & API

### 3.1. Database Model

File: `src/modules/laptop/models.rs`
```rust
// Trong SQLx model
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct SqlxLaptop {
    // ...
    pub status: LaptopStatus,
    // ...
}

#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct SqlxSpecification {
    // ...
    pub screen_resolution: ScreenResolution,
    // ...
}
```

### 3.2. API Model

```rust
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LaptopPublicView {
    pub status: LaptopStatus,
    // ...
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct Specification {
    pub screen_resolution: ScreenResolution,
    // ...
}
```

### 3.3. Request Model

```rust
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreateSpecificationRequest {
    pub screen_resolution: ScreenResolution,
    // ...
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct LaptopPaginationRequest {
    pub status: Option<LaptopStatus>,
    // ...
}
```

## 4. Business Logic & Repository

- Trường `status` của laptop được dùng để phân biệt trạng thái (draft, published, archived) trong các hàm publish/archive.
- Trường `screen_resolution` dùng để filter, lưu trữ thông tin chi tiết của specification.

## 5. Index & Constraint

- Có index cho cả hai trường này để tối ưu truy vấn:
  ```sql
  CREATE INDEX idx_laptops_status ON laptops(status);
  CREATE INDEX idx_specs_screen_resolution ON specifications(screen_resolution);
  ```
- Có constraint đảm bảo giá trị hợp lệ:
  ```sql
  ALTER TABLE laptops ADD CONSTRAINT check_laptop_status
      CHECK (status IN ('draft', 'published', 'archived'));
  ```

## 6. Tổng hợp giá trị enum

| Enum                  | Giá trị hợp lệ                        |
|-----------------------|---------------------------------------|
| screen_resolution     | FHD, 2K, 2.5K, 3K, 3.5K, 4K           |
| status (laptop)       | draft, published, archived            |

---

**Tài liệu này chỉ tổng hợp từ codebase thực tế, không thêm kiến thức ngoài.**