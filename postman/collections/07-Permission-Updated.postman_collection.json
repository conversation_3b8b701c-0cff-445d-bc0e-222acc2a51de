{"info": {"name": "07-Permission", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Permission Management (Private APIs)", "item": [{"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"laptops:create\",\n    \"description\": \"Permission to create laptops\",\n    \"resource\": \"laptops\",\n    \"action\": \"create\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions", "host": ["{{base_url}}"], "path": ["api", "permissions"]}, "description": "Create a new permission"}}, {"name": "Get Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "permissions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get permissions with pagination"}}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}, "description": "Get permission by ID"}}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"laptops:create_advanced\",\n    \"description\": \"Advanced permission to create laptops with all features\",\n    \"resource\": \"laptops\",\n    \"action\": \"create\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}, "description": "Update permission"}}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}, "description": "Delete permission"}}]}]}