{"info": {"name": "08-Progression", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public APIs", "item": [{"name": "Get Leaderboard (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/leaderboard?limit=10", "host": ["{{base_url}}"], "path": ["api", "leaderboard"], "query": [{"key": "limit", "value": "10"}]}, "description": "Get leaderboard (public API)"}}, {"name": "Get User Level (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/level", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "level"]}, "description": "Get user level (public API with limited info)"}}]}, {"name": "Private APIs", "item": [{"name": "Get Leaderboard (Private)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/leaderboard?limit=10", "host": ["{{base_url}}"], "path": ["api", "leaderboard"], "query": [{"key": "limit", "value": "10"}]}, "description": "Get leaderboard (private API with detailed info)"}}, {"name": "Get User Level (Private)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/level", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "level"]}, "description": "Get user level (private API with detailed info)"}}, {"name": "Add Experience", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"experience_points\": 100,\n    \"reason\": \"Completed tutorial\"\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/experience", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "experience"]}, "description": "Add experience points to user"}}]}]}