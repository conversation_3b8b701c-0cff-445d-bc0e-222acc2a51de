{"info": {"name": "05-User", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Management (Private APIs)", "item": [{"name": "Get Users with Pagination", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users?page=1&limit=10&search=john", "host": ["{{base_url}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "john", "disabled": true}]}, "description": "Get users with pagination and search"}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\",\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"role_ids\": [\"{{role_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}, "description": "Create a new user"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}, "description": "Get user by ID"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON> Updated\",\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}, "description": "Update user information"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}, "description": "Delete user"}}, {"name": "Update User Roles", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"role_ids\": [\"{{role_id_1}}\", \"{{role_id_2}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}, "description": "Update user roles"}}, {"name": "Get User Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}, "description": "Get user roles"}}]}, {"name": "Profile (Private API)", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/profile/me", "host": ["{{base_url}}"], "path": ["api", "profile", "me"]}, "description": "Get current user profile with progression and title info"}}]}]}