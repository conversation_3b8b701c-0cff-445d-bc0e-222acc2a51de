{"info": {"name": "04-Category", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public APIs", "item": [{"name": "Get Categories (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "categories"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get categories (public API)"}}, {"name": "Get Category by ID (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}, "description": "Get category by ID (public API)"}}]}, {"name": "Private APIs", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Gaming Laptops\",\n    \"description\": \"High-performance laptops for gaming\",\n    \"category_type\": \"laptop\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/categories", "host": ["{{base_url}}"], "path": ["api", "categories"]}, "description": "Create a new category"}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Gaming Laptops Updated\",\n    \"description\": \"Updated description for gaming laptops\",\n    \"category_type\": \"laptop\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}, "description": "Update category"}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}, "description": "Delete category"}}, {"name": "Toggle Category Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}/toggle-status", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}", "toggle-status"]}, "description": "Toggle category active/inactive status"}}, {"name": "Get Categories by Type", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/type/laptop", "host": ["{{base_url}}"], "path": ["api", "categories", "type", "laptop"]}, "description": "Get categories by type (private API with detailed info)"}}]}]}