{"info": {"name": "03-<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Laptops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops?page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "laptops"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Get Laptop by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/apple-macbook-pro-14-m3-pro", "host": ["{{base_url}}"], "path": ["api", "laptops", "apple-macbook-pro-14-m3-pro"]}}}, {"name": "Increment View Count", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/laptops/d1e2f3a4-b5c6-7890-1234-567890abcdef/view", "host": ["{{base_url}}"], "path": ["api", "laptops", "d1e2f3a4-b5c6-7890-1234-567890abcdef", "view"]}}}, {"name": "Create Complete Laptop", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Apple\",\n    \"model\": \"MacBook Pro\",\n    \"full_name\": \"Apple MacBook Pro 14-inch M3 Pro\",\n    \"slug\": \"apple-macbook-pro-14-m3-pro\",\n    \"category_id\": \"3fa85f64-5717-4562-b3fc-2c963f66afa6\",\n    \"market_region\": \"Global\",\n    \"image_urls\": [\"https://example.com/image1.jpg\"],\n    \"specification\": { /* details */ },\n    \"price\": { /* details */ }\n}"}, "url": {"raw": "{{base_url}}/api/laptops/complete", "host": ["{{base_url}}"], "path": ["api", "laptops", "complete"]}}}]}