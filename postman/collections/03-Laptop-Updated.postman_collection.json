{"info": {"name": "03-<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public APIs", "item": [{"name": "Get Laptops (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops?page=1&per_page=10&search=macbook&brand=Apple", "host": ["{{base_url}}"], "path": ["api", "laptops"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "search", "value": "macbook"}, {"key": "brand", "value": "Apple"}, {"key": "category_id", "value": "", "disabled": true}, {"key": "is_featured", "value": "", "disabled": true}, {"key": "market_region", "value": "", "disabled": true}]}, "description": "Get laptops with pagination and filters (public API)"}}, {"name": "Get Laptop by Slug (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/by-slug/apple-macbook-pro-14-m3-pro", "host": ["{{base_url}}"], "path": ["api", "laptops", "by-slug", "apple-macbook-pro-14-m3-pro"]}, "description": "Get laptop details by slug (public API)"}}, {"name": "Increment View Count", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/view", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "view"]}, "description": "Increment laptop view count"}}, {"name": "Get Laptop Prices", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/prices", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "prices"]}, "description": "Get prices for a specific laptop"}}, {"name": "Get Laptop Specifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/specifications", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "specifications"]}, "description": "Get specifications for a specific laptop"}}]}, {"name": "Private APIs - Laptop Management", "item": [{"name": "Create Complete Laptop", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Apple\",\n    \"model\": \"MacBook Pro\",\n    \"full_name\": \"Apple MacBook Pro 14-inch M3 Pro\",\n    \"slug\": \"apple-macbook-pro-14-m3-pro-2024\",\n    \"category_id\": \"{{category_id}}\",\n    \"market_region\": \"Global\",\n    \"image_urls\": [\"https://example.com/image1.jpg\"],\n    \"specification\": {\n        \"processor\": \"Apple M3 Pro\",\n        \"memory\": \"16GB\",\n        \"storage\": \"512GB SSD\",\n        \"display\": \"14-inch Liquid Retina XDR\",\n        \"graphics\": \"Integrated\",\n        \"operating_system\": \"macOS\",\n        \"battery_life\": \"18 hours\",\n        \"weight\": \"1.6 kg\",\n        \"dimensions\": \"31.26 x 22.12 x 1.55 cm\",\n        \"ports\": [\"3x Thunderbolt 4\", \"HDMI\", \"MagSafe 3\", \"3.5mm headphone jack\"]\n    },\n    \"price\": {\n        \"amount\": 1999.00,\n        \"currency\": \"USD\",\n        \"market_region\": \"Global\",\n        \"retailer\": \"Apple Store\",\n        \"is_current\": true\n    }\n}"}, "url": {"raw": "{{base_url}}/api/laptops/complete", "host": ["{{base_url}}"], "path": ["api", "laptops", "complete"]}, "description": "Create a complete laptop with specifications and price"}}, {"name": "Get Laptop by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}, "description": "Get laptop by ID (private API with full details)"}}, {"name": "Update Laptop", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Apple\",\n    \"model\": \"MacBook Pro\",\n    \"full_name\": \"Apple MacBook Pro 14-inch M3 Pro (Updated)\",\n    \"slug\": \"apple-macbook-pro-14-m3-pro-updated\",\n    \"category_id\": \"{{category_id}}\",\n    \"market_region\": \"Global\",\n    \"image_urls\": [\"https://example.com/updated-image.jpg\"]\n}"}, "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}, "description": "Update laptop information"}}, {"name": "Delete Laptop", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}, "description": "Delete laptop"}}, {"name": "Publish Laptop", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/publish", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "publish"]}, "description": "Publish laptop (make it visible to public)"}}, {"name": "Archive Laptop", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/archive", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "archive"]}, "description": "Archive laptop (hide from public)"}}, {"name": "Set Laptop Featured", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"is_featured\": true\n}"}, "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/featured", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "featured"]}, "description": "Set laptop as featured or not"}}]}, {"name": "Private APIs - Specifications", "item": [{"name": "Create Specification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"laptop_id\": \"{{laptop_id}}\",\n    \"processor\": \"Apple M3 Pro\",\n    \"memory\": \"16GB\",\n    \"storage\": \"512GB SSD\",\n    \"display\": \"14-inch Liquid Retina XDR\",\n    \"graphics\": \"Integrated\",\n    \"operating_system\": \"macOS\",\n    \"battery_life\": \"18 hours\",\n    \"weight\": \"1.6 kg\",\n    \"dimensions\": \"31.26 x 22.12 x 1.55 cm\",\n    \"ports\": [\"3x Thunderbolt 4\", \"HDMI\", \"MagSafe 3\", \"3.5mm headphone jack\"]\n}"}, "url": {"raw": "{{base_url}}/api/specifications", "host": ["{{base_url}}"], "path": ["api", "specifications"]}, "description": "Create laptop specification"}}, {"name": "Get Specification by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}, "description": "Get specification by ID"}}, {"name": "Update Specification", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"processor\": \"Apple M3 Pro (Updated)\",\n    \"memory\": \"32GB\",\n    \"storage\": \"1TB SSD\"\n}"}, "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}, "description": "Update specification"}}, {"name": "Delete Specification", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}, "description": "Delete specification"}}]}, {"name": "Private APIs - Prices", "item": [{"name": "Create Price", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"laptop_id\": \"{{laptop_id}}\",\n    \"amount\": 1999.00,\n    \"currency\": \"USD\",\n    \"market_region\": \"Global\",\n    \"retailer\": \"Apple Store\",\n    \"is_current\": true\n}"}, "url": {"raw": "{{base_url}}/api/prices", "host": ["{{base_url}}"], "path": ["api", "prices"]}, "description": "Create laptop price"}}, {"name": "Get Price by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}, "description": "Get price by ID"}}, {"name": "Update Price", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 1899.00,\n    \"currency\": \"USD\",\n    \"market_region\": \"Global\",\n    \"retailer\": \"Apple Store\",\n    \"is_current\": true\n}"}, "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}, "description": "Update price"}}, {"name": "Delete Price", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}, "description": "Delete price"}}, {"name": "Set Current Price", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/prices/{{price_id}}/set-current", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "prices", "{{price_id}}", "set-current"]}, "description": "Set a price as the current price for a laptop"}}]}]}