{"info": {"name": "02-Image", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Generate Image Signature", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"resource_type\": \"laptop\",\n    \"folder\": \"laptops/test\",\n    \"tags\": [\"featured\"],\n    \"max_file_size\": 10485760,\n    \"allowed_formats\": [\"jpg\", \"png\"]\n}"}, "url": {"raw": "{{base_url}}/api/image/signature", "host": ["{{base_url}}"], "path": ["api", "image", "signature"]}}}, {"name": "Validate Image URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"image_url\": \"https://res.cloudinary.com/yourcloud/image/upload/laptops/test/image.jpg\",\n    \"resource_type\": \"laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/image/validate", "host": ["{{base_url}}"], "path": ["api", "image", "validate"]}}}, {"name": "Delete Image", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"public_id\": \"laptops/test/image\",\n    \"resource_type\": \"laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/image/delete", "host": ["{{base_url}}"], "path": ["api", "image", "delete"]}}}, {"name": "Extract Public ID", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"image_url\": \"https://res.cloudinary.com/yourcloud/image/upload/laptops/test/image.jpg\",\n    \"resource_type\": \"laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/image/extract-public-id", "host": ["{{base_url}}"], "path": ["api", "image", "extract-public-id"]}}}, {"name": "Cloudinary Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/image/health", "host": ["{{base_url}}"], "path": ["api", "image", "health"]}}}]}