{"info": {"_postman_id": "platform-rust-api-v7", "name": "01-Authentication", "description": "Authentication endpoints for the Platform Rust API.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "Health Checks", "item": [{"name": "Simple Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Simple health check endpoint."}}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}, "description": "Detailed health check including database status."}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{test_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Register", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_user_email}}\",\n    \"username\": \"testuser\",\n    \"fullname\": \"Test User\",\n    \"password\": \"TestPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/oauth/google", "host": ["{{base_url}}"], "path": ["api", "auth", "o<PERSON>h", "google"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/oauth/google/callback", "host": ["{{base_url}}"], "path": ["api", "auth", "o<PERSON>h", "google", "callback"]}}}]}, {"name": "Profile", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/profile/me", "host": ["{{base_url}}"], "path": ["api", "profile", "me"]}}}]}, {"name": "Users", "item": [{"name": "Get Users (Paginated)", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"username\": \"newuser\",\n    \"fullname\": \"New User\",\n    \"password\": \"NewUserPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"updateduser\",\n    \"fullname\": \"Updated User Name\"\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}}, {"name": "Get User Roles", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}}}, {"name": "Update User Roles", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"role_ids\": [\"{{role_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}}}]}, {"name": "Roles & Permissions", "item": [{"name": "Get Roles", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}}}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"new_role\",\n    \"description\": \"A new role for testing\"\n}"}, "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}}}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"updated_role_name\",\n    \"description\": \"Updated role description\"\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}}}, {"name": "Get Role Permissions", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}", "permissions"]}}}, {"name": "Update Role Permissions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"permission_ids\": [\"{{permission_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}", "permissions"]}}}, {"name": "Get Permissions", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions", "host": ["{{base_url}}"], "path": ["api", "permissions"]}}}, {"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"new:permission\",\n    \"description\": \"A new permission for testing\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions", "host": ["{{base_url}}"], "path": ["api", "permissions"]}}}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}}}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"updated:permission\",\n    \"description\": \"Updated permission description\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}}}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{permission_id}}"]}}}, {"name": "Update Permission Matrix", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"matrix\": {\n        \"{{role_id}}\": [\"{{permission_id}}\"]\n    }\n}"}, "url": {"raw": "{{base_url}}/api/permissions/matrix", "host": ["{{base_url}}"], "path": ["api", "permissions", "matrix"]}}}]}, {"name": "Laptops", "item": [{"name": "Get Laptops (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops", "host": ["{{base_url}}"], "path": ["api", "laptops"]}}}, {"name": "Get Laptop by Slug (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/by-slug/{{laptop_slug}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "by-slug", "{{laptop_slug}}"]}}}, {"name": "Get Prices by Laptop ID (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/prices", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "prices"]}}}, {"name": "Get Specifications by Laptop ID (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/specifications", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "specifications"]}}}, {"name": "Increment Laptop View Count", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/view", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "view"]}}}, {"name": "Create Complete La<PERSON>op (Private)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Awesome Laptops\",\n    \"model\": \"Pro 1\",\n    \"full_name\": \"Awesome Laptops Pro 1\",\n    \"slug\": \"awesome-laptops-pro-1\",\n    \"category_id\": \"{{category_id}}\",\n    \"description\": \"A new powerful laptop for testing.\",\n    \"market_region\": \"Global\",\n    \"release_date\": \"2025-07-15\",\n    \"is_featured\": false,\n    \"image_urls\": [\"https://example.com/image.jpg\"],\n    \"specification\": {\n        \"cpu_brand\": \"Intel\",\n        \"cpu_model\": \"Core i9\",\n        \"ram_size\": 32,\n        \"storage_type\": \"SSD\",\n        \"storage_capacity\": 1024,\n        \"gpu_type\": \"Dedicated\",\n        \"gpu_model\": \"NVIDIA RTX 4090\",\n        \"screen_size\": 16,\n        \"screen_resolution\": \"4K\",\n        \"refresh_rate\": 120\n    },\n    \"price\": {\n        \"min_price\": 2499.99,\n        \"max_price\": 2999.99,\n        \"currency\": \"USD\",\n        \"region\": \"US\",\n        \"is_current\": true\n    }\n}"}, "url": {"raw": "{{base_url}}/api/laptops/complete", "host": ["{{base_url}}"], "path": ["api", "laptops", "complete"]}}}, {"name": "Get Laptop by ID (Private)", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}}}, {"name": "Update <PERSON><PERSON><PERSON> (Private)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Laptop Name\"\n}"}, "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}}}, {"name": "Delete <PERSON> (Private)", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}"]}}}, {"name": "Publish Laptop", "request": {"method": "PUT", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/publish", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "publish"]}}}, {"name": "Archive Laptop", "request": {"method": "PUT", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/archive", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "archive"]}}}, {"name": "Set Laptop Featured", "request": {"method": "PUT", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/featured", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "featured"]}}}]}, {"name": "Laptop Components (Specs & Prices)", "item": [{"name": "Create Specification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"CPU\", \"value\": \"Intel Core i9\"\n}"}, "url": {"raw": "{{base_url}}/api/specifications", "host": ["{{base_url}}"], "path": ["api", "specifications"]}}}, {"name": "Get Specification by ID", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}}}, {"name": "Update Specification", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"CPU\", \"value\": \"AMD Ryzen 9\"\n}"}, "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}}}, {"name": "Delete Specification", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{spec_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{spec_id}}"]}}}, {"name": "Create Price", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"laptop_id\": \"{{laptop_id}}\", \"price\": 1999.99, \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/api/prices", "host": ["{{base_url}}"], "path": ["api", "prices"]}}}, {"name": "Get Price by ID", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}}}, {"name": "Update Price", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"price\": 1899.99, \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}}}, {"name": "Delete Price", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/prices/{{price_id}}", "host": ["{{base_url}}"], "path": ["api", "prices", "{{price_id}}"]}}}, {"name": "Set Current Price for Laptop", "request": {"method": "PUT", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{laptop_id}}/prices/{{price_id}}/set-current", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{laptop_id}}", "prices", "{{price_id}}", "set-current"]}}}]}, {"name": "Categories", "item": [{"name": "Get Categories (Shared)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories", "host": ["{{base_url}}"], "path": ["api", "categories"]}}}, {"name": "Get Category by ID (Shared)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}}}, {"name": "Create Category (Private)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New Category\",\n    \"slug\": \"new-category-slug\",\n    \"category_type\": \"Laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/categories", "host": ["{{base_url}}"], "path": ["api", "categories"]}}}, {"name": "Update Category (Private)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Category Name\"\n}"}, "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}}}, {"name": "Delete Category (Private)", "request": {"method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}"]}}}, {"name": "Toggle Category Status (Private)", "request": {"method": "PATCH", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{category_id}}/toggle-status", "host": ["{{base_url}}"], "path": ["api", "categories", "{{category_id}}", "toggle-status"]}}}, {"name": "Get Categories by Type (Private)", "request": {"method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/type/Laptop", "host": ["{{base_url}}"], "path": ["api", "categories", "type", "Laptop"]}}}]}, {"name": "Progression", "item": [{"name": "Get Leaderboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/leaderboard", "host": ["{{base_url}}"], "path": ["api", "leaderboard"]}}}, {"name": "Get User Level", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/level", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "level"]}}}, {"name": "Add Experience (Private)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"experience\": 100 \n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/experience", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "experience"]}}}]}, {"name": "Redis", "item": [{"name": "Redis Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/status", "host": ["{{base_url}}"], "path": ["api", "redis", "status"]}}}, {"name": "<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/ping", "host": ["{{base_url}}"], "path": ["api", "redis", "ping"]}}}, {"name": "List Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/keys", "host": ["{{base_url}}"], "path": ["api", "redis", "keys"]}}}, {"name": "Get Key", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/redis/{{redis_key}}", "host": ["{{base_url}}"], "path": ["api", "redis", "{{redis_key}}"]}}}, {"name": "Set Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"value\": \"some_value\"\n}"}, "url": {"raw": "{{base_url}}/api/redis/{{redis_key}}", "host": ["{{base_url}}"], "path": ["api", "redis", "{{redis_key}}"]}}}, {"name": "Delete Key", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/redis/{{redis_key}}", "host": ["{{base_url}}"], "path": ["api", "redis", "{{redis_key}}"]}}}]}]}