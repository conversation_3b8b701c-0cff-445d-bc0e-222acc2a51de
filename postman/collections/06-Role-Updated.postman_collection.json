{"info": {"name": "06-Role", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Role Management (Private APIs)", "item": [{"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Content Manager\",\n    \"description\": \"Can manage content and moderate users\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}, "description": "Create a new role"}}, {"name": "Get Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "roles"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get roles with pagination"}}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}, "description": "Get role by ID"}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Content Manager Updated\",\n    \"description\": \"Updated description for content manager role\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}, "description": "Update role"}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}"]}, "description": "Delete role"}}, {"name": "Get Role Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}", "permissions"]}, "description": "Get permissions for a role"}}, {"name": "Update Role Permissions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"permission_ids\": [\"{{permission_id_1}}\", \"{{permission_id_2}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{role_id}}", "permissions"]}, "description": "Update role permissions"}}]}, {"name": "Permission Matrix", "item": [{"name": "Update Permission Matrix", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"role_permissions\": [\n        {\n            \"role_id\": \"{{role_id_1}}\",\n            \"permission_ids\": [\"{{permission_id_1}}\", \"{{permission_id_2}}\"]\n        },\n        {\n            \"role_id\": \"{{role_id_2}}\",\n            \"permission_ids\": [\"{{permission_id_3}}\", \"{{permission_id_4}}\"]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/permissions/matrix", "host": ["{{base_url}}"], "path": ["api", "permissions", "matrix"]}, "description": "Update permission matrix for multiple roles"}}]}]}