{"info": {"name": "04-Category", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Categories", "request": {"method": "GET", "url": "{{base_url}}/api/categories"}}, {"name": "Get Category by ID", "request": {"method": "GET", "url": "{{base_url}}/api/categories/{{category_id}}"}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{ /* body */ }"}, "url": "{{base_url}}/api/categories"}}]}