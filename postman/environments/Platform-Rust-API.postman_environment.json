{"id": "platform-rust-environment", "name": "Platform Rust API - Environment", "values": [{"key": "base_url", "value": "http://localhost:8386", "description": "Base URL for the Platform Rust API", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "Test email for authentication", "enabled": true}, {"key": "test_password", "value": "SuperAdmin123!", "description": "Test password for authentication", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT access token (auto-populated)", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT refresh token (auto-populated)", "enabled": true}, {"key": "user_id", "value": "", "description": "Current user ID (auto-populated)", "enabled": true}, {"key": "test_user_email", "value": "<EMAIL>", "description": "Test user email for user management", "enabled": true}, {"key": "test_role_id", "value": "", "description": "Test role ID (auto-populated)", "enabled": true}, {"key": "test_permission_id", "value": "", "description": "Test permission ID (auto-populated)", "enabled": true}, {"key": "test_laptop_id", "value": "", "description": "Test laptop ID (auto-populated)", "enabled": true}, {"key": "test_category_id", "value": "", "description": "Test category ID (auto-populated)", "enabled": true}], "_postman_variable_scope": "environment"}