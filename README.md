# Platform Rust - Axum Backend

Một backend API được xây dựng bằng Rust và Axum framework, sử dụng SQLx cho database interactions và tuân thủ nguyên tắc SOLID.

## Cấu trúc Project

```
src/
├── main.rs              # Entry point
├── lib.rs               # Library exports  
├── config.rs            # Configuration management
├── errors.rs            # Error handling
├── database.rs          # SQLx connection pool
├── schema.rs            # SQL queries and enums
├── constants.rs         # Application constants
├── response.rs          # Standardized API responses
├── modules/             # Domain modules (module-first approach)
│   ├── user/
│   │   ├── models/      # User domain models
│   │   ├── database/    # SQLx repository and models
│   │   ├── services/    # Business logic
│   │   ├── handlers/    # HTTP handlers
│   │   └── mod.rs
│   ├── role/
│   │   └── ... (similar structure)
│   └── mod.rs
├── handlers/
│   ├── fallback.rs      # Global fallback handler
│   └── mod.rs
├── routes/
│   ├── router.rs        # API routes definition
│   ├── middleware/      # Custom middleware
│   └── mod.rs
└── sqlx-migrations/     # SQLx database migrations
```

## Cài đặt

### Development (Local)

1. **Cài đặt Rust**: https://rustup.rs/

2. **Clone repository và cài đặt dependencies**:
```bash
git clone <repository-url>
cd platform-rust
cargo build
```

3. **Thiết lập environment variables**:
```bash
cp .env.example .env
# Chỉnh sửa .env với thông tin cụ thể của bạn
```

4. **Chạy database migrations**:
```bash
sqlx migrate run
```

5. **Khởi chạy server**:
```bash
cargo run
```

### Production (Docker)

1. **Thiết lập environment file**:
```bash
cp .env.example .env
# Cập nhật .env với production values
```

2. **Chạy với Docker Compose**:
```bash
docker-compose up -d
```

3. **Kiểm tra logs**:
```bash
docker-compose logs -f platform-rust
```

## Configuration

### Environment Variables

Tất cả cấu hình được quản lý thông qua environment variables. Tham khảo file `.env.example` để biết danh sách đầy đủ.

**Các biến quan trọng:**

#### Database
- `DATABASE_URL`: PostgreSQL connection string
- `DB_MAX_CONNECTIONS`: Số kết nối tối đa (default: 16)

#### Security
- `JWT_SECRET`: Secret key cho JWT tokens (PHẢI thay đổi trong production)
- `ACCESS_TOKEN_EXPIRY_SECS`: Thời gian sống của access token (default: 3600)

#### Redis
- `REDIS_URL`: Redis connection string
- `REDIS_MAX_CONNECTIONS`: Số kết nối Redis tối đa (default: 16)

#### Email (SMTP)
- `SMTP_HOST`: SMTP server host
- `SMTP_USERNAME`: Email username
- `SMTP_PASSWORD`: Email password hoặc app password
- `FROM_EMAIL`: Email gửi đi

#### OAuth
- `GOOGLE_OAUTH_CLIENT_ID`: Google OAuth Client ID
- `GOOGLE_OAUTH_CLIENT_SECRET`: Google OAuth Client Secret

## API Documentation

Sau khi khởi chạy server, truy cập:

- **Swagger UI**: http://localhost:8386/swagger-ui
- **OpenAPI JSON**: http://localhost:8386/api-docs/openapi.json

## Docker Commands

```bash
# Build image
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Restart specific service
docker-compose restart platform-rust

# Shell access
docker-compose exec platform-rust sh
```

## Development

### Testing
```bash
cargo test
```

### Code formatting
```bash
cargo fmt
```

### Linting
```bash
cargo clippy
```

### Watch mode (với cargo-watch)
```bash
cargo install cargo-watch
cargo watch -x run
```

## Features

- ✅ JWT Authentication & Authorization
- ✅ Role-Based Access Control (RBAC)
- ✅ Google OAuth2 Integration
- ✅ Email Service với Redis Queue
- ✅ Redis Caching
- ✅ PostgreSQL với SQLx
- ✅ API Documentation với Swagger/OpenAPI
- ✅ Docker Support
- ✅ Comprehensive Error Handling
- ✅ Request Rate Limiting
- ✅ CORS Configuration
- ✅ Health Check Endpoints

## Architecture

Xem [ARCHITECTURE.md](docs/ARCHITECTURE.md) để hiểu chi tiết về kiến trúc hệ thống.

## License

MIT License - xem file [LICENSE](LICENSE) để biết chi tiết. 