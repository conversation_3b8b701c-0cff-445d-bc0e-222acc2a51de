use crate::{
    container::ServiceContainer,
    database::Database,
    modules::{
        permission::{models::domain::Permission, traits::service_trait::PermissionServiceTrait},
        role::{models::domain::Role, traits::service_trait::RoleServiceTrait},
        user::{models::domain::User, traits::service_traits::UserServiceTrait},
    },
};
use std::sync::Arc;

/// Initialize database with default data
pub async fn initialize_database(database: Database) -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Initializing database with default data...");

    // Create service container
    let config =
        crate::config::Config::from_env().map_err(|e| format!("Failed to load config: {e}"))?;
    let container = ServiceContainer::new(database, config)
        .await
        .map_err(|e| format!("Failed to create service container: {e}"))?;
    let container = Arc::new(container);

    initialize_database_with_container(container).await
}

/// Initialize database with default data using existing ServiceContainer
pub async fn initialize_database_with_container(
    container: Arc<ServiceContainer>,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Initializing database with default data...");

    let mut has_errors = false;

    // Initialize permissions first (no dependencies)
    println!("📝 Creating permissions...");
    let _permissions = match initialize_permissions(&container.permission_service()).await {
        Ok(permissions) => {
            if permissions.is_empty() {
                println!("⚠️ No new permissions created (may already exist)");
            } else {
                println!("✅ Created {} new permissions", permissions.len());
            }
            permissions
        }
        Err(e) => {
            eprintln!("❌ Permission initialization failed: {e}");
            has_errors = true;
            Vec::new()
        }
    };

    // Small delay to ensure permissions are fully committed
    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

    // Initialize roles with permissions
    println!("👑 Creating roles and assigning permissions...");
    let _roles = match initialize_roles_with_permissions(
        &container.role_service(),
        &container.permission_service(),
    )
    .await
    {
        Ok(roles) => {
            if roles.is_empty() {
                println!("⚠️ No new roles created (may already exist)");
            } else {
                println!("✅ Created {} new roles", roles.len());
            }
            roles
        }
        Err(e) => {
            eprintln!("❌ Role initialization failed: {e}");
            has_errors = true;
            Vec::new()
        }
    };

    // Initialize super admin user with role assignment
    match initialize_super_admin_with_role(
        container.user_service().as_ref(),
        &container.role_service(),
    )
    .await
    {
        Ok(super_admin) => {
            println!("✅ Super admin ready: {}", super_admin.email);
        }
        Err(e) => {
            eprintln!("❌ Super admin initialization failed: {e}");
            has_errors = true;
        }
    }

    if has_errors {
        println!("⚠️ Database initialization completed with some warnings");
        return Err("Some initialization steps failed".into());
    } else {
        println!("🎉 Database initialization completed successfully!");
    }

    Ok(())
}

/// Create all required permissions (excluding public endpoints)
async fn initialize_permissions(
    permission_service: &Arc<dyn PermissionServiceTrait>,
) -> Result<Vec<Permission>, Box<dyn std::error::Error>> {
    let permissions_data = vec![
        // User Management Permissions
        ("users:create", "Create new users"),
        ("users:read", "View user details and list users"),
        ("users:update", "Update user information"),
        ("users:delete", "Delete users"),
        // Role Management Permissions (RBAC permissions for moderator)
        ("roles:create", "Create new roles"),
        ("roles:read", "View role details and list roles"),
        ("roles:update", "Update role information"),
        ("roles:delete", "Delete roles"),
        // Permission Management Permissions (RBAC permissions for moderator)
        ("permissions:create", "Create new permissions"),
        (
            "permissions:read",
            "View permission details and list permissions",
        ),
        ("permissions:update", "Update permission information"),
        ("permissions:delete", "Delete permissions"),
        // Profile Management Permissions (basic for member)
        ("profile:read", "View own profile"),
        ("profile:update", "Update own profile"),
        // System Administration Permissions
        ("admin:all", "Full administrative access - Super Admin only"),
        // Laptop Management Permissions
        ("laptops:create", "Create new laptop entries"),
        ("laptops:read", "View laptop details and list laptops"),
        ("laptops:update", "Update laptop information"),
        ("laptops:delete", "Delete laptops"),
        (
            "laptops:publish",
            "Publish laptops (change status to published)",
        ),
        ("laptops:feature", "Mark laptops as featured"),
        // Category Management Permissions
        ("categories:create", "Create new categories"),
        (
            "categories:read",
            "View category details and list categories",
        ),
        ("categories:update", "Update category information"),
        ("categories:delete", "Delete categories"),
        // Price Management Permissions
        ("prices:create", "Create price entries"),
        ("prices:read", "View price information"),
        ("prices:update", "Update price information"),
        ("prices:delete", "Delete price entries"),
        // Specification Management Permissions
        ("specs:create", "Create specification entries"),
        ("specs:read", "View specification details"),
        ("specs:update", "Update specification information"),
        ("specs:delete", "Delete specification entries"),
        // Note: Removed public permissions (auth.*, system.health) as they don't require authentication
    ];

    // Create permissions concurrently using futures::future::join_all
    let permission_futures: Vec<_> = permissions_data
        .into_iter()
        .map(|(name, description)| {
            let permission_service = permission_service.clone();
            async move {
                // Check if permission already exists
                if let Ok(_existing) = permission_service.get_permission_by_name(name).await {
                    println!("⚠️ Permission '{name}' already exists, skipping");
                    return None;
                }

                let permission = Permission::new(name.to_string(), Some(description.to_string()));

                match permission_service
                    .create_permission_direct(permission.clone())
                    .await
                {
                    Ok(created) => {
                        println!("📝 Created permission: {name}");
                        Some(created)
                    }
                    Err(e) => {
                        eprintln!("❌ Failed to create permission '{name}': {e}");
                        None
                    }
                }
            }
        })
        .collect();

    // Execute all permission creation tasks concurrently
    let results = futures::future::join_all(permission_futures).await;

    // Collect successful results
    let created_permissions: Vec<Permission> =
        results.into_iter().filter_map(|result| result).collect();

    Ok(created_permissions)
}

/// Create default roles and assign permissions to them
async fn initialize_roles_with_permissions(
    role_service: &Arc<dyn RoleServiceTrait>,
    permission_service: &Arc<dyn PermissionServiceTrait>,
) -> Result<Vec<Role>, Box<dyn std::error::Error>> {
    let roles_data = vec![
        (
            "super_admin",
            "Super Administrator - Full system access",
            vec![
                "admin:all", // Super admin has the special admin:all permission
            ],
        ),
        (
            "moderator",
            "Moderator - User and content management with RBAC permissions",
            vec![
                "users:read",
                "users:update", // Can view and edit users, but not create/delete
                "roles:create",
                "roles:read",
                "roles:update",
                "roles:delete", // Full RBAC access
                "permissions:create",
                "permissions:read",
                "permissions:update",
                "permissions:delete", // Full RBAC access
                "profile:read",
                "profile:update",
            ],
        ),
        (
            "content_manager",
            "Content Manager - Full laptop content management with publish rights",
            vec![
                "laptops:create",
                "laptops:read",
                "laptops:update",
                "laptops:publish",
                "laptops:feature",
                "categories:create",
                "categories:read",
                "categories:update",
                "prices:create",
                "prices:read",
                "prices:update",
                "specs:create",
                "specs:read",
                "specs:update",
                "profile:read",
                "profile:update",
            ],
        ),
        (
            "content_editor",
            "Content Editor - Laptop content editing without publish rights",
            vec![
                "laptops:create",
                "laptops:read",
                "laptops:update",
                "categories:read",
                "prices:create",
                "prices:read",
                "prices:update",
                "specs:create",
                "specs:read",
                "specs:update",
                "profile:read",
                "profile:update",
            ],
        ),
        (
            "member",
            "Regular Member - Basic profile access and laptop viewing",
            vec![
                "laptops:read",
                "categories:read",
                "prices:read",
                "specs:read",
                "profile:read",
                "profile:update",
            ],
        ),
    ];

    let mut created_roles = Vec::new();

    for (name, description, permission_names) in roles_data {
        // Check if role already exists
        let role = if let Ok(existing) = role_service.get_role_by_name(name).await {
            println!("⚠️ Role '{name}' already exists, skipping creation");
            existing
        } else {
            let new_role = Role::new(name.to_string(), Some(description.to_string()));

            match role_service.create_role_direct(new_role.clone()).await {
                Ok(created) => {
                    println!("👑 Created role: {name}");
                    created
                }
                Err(e) => {
                    eprintln!("❌ Failed to create role '{name}': {e}");
                    continue;
                }
            }
        };

        // Collect all permission IDs first, then assign in batch
        let mut permission_ids = Vec::new();
        let mut missing_permissions = Vec::new();

        for permission_name in permission_names {
            // Retry mechanism for permission lookup (in case of timing issues)
            let mut attempts = 0;
            let max_attempts = 3;
            let mut permission_found = false;

            while attempts < max_attempts && !permission_found {
                match permission_service
                    .get_permission_by_name(permission_name)
                    .await
                {
                    Ok(permission) => {
                        permission_ids.push(permission.id);
                        permission_found = true;
                    }
                    Err(_) if attempts < max_attempts - 1 => {
                        attempts += 1;
                        println!(
                            "⏳ Permission '{permission_name}' not found, retrying... (attempt {}/{})",
                            attempts, max_attempts
                        );
                        // Small delay to allow for database consistency
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                    Err(e) => {
                        eprintln!(
                            "❌ Permission '{permission_name}' not found for role '{name}' after {} attempts: {e}",
                            max_attempts
                        );
                        missing_permissions.push(permission_name);
                        break;
                    }
                }
            }
        }

        // Batch assign all permissions at once instead of individual assignments
        if !permission_ids.is_empty() {
            match role_service
                .assign_permissions_to_role(&role.id, &permission_ids)
                .await
            {
                Ok(()) => {
                    println!(
                        "🔗 Batch assigned {} permissions to role '{name}'",
                        permission_ids.len()
                    );
                }
                Err(e) => {
                    eprintln!("❌ Failed to batch assign permissions to role '{name}': {e}");
                    // Fallback to individual assignment if batch fails
                    for permission_id in permission_ids {
                        if let Err(e) = role_service
                            .assign_permission_to_role_without_cache_invalidation(
                                &role.id,
                                &permission_id,
                            )
                            .await
                        {
                            eprintln!("❌ Failed to assign permission to role '{name}': {e}");
                        }
                    }
                }
            }
        }

        if !missing_permissions.is_empty() {
            eprintln!(
                "⚠️ Role '{name}' missing {} permissions: {:?}",
                missing_permissions.len(),
                missing_permissions
            );
        }

        created_roles.push(role);
    }

    Ok(created_roles)
}

/// Create the super admin user and assign super_admin role
async fn initialize_super_admin_with_role(
    user_service: &dyn UserServiceTrait,
    _role_service: &Arc<dyn RoleServiceTrait>,
) -> Result<User, Box<dyn std::error::Error>> {
    let super_admin_email = "<EMAIL>";
    let super_admin_username = "superadmin";

    // Get or create super admin user
    let super_admin_user_with_roles =
        if let Ok(existing) = user_service.get_user_by_email(super_admin_email).await {
            println!("⚠️ Super admin user '{super_admin_email}' already exists, skipping creation");
            // Get user with roles using ID
            user_service
                .get_user_by_id(&existing.id)
                .await
                .map_err(|e| anyhow::anyhow!("Failed to get existing user with roles: {}", e))?
        } else if let Ok(existing) = user_service
            .get_user_by_username(super_admin_username)
            .await
        {
            println!(
                "⚠️ Super admin username '{super_admin_username}' already exists, skipping creation"
            );
            // Get user with roles using ID
            user_service
                .get_user_by_id(&existing.id)
                .await
                .map_err(|e| anyhow::anyhow!("Failed to get existing user with roles: {}", e))?
        } else {
            // DO NOT hash the password here. The UserService is responsible for hashing.
            let create_request = crate::modules::user::models::CreateUserRequest {
                email: super_admin_email.to_string(),
                username: super_admin_username.to_string(),
                fullname: "Super Administrator".to_string(),
                password: "SuperAdmin123!".to_string(), // Pass raw password
                role_names: Some(vec!["super_admin".to_string()]),
            };

            let created_user_with_roles = user_service
                .create_user(create_request)
                .await
                .map_err(|e| anyhow::anyhow!("Failed to create super admin user: {}", e))?;

            // Print creation info
            println!("👤 Created super admin user:");
            println!("   📧 Email: {super_admin_email}");
            println!("   👤 Username: {super_admin_username}");
            println!("   🔑 Password: SuperAdmin123!");
            println!("   ⚠️  IMPORTANT: Change this password after first login!");

            created_user_with_roles
        };

    // The role assignment is already handled in UserService.create_user() when roles are specified
    // So we don't need to assign role again here for created users

    Ok(super_admin_user_with_roles.to_user())
}

/// Initialize specific test data for development
pub async fn initialize_test_data(database: Database) -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Initializing test data...");

    let config =
        crate::config::Config::from_env().map_err(|e| format!("Failed to load config: {e}"))?;
    let container = ServiceContainer::new(database, config)
        .await
        .map_err(|e| format!("Failed to create service container: {e}"))?;
    let container = Arc::new(container);

    // Create test users
    let test_users = vec![
        ("<EMAIL>", "johndoe", "John Doe"),
        ("<EMAIL>", "janesmith", "Jane Smith"),
        ("<EMAIL>", "bobwilson", "Bob Wilson"),
    ];

    for (email, username, fullname) in test_users {
        let create_request = crate::modules::user::models::CreateUserRequest {
            email: email.to_string(),
            username: username.to_string(),
            fullname: fullname.to_string(),
            password: "TestPass123!".to_string(), // Pass raw password
            role_names: None,                     // Will be automatically assigned as member
        };

        match container.user_service().create_user(create_request).await {
            Ok(_) => println!("👤 Created test user: {email}"),
            Err(e) => eprintln!("❌ Failed to create test user '{email}': {e}"),
        }
    }

    println!("✅ Test data initialization completed!");
    Ok(())
}
