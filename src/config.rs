use crate::modules::email::EmailConfig;
use anyhow::Result;
use std::time::Duration;

#[derive(Debug, <PERSON>lone)]
pub struct Config {
    pub database_url: String,
    pub server_host: String,
    pub server_port: u16,
    pub jwt_secret: String,
    pub tokens: TokenConfig,
    pub database: DatabasePerformanceConfig,
    pub google_oauth: GoogleOAuthConfig,
    pub cors: CorsConfig,
    pub email: EmailConfig,
    pub redis: RedisConfig,
    pub cache: CacheConfig,
    pub cloudinary: CloudinaryConfig,
}

#[derive(Debug, Clone)]
pub struct TokenConfig {
    pub access_token_expiry_secs: i64,
    pub refresh_token_expiry_secs: i64,
}

impl Default for TokenConfig {
    fn default() -> Self {
        Self {
            access_token_expiry_secs: 3600,    // 1 hour
            refresh_token_expiry_secs: 604800, // 7 days
        }
    }
}

#[derive(Debug, <PERSON>lone)]
pub struct DatabasePerformanceConfig {
    pub max_connections: u32,
    pub min_idle_connections: Option<u32>,
    pub connection_timeout_secs: u64,
    pub idle_timeout_secs: Option<u64>,
    pub max_lifetime_secs: Option<u64>,
}

impl Default for DatabasePerformanceConfig {
    fn default() -> Self {
        Self {
            max_connections: 16,
            min_idle_connections: Some(4),
            connection_timeout_secs: 10,
            idle_timeout_secs: Some(300),  // 5 minutes
            max_lifetime_secs: Some(1800), // 30 minutes
        }
    }
}

impl DatabasePerformanceConfig {
    pub fn to_database_config(&self, url: String) -> crate::database::DatabaseConfig {
        crate::database::DatabaseConfig {
            url,
            max_connections: self.max_connections,
            min_connections: self.min_idle_connections,
            connection_timeout: Duration::from_secs(self.connection_timeout_secs),
            idle_timeout: self.idle_timeout_secs.map(Duration::from_secs),
            max_lifetime: self.max_lifetime_secs.map(Duration::from_secs),
            test_before_acquire: true, // PostgreSQL benefits from connection testing
        }
    }
}

#[derive(Debug, Clone)]
pub struct GoogleOAuthConfig {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_url: String,
    pub token_url: String,
}

impl Default for GoogleOAuthConfig {
    fn default() -> Self {
        Self {
            client_id: String::new(),
            client_secret: String::new(),
            redirect_uri: "http://localhost:3000/callback".to_string(), // Frontend URL instead of backend
            auth_url: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_url: "https://www.googleapis.com/oauth2/v4/token".to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct CorsConfig {
    pub allowed_origins: Vec<String>,
    pub allowed_headers: Vec<String>,
    pub allow_credentials: bool,
}

#[derive(Debug, Clone)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
    pub connection_timeout_secs: u64,
    pub idle_timeout_secs: u64,
    pub max_lifetime_secs: u64,
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self {
            url: "redis://127.0.0.1:6379".to_string(),
            max_connections: 16,
            connection_timeout_secs: 10,
            idle_timeout_secs: 300,
            max_lifetime_secs: 1800,
        }
    }
}

#[derive(Debug, Clone)]
pub struct CloudinaryConfig {
    pub cloud_name: String,
    pub api_key: String,
    pub api_secret: String,
    pub upload_preset: Option<String>,
    pub secure: bool,
}

impl Default for CloudinaryConfig {
    fn default() -> Self {
        Self {
            cloud_name: String::new(),
            api_key: String::new(),
            api_secret: String::new(),
            upload_preset: None,
            secure: true,
        }
    }
}

impl CloudinaryConfig {
    /// Validate Cloudinary configuration
    pub fn validate(&self) -> Result<()> {
        if self.cloud_name.is_empty() {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_CLOUD_NAME is required but not set. Please set this environment variable."
            ));
        }

        if self.api_key.is_empty() {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_API_KEY is required but not set. Please set this environment variable."
            ));
        }

        if self.api_secret.is_empty() {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_API_SECRET is required but not set. Please set this environment variable."
            ));
        }

        // Validate cloud_name format (basic check)
        if !self
            .cloud_name
            .chars()
            .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
        {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_CLOUD_NAME contains invalid characters. Only alphanumeric, hyphens, and underscores are allowed."
            ));
        }

        // Validate API key format (should be numeric)
        if !self.api_key.chars().all(|c| c.is_ascii_digit()) {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_API_KEY should contain only digits."
            ));
        }

        // Validate API secret length (basic security check)
        if self.api_secret.len() < 20 {
            return Err(anyhow::anyhow!(
                "CLOUDINARY_API_SECRET appears to be too short. Please check your configuration."
            ));
        }

        tracing::info!(
            "Cloudinary configuration validated successfully for cloud: {}",
            self.cloud_name
        );

        Ok(())
    }

    /// Check if Cloudinary is properly configured
    pub fn is_configured(&self) -> bool {
        !self.cloud_name.is_empty() && !self.api_key.is_empty() && !self.api_secret.is_empty()
    }
}

#[derive(Debug, Clone)]
pub struct CacheConfig {
    pub permission_version_ttl_secs: u64,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            permission_version_ttl_secs: 3600, // 1 hour
        }
    }
}

impl CacheConfig {
    pub fn permission_version_ttl(&self) -> Duration {
        Duration::from_secs(self.permission_version_ttl_secs)
    }
}

impl Default for CorsConfig {
    fn default() -> Self {
        Self {
            allowed_origins: vec![
                "http://localhost:3000".to_string(),
                "http://localhost:3001".to_string(),
                "http://localhost:8386".to_string(),
                "https://all-in-one-r.vercel.app".to_string(),
            ],
            allowed_headers: vec![
                "authorization".to_string(),
                "content-type".to_string(),
                "accept".to_string(),
                "x-api-type".to_string(),
            ],
            allow_credentials: true,
        }
    }
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenvy::dotenv().ok();

        let database_url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgresql://localhost/platform_rust".to_string());

        let server_host = std::env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string());

        let server_port = std::env::var("SERVER_PORT")
            .unwrap_or_else(|_| "8386".to_string())
            .parse::<u16>()?;

        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string());

        // Database performance configuration from environment
        let max_connections = std::env::var("DB_MAX_CONNECTIONS")
            .unwrap_or_else(|_| "16".to_string())
            .parse::<u32>()
            .unwrap_or(16);

        let min_idle_connections = std::env::var("DB_MIN_IDLE_CONNECTIONS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok());

        let connection_timeout_secs = std::env::var("DB_CONNECTION_TIMEOUT_SECS")
            .unwrap_or_else(|_| "10".to_string())
            .parse::<u64>()
            .unwrap_or(10);

        let idle_timeout_secs = std::env::var("DB_IDLE_TIMEOUT_SECS")
            .ok()
            .and_then(|s| s.parse::<u64>().ok());

        let max_lifetime_secs = std::env::var("DB_MAX_LIFETIME_SECS")
            .ok()
            .and_then(|s| s.parse::<u64>().ok());

        let database = DatabasePerformanceConfig {
            max_connections,
            min_idle_connections,
            connection_timeout_secs,
            idle_timeout_secs,
            max_lifetime_secs,
        };

        // Google OAuth configuration from environment
        let google_oauth = GoogleOAuthConfig {
            client_id: std::env::var("GOOGLE_OAUTH_CLIENT_ID").unwrap_or_else(|_| String::new()),
            client_secret: std::env::var("GOOGLE_OAUTH_CLIENT_SECRET")
                .unwrap_or_else(|_| String::new()),
            redirect_uri: std::env::var("FRONTEND_CALLBACK_URL")
                .or_else(|_| std::env::var("GOOGLE_OAUTH_REDIRECT_URI"))
                .unwrap_or_else(|_| "http://localhost:3000/callback".to_string()), // Frontend-first flow
            auth_url: std::env::var("GOOGLE_OAUTH_AUTH_URL")
                .unwrap_or_else(|_| "https://accounts.google.com/o/oauth2/v2/auth".to_string()),
            token_url: std::env::var("GOOGLE_OAUTH_TOKEN_URL")
                .unwrap_or_else(|_| "https://www.googleapis.com/oauth2/v4/token".to_string()),
        };

        // CORS configuration from environment
        let allowed_origins: Vec<String> = std::env::var("ALLOWED_ORIGINS")
            .unwrap_or_else(|_| {
                "http://localhost:3000,http://localhost:3001,http://localhost:8386".to_string()
            })
            .split(',')
            .map(|s| s.trim().to_string())
            .collect();

        let allowed_headers: Vec<String> = std::env::var("ALLOWED_HEADERS")
            .unwrap_or_else(|_| "authorization,content-type,accept,x-api-type".to_string())
            .split(',')
            .map(|s| s.trim().to_string())
            .collect();

        let allow_credentials = std::env::var("ALLOW_CREDENTIALS")
            .unwrap_or_else(|_| "true".to_string())
            .parse::<bool>()
            .unwrap_or(true);

        let cors = CorsConfig {
            allowed_origins,
            allowed_headers,
            allow_credentials,
        };

        // Email configuration from environment
        let email = EmailConfig {
            smtp_host: std::env::var("SMTP_HOST").unwrap_or_else(|_| "localhost".to_string()),
            smtp_port: std::env::var("SMTP_PORT")
                .unwrap_or_else(|_| "587".to_string())
                .parse::<u16>()
                .unwrap_or(587),
            smtp_username: std::env::var("SMTP_USERNAME").unwrap_or_else(|_| String::new()),
            smtp_password: std::env::var("SMTP_PASSWORD").unwrap_or_else(|_| String::new()),
            from_email: std::env::var("FROM_EMAIL")
                .unwrap_or_else(|_| "<EMAIL>".to_string()),
            from_name: std::env::var("FROM_NAME").unwrap_or_else(|_| "Platform Rust".to_string()),
            use_tls: std::env::var("SMTP_USE_TLS")
                .unwrap_or_else(|_| "true".to_string())
                .parse::<bool>()
                .unwrap_or(true),
        };

        // Token configuration from environment
        let tokens = TokenConfig {
            access_token_expiry_secs: std::env::var("ACCESS_TOKEN_EXPIRY_SECS")
                .unwrap_or_else(|_| "3600".to_string())
                .parse::<i64>()
                .unwrap_or(3600),
            refresh_token_expiry_secs: std::env::var("REFRESH_TOKEN_EXPIRY_SECS")
                .unwrap_or_else(|_| "604800".to_string())
                .parse::<i64>()
                .unwrap_or(604800),
        };

        // Redis configuration from environment
        let redis = RedisConfig {
            url: std::env::var("REDIS_URL")
                .unwrap_or_else(|_| "redis://:123456789@103.103.103.103:6379".to_string()),
            max_connections: std::env::var("REDIS_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "16".to_string())
                .parse::<u32>()
                .unwrap_or(16),
            connection_timeout_secs: std::env::var("REDIS_CONNECTION_TIMEOUT_SECS")
                .unwrap_or_else(|_| "10".to_string())
                .parse::<u64>()
                .unwrap_or(10),
            idle_timeout_secs: std::env::var("REDIS_IDLE_TIMEOUT_SECS")
                .unwrap_or_else(|_| "300".to_string())
                .parse::<u64>()
                .unwrap_or(300),
            max_lifetime_secs: std::env::var("REDIS_MAX_LIFETIME_SECS")
                .unwrap_or_else(|_| "1800".to_string())
                .parse::<u64>()
                .unwrap_or(1800),
        };

        // Cache configuration from environment
        let cache = CacheConfig {
            permission_version_ttl_secs: std::env::var("CACHE_PERMISSION_VERSION_TTL_SECS")
                .unwrap_or_else(|_| "3600".to_string())
                .parse::<u64>()
                .unwrap_or(3600),
        };

        // Cloudinary configuration from environment
        let cloudinary = CloudinaryConfig {
            cloud_name: std::env::var("CLOUDINARY_CLOUD_NAME").unwrap_or_else(|_| String::new()),
            api_key: std::env::var("CLOUDINARY_API_KEY").unwrap_or_else(|_| String::new()),
            api_secret: std::env::var("CLOUDINARY_API_SECRET").unwrap_or_else(|_| String::new()),
            upload_preset: std::env::var("CLOUDINARY_UPLOAD_PRESET").ok(),
            secure: std::env::var("CLOUDINARY_SECURE")
                .unwrap_or_else(|_| "true".to_string())
                .parse::<bool>()
                .unwrap_or(true),
        };

        // Validate Cloudinary configuration
        cloudinary.validate()?;

        Ok(Self {
            database_url,
            server_host,
            server_port,
            jwt_secret,
            tokens,
            database,
            google_oauth,
            cors,
            email,
            redis,
            cache,
            cloudinary,
        })
    }

    pub fn server_address(&self) -> String {
        format!("{}:{}", self.server_host, self.server_port)
    }
}
