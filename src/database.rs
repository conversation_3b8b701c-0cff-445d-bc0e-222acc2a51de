use anyhow::Result;
use sqlx::{PgConnection, PgPool, postgres::PgPoolOptions};
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};

pub type DbPool = PgPool;
pub type DbConnection = PgConnection;

// OPTIMIZATION: Connection monitoring structures
#[derive(Debug, <PERSON>lone)]
pub struct ConnectionMetrics {
    pub total_connections: u64,
    pub active_connections: u64,
    pub pool_connections: u32,
    pub pool_idle_connections: u32,
    pub avg_wait_time_us: u64,
}

#[derive(Debug, Clone)]
pub enum PoolAdjustment {
    Increase,
    Decrease,
}

#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: Option<u32>,
    pub connection_timeout: Duration,
    pub idle_timeout: Option<Duration>,
    pub max_lifetime: Option<Duration>,
    pub test_before_acquire: bool,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "postgresql://localhost/platform_rust".to_string(),
            max_connections: 20,      // Optimized for high concurrency
            min_connections: Some(5), // Higher minimum for better response times
            connection_timeout: Duration::from_secs(10),
            idle_timeout: Some(Duration::from_secs(600)), // 10 minutes
            max_lifetime: Some(Duration::from_secs(3600)), // 1 hour
            test_before_acquire: true, // PostgreSQL benefits from connection testing
        }
    }
}

#[derive(Debug)]
pub struct Database {
    pool: DbPool,
    // OPTIMIZATION: Connection monitoring for adaptive sizing
    total_connections: AtomicU64,
    active_connections: AtomicU64,
    connection_wait_time: AtomicU64, // in microseconds
}

impl Clone for Database {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            total_connections: AtomicU64::new(self.total_connections.load(Ordering::Relaxed)),
            active_connections: AtomicU64::new(self.active_connections.load(Ordering::Relaxed)),
            connection_wait_time: AtomicU64::new(self.connection_wait_time.load(Ordering::Relaxed)),
        }
    }
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let config = DatabaseConfig {
            url: database_url.to_string(),
            ..DatabaseConfig::default()
        };
        Self::with_config(config).await
    }

    pub async fn with_config(config: DatabaseConfig) -> Result<Self> {
        let mut pool_builder = PgPoolOptions::new()
            .max_connections(config.max_connections)
            .test_before_acquire(config.test_before_acquire);

        if let Some(min_connections) = config.min_connections {
            pool_builder = pool_builder.min_connections(min_connections);
        }

        if let Some(idle_timeout) = config.idle_timeout {
            pool_builder = pool_builder.idle_timeout(idle_timeout);
        }

        if let Some(max_lifetime) = config.max_lifetime {
            pool_builder = pool_builder.max_lifetime(max_lifetime);
        }

        // Build pool
        let pool = pool_builder.connect(&config.url).await?;

        // Test connection to ensure PostgreSQL is accessible
        sqlx::query("SELECT 1").execute(&pool).await?;
        tracing::info!("✅ PostgreSQL connection established successfully");

        Ok(Self {
            pool,
            total_connections: AtomicU64::new(0),
            active_connections: AtomicU64::new(0),
            connection_wait_time: AtomicU64::new(0),
        })
    }

    pub async fn migrate(&self) -> Result<()> {
        sqlx::migrate!("./sqlx-migrations").run(&self.pool).await?;
        tracing::info!("✅ Database migrations completed successfully");
        Ok(())
    }

    pub fn pool(&self) -> &DbPool {
        &self.pool
    }

    pub async fn conn(&self) -> Result<sqlx::pool::PoolConnection<sqlx::Postgres>> {
        let start = Instant::now();
        self.total_connections.fetch_add(1, Ordering::Relaxed);
        self.active_connections.fetch_add(1, Ordering::Relaxed);

        let result = self.pool.acquire().await;

        // Update metrics
        let wait_time = start.elapsed().as_micros() as u64;
        self.connection_wait_time
            .fetch_add(wait_time, Ordering::Relaxed);

        if result.is_ok() {
            // Connection acquired successfully
            tracing::debug!("Database connection acquired in {}μs", wait_time);
        } else {
            // Connection failed, decrement active count
            self.active_connections.fetch_sub(1, Ordering::Relaxed);
        }

        result.map_err(|e| anyhow::anyhow!("Pool error: {}", e))
    }

    /// Fast connection check for health endpoint
    pub async fn is_healthy(&self) -> bool {
        sqlx::query("SELECT 1").execute(&self.pool).await.is_ok()
    }

    /// Get pool status for monitoring
    pub fn pool_status(&self) -> u32 {
        self.pool.size()
    }

    // OPTIMIZATION: Enhanced connection monitoring
    pub fn connection_metrics(&self) -> ConnectionMetrics {
        let stats = self.pool.size();
        ConnectionMetrics {
            total_connections: self.total_connections.load(Ordering::Relaxed),
            active_connections: self.active_connections.load(Ordering::Relaxed),
            pool_connections: stats,
            pool_idle_connections: 0,
            avg_wait_time_us: self.connection_wait_time.load(Ordering::Relaxed),
        }
    }

    // OPTIMIZATION: Adaptive connection sizing based on metrics
    pub fn should_adjust_pool_size(&self) -> Option<PoolAdjustment> {
        let metrics = self.connection_metrics();
        let avg_wait_time_ms = metrics.avg_wait_time_us / 1000;

        // If average wait time > 100ms, consider increasing pool size
        if avg_wait_time_ms > 100 && metrics.pool_connections < 50 {
            Some(PoolAdjustment::Increase)
        }
        // If many idle connections and low wait time, consider decreasing
        else if avg_wait_time_ms < 10 && metrics.pool_idle_connections > 10 {
            Some(PoolAdjustment::Decrease)
        } else {
            None
        }
    }

    /// Async transaction helper with improved error handling  
    pub async fn transaction<F, R, E>(&self, f: F) -> Result<R, E>
    where
        F: for<'a> FnOnce(
            &'a mut sqlx::Transaction<'_, sqlx::Postgres>,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R, E>> + Send + 'a>,
        >,
        R: Send + 'static,
        E: From<sqlx::Error> + From<anyhow::Error> + Send + 'static + std::fmt::Display,
    {
        let mut tx = self
            .pool
            .begin()
            .await
            .map_err(|e| E::from(anyhow::anyhow!("Failed to begin transaction: {}", e)))?;

        let result = f(&mut tx).await;

        match result {
            Ok(value) => {
                tx.commit()
                    .await
                    .map_err(|e| E::from(anyhow::anyhow!("Failed to commit transaction: {}", e)))?;
                Ok(value)
            }
            Err(e) => {
                tx.rollback().await.map_err(|rollback_e| {
                    E::from(anyhow::anyhow!(
                        "Failed to rollback transaction: {} (original error: {})",
                        rollback_e,
                        e
                    ))
                })?;
                Err(e)
            }
        }
    }

    /// Create a test database instance for unit testing
    #[cfg(test)]
    pub fn new_for_tests() -> Self {
        // For tests, we create a minimal database instance
        // In a real scenario, you might want to use an in-memory database
        // or a test-specific database URL
        let config = DatabaseConfig {
            url: "postgresql://localhost/platform_rust_test".to_string(),
            max_connections: 1,
            min_connections: None,
            connection_timeout: Duration::from_secs(5),
            idle_timeout: None,
            max_lifetime: None,
            test_before_acquire: false,
        };

        // This is a blocking call for tests
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(async { Self::with_config(config).await.unwrap() })
    }
}
