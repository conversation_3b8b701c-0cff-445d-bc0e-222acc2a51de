use crate::{
    config::Config,
    database::Database,
    modules::{
        email::EmailEventBus,
        redis::{RedisManager, RedisService, RedisServiceTrait},
    },
};
use anyhow::Result;
use std::sync::Arc;

/// Builder for ServiceContainer to simplify construction and improve testability
pub struct ServiceContainerBuilder {
    database: Option<Database>,
    config: Option<Config>,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
    email_event_bus: Option<Arc<EmailEventBus>>,
}

impl ServiceContainerBuilder {
    /// Create a new builder
    pub fn new() -> Self {
        Self {
            database: None,
            config: None,
            redis_service: None,
            email_event_bus: None,
        }
    }

    /// Set the database
    pub fn with_database(mut self, database: Database) -> Self {
        self.database = Some(database);
        self
    }

    /// Set the configuration
    pub fn with_config(mut self, config: Config) -> Self {
        self.config = Some(config);
        self
    }

    /// Set the Redis service
    pub fn with_redis_service(mut self, redis_service: Arc<dyn RedisServiceTrait>) -> Self {
        self.redis_service = Some(redis_service);
        self
    }

    /// Set the email event bus
    pub fn with_email_event_bus(mut self, email_event_bus: Arc<EmailEventBus>) -> Self {
        self.email_event_bus = Some(email_event_bus);
        self
    }

    /// Initialize Redis service from config
    pub async fn with_redis_from_config(mut self) -> Result<Self> {
        let config = self
            .config
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Config must be set before initializing Redis"))?;

        tracing::info!("Initializing Redis service...");
        let redis_manager = RedisManager::new(&config.redis).await?;
        let redis_service: Arc<dyn RedisServiceTrait> = Arc::new(RedisService::new(redis_manager));
        tracing::info!("Redis service initialized successfully");

        self.redis_service = Some(redis_service);
        Ok(self)
    }

    /// Initialize email event bus with Redis
    pub fn with_email_event_bus_from_redis(mut self) -> Result<Self> {
        let redis_service = self.redis_service.as_ref().ok_or_else(|| {
            anyhow::anyhow!("Redis service must be set before creating email event bus")
        })?;

        let email_event_bus = Arc::new(EmailEventBus::new(redis_service.clone()));
        self.email_event_bus = Some(email_event_bus);
        Ok(self)
    }

    /// Build the ServiceContainer
    pub async fn build(self) -> Result<super::ServiceContainer> {
        let database = self
            .database
            .ok_or_else(|| anyhow::anyhow!("Database is required"))?;
        let config = self
            .config
            .ok_or_else(|| anyhow::anyhow!("Config is required"))?;
        let redis_service = self
            .redis_service
            .ok_or_else(|| anyhow::anyhow!("Redis service is required"))?;
        let email_event_bus = self
            .email_event_bus
            .ok_or_else(|| anyhow::anyhow!("Email event bus is required"))?;

        super::ServiceContainer::with_services(database, config, redis_service, email_event_bus)
            .await
    }

    /// Build with fallback email event bus if Redis is not available
    pub async fn build_with_fallback(self) -> Result<super::ServiceContainer> {
        let database = self
            .database
            .ok_or_else(|| anyhow::anyhow!("Database is required"))?;
        let config = self
            .config
            .ok_or_else(|| anyhow::anyhow!("Config is required"))?;

        match (self.redis_service, self.email_event_bus) {
            (Some(redis_service), Some(email_event_bus)) => {
                super::ServiceContainer::with_services(
                    database,
                    config,
                    redis_service,
                    email_event_bus,
                )
                .await
            }
            (Some(redis_service), None) => {
                let email_event_bus = Arc::new(EmailEventBus::new(redis_service.clone()));
                super::ServiceContainer::with_services(
                    database,
                    config,
                    redis_service,
                    email_event_bus,
                )
                .await
            }
            _ => {
                tracing::warn!("Redis service not available, creating fallback email event bus");
                // Create a mock Redis service for fallback
                let mock_redis = Arc::new(MockRedisService::new());
                let email_event_bus = Arc::new(EmailEventBus::new(mock_redis.clone()));
                super::ServiceContainer::with_services(
                    database,
                    config,
                    mock_redis,
                    email_event_bus,
                )
                .await
            }
        }
    }
}

impl Default for ServiceContainerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Mock Redis service for fallback scenarios
struct MockRedisService;

impl MockRedisService {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl RedisServiceTrait for MockRedisService {
    async fn set(
        &self,
        _key: &str,
        _value: &str,
        _expiration: Option<std::time::Duration>,
    ) -> Result<()> {
        tracing::warn!("MockRedisService: set operation ignored (Redis not available)");
        Ok(())
    }

    async fn get(&self, _key: &str) -> Result<Option<String>> {
        Ok(None)
    }

    async fn delete(&self, _key: &str) -> Result<bool> {
        Ok(false)
    }

    async fn exists(&self, _key: &str) -> Result<bool> {
        Ok(false)
    }

    async fn expire(&self, _key: &str, _expiration: std::time::Duration) -> Result<bool> {
        Ok(false)
    }

    async fn ttl(&self, _key: &str) -> Result<i64> {
        Ok(-1)
    }

    async fn increment(&self, _key: &str, _delta: i64) -> Result<i64> {
        Ok(_delta)
    }

    async fn hset(&self, _key: &str, _field: &str, _value: &str) -> Result<bool> {
        Ok(false)
    }

    async fn hget(&self, _key: &str, _field: &str) -> Result<Option<String>> {
        Ok(None)
    }

    async fn hdel(&self, _key: &str, _field: &str) -> Result<bool> {
        Ok(false)
    }

    async fn hgetall(&self, _key: &str) -> Result<Vec<(String, String)>> {
        Ok(vec![])
    }

    async fn lpush(&self, _key: &str, _value: &str) -> Result<i64> {
        Ok(0)
    }

    async fn rpush(&self, _key: &str, _value: &str) -> Result<i64> {
        Ok(0)
    }

    async fn lpop(&self, _key: &str) -> Result<Option<String>> {
        Ok(None)
    }

    async fn rpop(&self, _key: &str) -> Result<Option<String>> {
        Ok(None)
    }

    async fn llen(&self, _key: &str) -> Result<i64> {
        Ok(0)
    }

    async fn lrange(&self, _key: &str, _start: i64, _stop: i64) -> Result<Vec<String>> {
        Ok(vec![])
    }

    async fn brpop(&self, _key: &str, _timeout_secs: u32) -> Result<Option<String>> {
        Ok(None)
    }

    async fn sadd(&self, _key: &str, _value: &str) -> Result<bool> {
        Ok(false)
    }

    async fn srem(&self, _key: &str, _value: &str) -> Result<bool> {
        Ok(false)
    }

    async fn sismember(&self, _key: &str, _value: &str) -> Result<bool> {
        Ok(false)
    }

    async fn smembers(&self, _key: &str) -> Result<Vec<String>> {
        Ok(vec![])
    }

    async fn ping(&self) -> Result<String> {
        Ok("PONG".to_string())
    }

    async fn flushdb(&self) -> Result<()> {
        Ok(())
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(false)
    }
}
