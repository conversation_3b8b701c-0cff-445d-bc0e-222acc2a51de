use std::sync::Arc;

use crate::modules::{
    auth::service_trait::AuthServiceTrait, category::service_trait::CategoryServiceTrait,
    laptop::service_trait::LaptopManagementServiceTrait,
    permission::service_trait::PermissionServiceTrait, redis::service::RedisServiceTrait,
    role::service_trait::RoleServiceTrait, user::traits::service_traits::UserServiceTrait,
};

/// Service registry trait for dependency injection (Interface Segregation Principle)
pub trait ServiceRegistry: Send + Sync {
    fn get_user_service(&self) -> Arc<dyn UserServiceTrait>;
    fn get_auth_service(&self) -> Arc<dyn AuthServiceTrait>;
    fn get_permission_service(&self) -> Arc<dyn PermissionServiceTrait>;
    fn get_role_service(&self) -> Arc<dyn RoleServiceTrait>;
    fn get_category_service(&self) -> Arc<dyn CategoryServiceTrait>;
    fn get_laptop_management_service(&self) -> Arc<dyn LaptopManagementServiceTrait>;
    fn get_redis_service(&self) -> Option<Arc<dyn RedisServiceTrait>>;
}
