use crate::{
    config::Config,
    container::service_container_traits::ServiceRegistry,
    database::Database,
    modules::{
        auth::{
            GoogleOAuthService,
            oauth_providers::{GoogleOAuthProvider, OAuthProviderFactory},
            service::AuthService,
            service_trait::AuthServiceTrait,
        },
        category::{
            repository::SqlxCategoryRepository, service::CategoryService,
            service_trait::CategoryServiceTrait,
        },
        email::EmailEventBus,
        image::{service::ImageService, service::ImageServiceTrait},
        laptop::{service::LaptopManagementService, service_trait::LaptopManagementServiceTrait},
        permission::{service::PermissionService, service_trait::PermissionServiceTrait},
        progression::{
            exp_system::{
                DynExpSystemService, repository::SqlxExpSystemRepository, service::ExpSystemService,
            },
            title_system::{
                service::{DynTitleService, TitleService},
                sqlx_repository::SqlxTitleRepository,
            },
        },
        redis::{RedisManager, RedisService, RedisServiceTrait},
        role::{service::RoleService, service_trait::RoleServiceTrait},
        user::{
            database::repository::SqlxUserRepository,
            database::user_role_repository::SqlxUserRoleRepository, services::UserService,
            traits::service_traits::UserServiceTrait,
        },
    },
};
use std::sync::Arc;

/// Type alias for complex service tuple to reduce type complexity
type ServiceTuple = (
    Arc<dyn UserServiceTrait>,
    Arc<dyn AuthServiceTrait>,
    Arc<dyn PermissionServiceTrait>,
    Arc<dyn RoleServiceTrait>,
    Arc<dyn CategoryServiceTrait>,
    Arc<dyn ImageServiceTrait>,
    Arc<dyn LaptopManagementServiceTrait>,
    DynExpSystemService,
    DynTitleService,
    Arc<dyn RedisServiceTrait>,
);

/// Dependency Injection Container (implements DIP through ServiceRegistry trait)
#[derive(Clone)]
pub struct ServiceContainer {
    user_service: Arc<dyn UserServiceTrait>,
    auth_service: Arc<dyn AuthServiceTrait>,
    permission_service: Arc<dyn PermissionServiceTrait>,
    role_service: Arc<dyn RoleServiceTrait>,
    category_service: Arc<dyn CategoryServiceTrait>,
    image_service: Arc<dyn ImageServiceTrait>,
    laptop_management_service: Arc<dyn LaptopManagementServiceTrait>,
    exp_system_service: DynExpSystemService,
    title_service: DynTitleService,
    email_event_bus: Arc<EmailEventBus>,
    redis_service: Arc<dyn RedisServiceTrait>, // Now required, not optional
    database: Database,
    config: crate::config::Config,
}

impl ServiceContainer {
    /// Private helper to construct the service graph (DRY principle)
    async fn build_services(
        database: &Database,
        config: &Config,
        redis_service: Arc<dyn RedisServiceTrait>,
        email_event_bus: Arc<EmailEventBus>,
    ) -> ServiceTuple {
        // Repository layer
        let user_repo = Arc::new(SqlxUserRepository::new(database.clone()));
        let user_role_repo = Arc::new(SqlxUserRoleRepository::new(database.clone()));

        let exp_system_repo = Arc::new(SqlxExpSystemRepository::new(database.pool().clone()));
        let title_repo = Arc::new(SqlxTitleRepository::new(database.clone()));
        let category_repo = Arc::new(SqlxCategoryRepository::new(database.clone()));

        // Use SQLx repositories where converted
        let role_repo = Arc::new(crate::modules::role::repository::SqlxRoleRepository::new(
            database.pool().clone(),
        ));

        // Service layer
        let permission_service = Arc::new(PermissionService::new(database.clone()));
        let role_service = Arc::new(RoleService::new(
            role_repo,
            permission_service.clone(),
            Some(redis_service.clone()),
        ));
        let category_service: Arc<dyn CategoryServiceTrait> = Arc::new(CategoryService::new(
            category_repo,
            Some(redis_service.clone()),
        ));
        let image_service: Arc<dyn ImageServiceTrait> =
            Arc::new(ImageService::new(config.cloudinary.clone()));

        // Verify Cloudinary connection if configured
        if config.cloudinary.is_configured() {
            if let Err(e) = image_service.verify_connection().await {
                tracing::warn!("Cloudinary connection verification failed: {}", e);
                tracing::warn!("Image upload functionality may not work properly");
                // Don't fail startup, just warn - allow app to start without Cloudinary
            } else {
                tracing::info!("Cloudinary connection verified successfully");
            }
        } else {
            tracing::warn!(
                "Cloudinary not configured - image upload functionality will be disabled"
            );
        }
        let laptop_management_service: Arc<dyn LaptopManagementServiceTrait> =
            Arc::new(LaptopManagementService::new(
                database.clone(),
                category_service.clone(),
                Some(redis_service.clone()),
            ));
        let title_service: DynTitleService = Arc::new(TitleService::new(title_repo));

        let exp_system_service = Arc::new(ExpSystemService::new(
            exp_system_repo,
            user_repo.clone(),
            title_service.clone(),
            Some(redis_service.clone()),
        ));

        let user_service_impl = UserService::new(
            user_repo.clone(),
            user_role_repo,
            role_service.clone(),
            exp_system_service.clone(),
            database.clone(),
            Some(redis_service.clone()),
            config.cache.clone(),
        );
        let user_service: Arc<dyn UserServiceTrait> = Arc::new(user_service_impl);

        // --- OAuth Provider Setup ---
        let mut oauth_provider_factory = OAuthProviderFactory::new();

        // Create and register Google OAuth provider if configured
        let google_oauth_service = match GoogleOAuthService::new(config.google_oauth.clone()) {
            Ok(service) => Arc::new(service),
            Err(e) => {
                tracing::error!("Failed to create GoogleOAuthService: {}", e);
                tracing::warn!(
                    "OAuth functionality will be disabled. Please check your OAuth configuration."
                );
                // Skip OAuth registration and continue without it
                // Create auth service without OAuth provider factory
                let mut auth_service_impl = AuthService::new(
                    user_service.clone(),
                    user_repo,
                    role_service.clone(),
                    permission_service.clone(),
                    config.jwt_secret.clone(),
                    config.tokens.clone(),
                );
                auth_service_impl.set_email_event_bus(email_event_bus);
                return (
                    user_service,
                    Arc::new(auth_service_impl),
                    permission_service,
                    role_service,
                    category_service,
                    image_service,
                    laptop_management_service,
                    exp_system_service,
                    title_service,
                    redis_service,
                );
            }
        };

        let google_provider = Arc::new(GoogleOAuthProvider::new(google_oauth_service));
        oauth_provider_factory.register_provider(google_provider);

        let auth_service_impl = AuthService::with_oauth_provider_factory_and_email_bus(
            user_service.clone(),
            user_repo,
            role_service.clone(),
            permission_service.clone(),
            config.jwt_secret.clone(),
            config.tokens.clone(),
            oauth_provider_factory,
            Some(email_event_bus),
        );

        (
            user_service,
            Arc::new(auth_service_impl),
            permission_service,
            role_service,
            category_service,
            image_service,
            laptop_management_service,
            exp_system_service,
            title_service,
            redis_service,
        )
    }

    pub async fn new(database: Database, config: Config) -> Result<Self, anyhow::Error> {
        // Initialize Redis service first - required for email event bus
        tracing::info!("Initializing Redis service...");
        let redis_manager = RedisManager::new(&config.redis).await?;
        let redis_service: Arc<dyn RedisServiceTrait> = Arc::new(RedisService::new(redis_manager));
        tracing::info!("Redis service initialized successfully");

        // Create email event bus with Redis service
        let email_event_bus = Arc::new(EmailEventBus::new(redis_service.clone()));

        Self::with_services(database, config, redis_service, email_event_bus).await
    }

    pub async fn with_services(
        database: Database,
        config: Config,
        redis_service: Arc<dyn RedisServiceTrait>,
        email_event_bus: Arc<EmailEventBus>,
    ) -> Result<Self, anyhow::Error> {
        let (
            user_service,
            auth_service,
            permission_service,
            role_service,
            category_service,
            image_service,
            laptop_management_service,
            exp_system_service,
            title_service,
            _redis_service,
        ) = Self::build_services(
            &database,
            &config,
            redis_service.clone(),
            email_event_bus.clone(),
        )
        .await;

        // Note: Email event bus is set in the AuthService constructor
        // We don't need to set it here since it's already configured

        Ok(Self {
            user_service,
            auth_service,
            permission_service,
            role_service,
            category_service,
            image_service,
            laptop_management_service,
            exp_system_service,
            title_service,
            email_event_bus,
            redis_service,
            database,
            config,
        })
    }

    pub fn config(&self) -> &crate::config::Config {
        &self.config
    }
}

// Implement ServiceRegistry trait (DIP: Depend on abstractions)
impl ServiceRegistry for ServiceContainer {
    fn get_user_service(&self) -> Arc<dyn UserServiceTrait> {
        Arc::clone(&self.user_service) // Explicit Arc::clone
    }

    fn get_auth_service(&self) -> Arc<dyn AuthServiceTrait> {
        Arc::clone(&self.auth_service) // Explicit Arc::clone
    }

    fn get_permission_service(&self) -> Arc<dyn PermissionServiceTrait> {
        Arc::clone(&self.permission_service) // Explicit Arc::clone
    }

    fn get_role_service(&self) -> Arc<dyn RoleServiceTrait> {
        Arc::clone(&self.role_service) // Explicit Arc::clone
    }

    fn get_category_service(&self) -> Arc<dyn CategoryServiceTrait> {
        Arc::clone(&self.category_service) // Explicit Arc::clone
    }

    fn get_laptop_management_service(&self) -> Arc<dyn LaptopManagementServiceTrait> {
        Arc::clone(&self.laptop_management_service) // Explicit Arc::clone
    }

    fn get_redis_service(&self) -> Option<Arc<dyn RedisServiceTrait>> {
        Some(Arc::clone(&self.redis_service)) // Explicit Arc::clone
    }
}

// Convenience methods for direct access (consistent with trait interface)
impl ServiceContainer {
    pub fn user_service(&self) -> Arc<dyn UserServiceTrait> {
        Arc::clone(&self.user_service) // Explicit Arc::clone
    }

    pub fn auth_service(&self) -> Arc<dyn AuthServiceTrait> {
        Arc::clone(&self.auth_service) // Explicit Arc::clone
    }

    pub fn permission_service(&self) -> Arc<dyn PermissionServiceTrait> {
        Arc::clone(&self.permission_service) // Explicit Arc::clone
    }

    pub fn role_service(&self) -> Arc<dyn RoleServiceTrait> {
        Arc::clone(&self.role_service) // Explicit Arc::clone
    }

    pub fn category_service(&self) -> Arc<dyn CategoryServiceTrait> {
        Arc::clone(&self.category_service) // Explicit Arc::clone
    }

    pub fn image_service(&self) -> Arc<dyn ImageServiceTrait> {
        Arc::clone(&self.image_service) // Explicit Arc::clone
    }

    pub fn laptop_management_service(&self) -> Arc<dyn LaptopManagementServiceTrait> {
        Arc::clone(&self.laptop_management_service) // Explicit Arc::clone
    }

    pub fn redis_service(&self) -> Arc<dyn RedisServiceTrait> {
        self.redis_service.clone() // Explicit Arc::clone
    }

    pub fn exp_system_service(&self) -> DynExpSystemService {
        self.exp_system_service.clone()
    }

    pub fn title_service(&self) -> DynTitleService {
        self.title_service.clone()
    }

    pub fn email_event_bus(&self) -> Arc<EmailEventBus> {
        Arc::clone(&self.email_event_bus)
    }

    pub fn database(&self) -> &Database {
        &self.database
    }

    /// Get Redis service status
    pub fn redis_status(&self) -> &str {
        "Connected" // Always connected since Redis is required
    }

    /// Check if Redis is available
    pub fn is_redis_available(&self) -> bool {
        true // Always true since Redis is required
    }
}
