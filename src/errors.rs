use crate::constants::auth as auth_codes;
use crate::constants::system as system_codes;
use axum::{
    Json,
    extract::rejection::JsonRejection,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use chrono::Utc;
use serde_json::json;
use std::sync::atomic::{AtomicPtr, Ordering};

/// Error context to store request-specific information
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ErrorContext {
    pub path: String,
}

impl Default for ErrorContext {
    fn default() -> Self {
        Self {
            path: "unknown".to_string(),
        }
    }
}

/// Global current path storage
static CURRENT_PATH: AtomicPtr<String> = AtomicPtr::new(std::ptr::null_mut());

/// Set current request path (called from middleware)
pub fn set_current_path(path: String) {
    let path_box = Box::new(path);
    let path_ptr = Box::into_raw(path_box);

    // Clean up old path if exists
    let old_ptr = CURRENT_PATH.swap(path_ptr, Ordering::SeqCst);
    if !old_ptr.is_null() {
        unsafe {
            let _ = Box::from_raw(old_ptr);
        }
    }
}

/// Get current request path
pub fn get_current_path() -> String {
    let path_ptr = CURRENT_PATH.load(Ordering::SeqCst);
    if path_ptr.is_null() {
        "unknown".to_string()
    } else {
        unsafe { (*path_ptr).clone() }
    }
}

/// Clear current path (called after request processing)
pub fn clear_current_path() {
    let old_ptr = CURRENT_PATH.swap(std::ptr::null_mut(), Ordering::SeqCst);
    if !old_ptr.is_null() {
        unsafe {
            let _ = Box::from_raw(old_ptr);
        }
    }
}

#[derive(thiserror::Error, Debug)]
pub enum AppError {
    #[error("SQLx database error: {0}")]
    Sqlx(#[from] sqlx::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Validation errors")]
    ValidationDetailed(crate::utils::validation::ValidationResult),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Internal server error")]
    Internal(#[from] anyhow::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Forbidden: {0}")]
    Forbidden(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[error("Token expired")]
    TokenExpired,

    #[error("Permission or roles changed")]
    PermissionChanged,

    #[error("JSON deserialization error: {0}")]
    JsonDeserialization(String),
}

impl AppError {
    /// Create error response with context
    pub fn into_response_with_context(self, context: ErrorContext) -> Response {
        let (status, error_message, error_code) = match &self {
            AppError::TokenExpired => (
                StatusCode::UNAUTHORIZED,
                "Access token expired",
                auth_codes::TOKEN_EXPIRED,
            ),
            AppError::PermissionChanged => (
                StatusCode::UNAUTHORIZED,
                "Permission changed or revoked",
                auth_codes::PERMISSION_CHANGED,
            ),
            AppError::Sqlx(err) => {
                // Check for specific database constraint violations
                if let sqlx::Error::Database(db_err) = err {
                    if let Some(code) = db_err.code() {
                        match code.as_ref() {
                            // PostgreSQL unique constraint violation
                            "23505" => {
                                let message = if db_err.message().contains("users_username_key") {
                                    "Username already exists"
                                } else if db_err.message().contains("users_email_key") {
                                    "Email already exists"
                                } else {
                                    "Resource already exists"
                                };
                                (StatusCode::CONFLICT, message, system_codes::ERROR_CONFLICT)
                            }
                            _ => {
                                tracing::error!("SQLx database error: {}", err);
                                (
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    "Database error occurred",
                                    system_codes::ERROR_DATABASE,
                                )
                            }
                        }
                    } else {
                        tracing::error!("SQLx database error: {}", err);
                        (
                            StatusCode::INTERNAL_SERVER_ERROR,
                            "Database error occurred",
                            system_codes::ERROR_DATABASE,
                        )
                    }
                } else {
                    tracing::error!("SQLx database error: {}", err);
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        "Database error occurred",
                        system_codes::ERROR_DATABASE,
                    )
                }
            }
            AppError::Validation(err) => (
                StatusCode::BAD_REQUEST,
                err.as_str(),
                system_codes::ERROR_VALIDATION,
            ),
            AppError::ValidationDetailed(_) => (
                StatusCode::BAD_REQUEST,
                "Validation failed",
                system_codes::ERROR_VALIDATION,
            ),
            AppError::NotFound(err) => (
                StatusCode::NOT_FOUND,
                err.as_str(),
                system_codes::ERROR_NOT_FOUND,
            ),
            AppError::Unauthorized(err) => (
                StatusCode::UNAUTHORIZED,
                err.as_str(),
                system_codes::ERROR_UNAUTHORIZED,
            ),
            AppError::Forbidden(err) => (
                StatusCode::FORBIDDEN,
                err.as_str(),
                system_codes::ERROR_FORBIDDEN,
            ),
            AppError::Conflict(err) => (
                StatusCode::CONFLICT,
                err.as_str(),
                system_codes::ERROR_CONFLICT,
            ),
            AppError::BadRequest(err) => (
                StatusCode::BAD_REQUEST,
                err.as_str(),
                system_codes::ERROR_BAD_REQUEST,
            ),
            AppError::NotImplemented(err) => (
                StatusCode::NOT_IMPLEMENTED,
                err.as_str(),
                system_codes::ERROR_INTERNAL,
            ),
            AppError::Internal(err) => {
                tracing::error!("Internal error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Internal server error",
                    system_codes::ERROR_INTERNAL,
                )
            }
            AppError::Serialization(err) => {
                tracing::error!("Serialization error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Serialization error",
                    system_codes::ERROR_INTERNAL,
                )
            }
            AppError::JsonDeserialization(err) => (
                StatusCode::UNPROCESSABLE_ENTITY,
                err.as_str(),
                system_codes::ERROR_VALIDATION,
            ),
        };

        let body = match &self {
            AppError::ValidationDetailed(validation_result) => Json(json!({
                "timestamp": Utc::now().to_rfc3339(),
                "path": context.path,
                "status": status.as_u16(),
                "code": error_code,
                "message": "Validation failed",
                "data": null,
                "error": validation_result.to_structured_error(),
            })),
            _ => {
                // Check for custom auth errors
                if error_code == auth_codes::TOKEN_EXPIRED
                    || error_code == auth_codes::PERMISSION_CHANGED
                {
                    let body = Json(json!({
                        "success": false,
                        "message": error_message,
                        "code": error_code,
                        "timestamp": Utc::now().to_rfc3339(),
                    }));
                    return (status, body).into_response();
                }

                Json(json!({
                    "timestamp": Utc::now().to_rfc3339(),
                    "path": context.path,
                    "status": status.as_u16(),
                    "code": error_code,
                    "message": "Error occurred",
                    "data": null,
                    "error": error_message,
                }))
            }
        };

        (status, body).into_response()
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        // Get current path from global storage
        let path = get_current_path();
        let context = ErrorContext { path };

        self.into_response_with_context(context)
    }
}

// Implement From trait for JsonRejection to convert it to AppError
impl From<JsonRejection> for AppError {
    fn from(rejection: JsonRejection) -> Self {
        AppError::JsonDeserialization(rejection.to_string())
    }
}

pub type Result<T> = std::result::Result<T, AppError>;

impl From<lettre::error::Error> for AppError {
    fn from(err: lettre::error::Error) -> Self {
        AppError::Internal(anyhow::anyhow!("Email error: {}", err))
    }
}

impl From<lettre::address::AddressError> for AppError {
    fn from(err: lettre::address::AddressError) -> Self {
        AppError::Internal(anyhow::anyhow!("Address error: {}", err))
    }
}

impl From<lettre::transport::smtp::Error> for AppError {
    fn from(err: lettre::transport::smtp::Error) -> Self {
        AppError::Internal(anyhow::anyhow!("SMTP error: {}", err))
    }
}
