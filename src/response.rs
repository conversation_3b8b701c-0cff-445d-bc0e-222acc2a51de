use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use utoipa::ToSchema;

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ApiResponse<T> {
    pub timestamp: String,
    pub path: String,
    pub status: u16,
    pub code: String,    // Service code (e.g., AUTH01, USER01)
    pub message: String, // Human readable message
    pub data: Option<T>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ApiResponseJson {
    pub timestamp: String,
    pub path: String,
    pub status: u16,
    pub code: String,    // Service code (e.g., AUTH01, USER01)
    pub message: String, // Human readable message
    #[schema(value_type = Object)]
    pub data: Option<Value>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T>
where
    T: Serialize,
{
    pub fn success(path: String, code: String, message: String, data: T) -> Self {
        Self {
            timestamp: Utc::now().to_rfc3339(),
            path,
            status: 200,
            code,
            message,
            data: Some(data),
            error: None,
        }
    }

    pub fn created(path: String, code: String, message: String, data: T) -> Self {
        Self {
            timestamp: Utc::now().to_rfc3339(),
            path,
            status: 201,
            code,
            message,
            data: Some(data),
            error: None,
        }
    }
}

impl ApiResponse<()> {
    pub fn error(path: String, status: u16, code: String, error: String) -> Self {
        Self {
            timestamp: Utc::now().to_rfc3339(),
            path,
            status,
            code,
            message: "Error occurred".to_string(),
            data: None,
            error: Some(error),
        }
    }
}

impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> Response {
        let status_code =
            StatusCode::from_u16(self.status).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR);
        (status_code, Json(self)).into_response()
    }
}

// Response helper functions
pub fn success_response<T: Serialize>(
    path: String,
    code: &str,
    message: &str,
    data: T,
) -> ApiResponse<T> {
    ApiResponse::success(path, code.to_string(), message.to_string(), data)
}

pub fn created_response<T: Serialize>(
    path: String,
    code: &str,
    message: &str,
    data: T,
) -> ApiResponse<T> {
    ApiResponse::created(path, code.to_string(), message.to_string(), data)
}

pub fn error_response(
    path: String,
    status: StatusCode,
    code: &str,
    error: &str,
) -> ApiResponse<()> {
    ApiResponse::error(path, status.as_u16(), code.to_string(), error.to_string())
}
