use platform_rust::container::ServiceContainer;
use platform_rust::{config::Config, database::Database, routes::create_routes};
use std::net::SocketAddr;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "platform_rust=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Load configuration
    let config = Config::from_env()?;
    tracing::info!("Configuration loaded successfully");

    // Initialize database
    let database = Database::new(&config.database_url).await?;
    tracing::info!("Database connection established");

    // Run migrations
    database.migrate().await?;
    tracing::info!("Database migrations completed");

    // Initialize database with default data
    if let Err(e) = platform_rust::init::initialize_database(database.clone()).await {
        tracing::warn!("Database initialization failed: {}", e);
    } else {
        tracing::info!("Database initialization completed");
    }

    // Initialize service container
    let container = ServiceContainer::new(database, config.clone()).await?;
    tracing::info!("Service container initialized");

    // Create router
    let container = std::sync::Arc::new(container);
    let app = create_routes(
        container.clone(),
        config.clone(),
        container.email_event_bus(),
    )
    .await?;

    // Start server
    let listener = tokio::net::TcpListener::bind(&config.server_address()).await?;
    tracing::info!("🚀 Server running on {}", config.server_address());

    axum::serve(
        listener,
        app.into_make_service_with_connect_info::<SocketAddr>(),
    )
    .await?;

    Ok(())
}
