use std::time::Duration;
use uuid::Uuid;

/// Cache key generation utilities to avoid DRY violations
pub struct CacheKeyGenerator;

impl CacheKeyGenerator {
    /// Generate cache key for entity details
    pub fn entity_details(entity_type: &str, id: &Uuid) -> String {
        format!("{entity_type}:details:{id}")
    }

    /// Generate cache key for entity lists by type
    pub fn entity_list_by_type(entity_type: &str, type_name: &str) -> String {
        format!("{entity_type}:type:{type_name}")
    }

    /// Generate cache key for user-specific data
    pub fn user_data(data_type: &str, user_id: &Uuid) -> String {
        format!("user:{data_type}:{user_id}")
    }

    /// Generate cache key for permission version
    pub fn permission_version(user_id: &Uuid) -> String {
        Self::user_data("perm_version", user_id)
    }

    /// Generate cache key for user level
    pub fn user_level(user_id: &Uuid) -> String {
        Self::user_data("level", user_id)
    }

    /// Generate cache key for leaderboard
    pub fn leaderboard(limit: i32) -> String {
        format!("leaderboard:limit_{limit}")
    }

    /// Generate cache key for popular items
    pub fn popular_items(item_type: &str) -> String {
        format!("{item_type}:popular")
    }

    /// Generate cache key for search results
    pub fn search_results(entity_type: &str, query: &str, filters: &str) -> String {
        format!("{entity_type}:search:{query}:{filters}")
    }

    /// Generate cache key for rate limiting
    pub fn rate_limit(user_id: &str, endpoint: &str) -> String {
        format!("rate_limit:{user_id}:{endpoint}")
    }

    /// Generate cache key for session storage
    pub fn session(session_id: &str) -> String {
        format!("session:{session_id}")
    }

    /// Generate cache key for API usage tracking
    pub fn api_usage(user_id: &str, date: &str) -> String {
        format!("api_usage:{user_id}:{date}")
    }

    /// Generate cache key for entity list by status
    pub fn entity_list_by_status(entity_type: &str, status: &str) -> String {
        format!("{entity_type}:status:{status}")
    }

    /// Generate cache key for entity search results
    pub fn entity_search(entity_type: &str, query: &str) -> String {
        format!("{entity_type}:search:{}", query.replace(" ", "_"))
    }

    /// Generate cache key for entity list by category
    pub fn entity_list_by_category(entity_type: &str, category_id: &Uuid) -> String {
        format!("{entity_type}:category:{category_id}")
    }

    /// Generate cache key for entity list by user
    pub fn entity_list_by_user(entity_type: &str, user_id: &Uuid) -> String {
        format!("{entity_type}:user:{user_id}")
    }

    /// Generate cache key for entity count
    pub fn entity_count(entity_type: &str) -> String {
        format!("{entity_type}:count")
    }

    /// Generate cache key for entity count by status
    pub fn entity_count_by_status(entity_type: &str, status: &str) -> String {
        format!("{entity_type}:count:status:{status}")
    }

    /// Generate cache key for entity statistics
    pub fn entity_stats(entity_type: &str) -> String {
        format!("{entity_type}:stats")
    }

    /// Generate cache key for entity recommendations
    pub fn entity_recommendations(entity_type: &str, user_id: &Uuid) -> String {
        format!("{entity_type}:recommendations:{user_id}")
    }

    /// Generate cache key for entity related items
    pub fn entity_related(entity_type: &str, entity_id: &Uuid) -> String {
        format!("{entity_type}:related:{entity_id}")
    }
}

/// Cache TTL constants to avoid magic numbers
pub struct CacheTTL;

impl CacheTTL {
    /// Short cache duration (15 minutes)
    pub fn short() -> Duration {
        Duration::from_secs(900)
    }

    /// Medium cache duration (30 minutes)
    pub fn medium() -> Duration {
        Duration::from_secs(1800)
    }

    /// Long cache duration (1 hour)
    pub fn long() -> Duration {
        Duration::from_secs(3600)
    }

    /// Very long cache duration (2 hours)
    pub fn very_long() -> Duration {
        Duration::from_secs(7200)
    }

    /// Specific TTL for different data types
    pub fn user_level() -> Duration {
        Self::medium()
    }

    pub fn leaderboard() -> Duration {
        Self::short()
    }

    pub fn entity_details() -> Duration {
        Self::long()
    }

    pub fn entity_lists() -> Duration {
        Self::long()
    }

    pub fn permission_version() -> Duration {
        Self::long()
    }
}

/// Cache operation utilities
pub struct CacheOperations;

impl CacheOperations {
    /// Log cache hit with consistent format
    pub fn log_cache_hit(entity_type: &str, id: &str) {
        tracing::debug!(
            entity_type = %entity_type,
            id = %id,
            cache_status = "hit",
            "{} retrieved from cache",
            entity_type
        );
    }

    /// Log cache miss with consistent format
    pub fn log_cache_miss(entity_type: &str, id: &str) {
        tracing::debug!(
            entity_type = %entity_type,
            id = %id,
            cache_status = "miss",
            "{} cache miss, querying database",
            entity_type
        );
    }

    /// Log cache set with consistent format
    pub fn log_cache_set(entity_type: &str, id: &str, ttl_secs: u64) {
        tracing::debug!(
            entity_type = %entity_type,
            id = %id,
            ttl_secs = ttl_secs,
            "Successfully cached {}",
            entity_type
        );
    }

    /// Log cache set failure with consistent format
    pub fn log_cache_set_failure(entity_type: &str, id: &str, error: &str) {
        tracing::warn!(
            entity_type = %entity_type,
            id = %id,
            error = %error,
            "Failed to cache {}",
            entity_type
        );
    }

    /// Log cache invalidation with consistent format
    pub fn log_cache_invalidation(entity_type: &str, id: &str) {
        tracing::debug!(
            entity_type = %entity_type,
            id = %id,
            "Successfully invalidated {} cache",
            entity_type
        );
    }

    /// Log cache invalidation failure with consistent format
    pub fn log_cache_invalidation_failure(entity_type: &str, id: &str, error: &str) {
        tracing::warn!(
            entity_type = %entity_type,
            id = %id,
            error = %error,
            "Failed to invalidate {} cache",
            entity_type
        );
    }
}
