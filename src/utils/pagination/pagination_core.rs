use crate::errors::{AppError, Result};
use crate::utils::ValidateRequest;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

/// Generic pagination metadata that can be used across all services
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, ToSchema)]
pub struct PaginationMeta {
    pub page: i64,
    pub limit: i64,
    pub total: i64,
    pub total_pages: i64,
}

impl PaginationMeta {
    /// Create pagination metadata with consistent total_pages calculation
    pub fn new(page: i64, limit: i64, total: i64) -> Self {
        let total_pages = if total == 0 {
            0
        } else {
            (total + limit - 1) / limit
        };
        Self {
            page,
            limit,
            total,
            total_pages,
        }
    }
}

/// Generic paginated response wrapper
#[derive(Debug, <PERSON>lone, Serialize)]
pub struct Paginated<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

impl<T> Paginated<T> {
    pub fn new(data: Vec<T>, page: i64, limit: i64, total: i64) -> Self {
        Self {
            data,
            meta: PaginationMeta::new(page, limit, total),
        }
    }
}

/// Common pagination request trait
pub trait PaginationRequest {
    fn page(&self) -> i64;
    fn limit(&self) -> i64;

    /// Calculate offset for database queries
    fn offset(&self) -> i64 {
        (self.page() - 1) * self.limit()
    }

    /// Validate pagination parameters
    fn validate_pagination(&self) -> Result<()> {
        if self.page() < 1 {
            return Err(AppError::BadRequest(
                "Page number must be greater than 0".to_string(),
            ));
        }

        if self.limit() < 1 {
            return Err(AppError::BadRequest(
                "Limit must be greater than 0".to_string(),
            ));
        }

        if self.limit() > 100 {
            return Err(AppError::BadRequest("Limit cannot exceed 100".to_string()));
        }

        Ok(())
    }
}

/// Generic pagination request struct
#[derive(Debug, Clone, Deserialize)]
pub struct GenericPaginationRequest {
    #[serde(default = "default_page")]
    pub page: i64,
    #[serde(default = "default_limit")]
    pub limit: i64,
}

impl PaginationRequest for GenericPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

impl ValidateRequest for GenericPaginationRequest {
    fn validate_request(&self) -> Result<()> {
        self.validate_pagination()
    }
}

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}
