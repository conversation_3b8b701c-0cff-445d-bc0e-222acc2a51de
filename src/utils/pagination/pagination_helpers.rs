use crate::errors::Result;

/// Pagination helper functions to reduce duplicate code
pub struct PaginationHelper;

impl PaginationHelper {
    /// Calculate offset from page and limit
    pub fn calculate_offset(page: i32, limit: i32) -> i32 {
        (page - 1) * limit
    }

    /// Calculate total pages from total count and limit
    pub fn calculate_total_pages(total: i64, limit: i32) -> i32 {
        ((total + limit as i64 - 1) / limit as i64) as i32
    }

    /// Calculate total pages from total count and limit (f64 version for more precision)
    pub fn calculate_total_pages_f64(total: f64, limit: f64) -> i32 {
        (total / limit).ceil() as i32
    }

    /// Validate pagination parameters
    pub fn validate_pagination(page: i32, limit: i32, max_limit: i32) -> Result<(i32, i32)> {
        let page = page.max(1);
        let limit = limit.max(1).min(max_limit);
        Ok((page, limit))
    }

    /// Get pagination info for response
    pub fn get_pagination_info(page: i32, limit: i32, total: i64) -> (i32, i32, i32, i64) {
        let total_pages = Self::calculate_total_pages(total, limit);
        (page, limit, total_pages, total)
    }

    /// Check if there's a next page
    pub fn has_next_page(page: i32, total_pages: i32) -> bool {
        page < total_pages
    }

    /// Check if there's a previous page
    pub fn has_prev_page(page: i32) -> bool {
        page > 1
    }

    /// Get next page number
    pub fn next_page(page: i32, total_pages: i32) -> Option<i32> {
        if Self::has_next_page(page, total_pages) {
            Some(page + 1)
        } else {
            None
        }
    }

    /// Get previous page number
    pub fn prev_page(page: i32) -> Option<i32> {
        if Self::has_prev_page(page) {
            Some(page - 1)
        } else {
            None
        }
    }

    /// Calculate start and end item numbers for current page
    pub fn get_item_range(page: i32, limit: i32, total: i64) -> (i64, i64) {
        let start = ((page - 1) * limit) as i64 + 1;
        let end = (page * limit) as i64;
        let end = end.min(total);
        (start, end)
    }

    /// Build pagination metadata
    pub fn build_pagination_meta(
        page: i32,
        limit: i32,
        total: i64,
    ) -> crate::utils::pagination::PaginationMeta {
        let (page, limit, total_pages, total) = Self::get_pagination_info(page, limit, total);

        crate::utils::pagination::PaginationMeta {
            page: page.into(),
            limit: limit.into(),
            total,
            total_pages: total_pages.into(),
        }
    }
}

/// Pagination request builder for common patterns
pub struct PaginationRequestBuilder {
    page: i32,
    limit: i32,
    max_limit: i32,
}

impl PaginationRequestBuilder {
    pub fn new() -> Self {
        Self {
            page: 1,
            limit: 10,
            max_limit: 100,
        }
    }

    pub fn with_page(mut self, page: i32) -> Self {
        self.page = page.max(1);
        self
    }

    pub fn with_limit(mut self, limit: i32) -> Self {
        self.limit = limit.max(1).min(self.max_limit);
        self
    }

    pub fn with_max_limit(mut self, max_limit: i32) -> Self {
        self.max_limit = max_limit.max(1);
        self.limit = self.limit.min(self.max_limit);
        self
    }

    pub fn build(self) -> (i32, i32) {
        (self.page, self.limit)
    }

    pub fn build_with_offset(self) -> (i32, i32, i32) {
        let (page, limit) = self.build();
        let offset = PaginationHelper::calculate_offset(page, limit);
        (page, limit, offset)
    }
}

impl Default for PaginationRequestBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Pagination response builder
pub struct PaginationResponseBuilder<T> {
    data: Vec<T>,
    page: i32,
    limit: i32,
    total: i64,
}

impl<T> PaginationResponseBuilder<T> {
    pub fn new(data: Vec<T>, page: i32, limit: i32, total: i64) -> Self {
        Self {
            data,
            page,
            limit,
            total,
        }
    }

    pub fn build(self) -> crate::utils::pagination::Paginated<T> {
        let meta = PaginationHelper::build_pagination_meta(self.page, self.limit, self.total);

        crate::utils::pagination::Paginated {
            data: self.data,
            meta,
        }
    }
}
