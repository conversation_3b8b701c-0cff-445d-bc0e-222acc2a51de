use axum::http::HeaderValue;
use std::sync::OnceLock;

/// Pre-validated CORS origin constants to avoid runtime parsing
pub struct CorsConstants;

impl CorsConstants {
    /// Get localhost:3000 header value
    pub fn localhost_3000() -> &'static HeaderValue {
        static LOCALHOST_3000: OnceLock<HeaderValue> = OnceLock::new();
        LOCALHOST_3000.get_or_init(|| {
            "http://localhost:3000"
                .parse()
                .unwrap_or_else(|_| HeaderValue::from_static("http://localhost:3000"))
        })
    }

    /// Get localhost:3001 header value
    pub fn localhost_3001() -> &'static HeaderValue {
        static LOCALHOST_3001: OnceLock<HeaderValue> = OnceLock::new();
        LOCALHOST_3001.get_or_init(|| {
            "http://localhost:3001"
                .parse()
                .unwrap_or_else(|_| HeaderValue::from_static("http://localhost:3001"))
        })
    }

    /// Get localhost:8386 header value
    pub fn localhost_8080() -> &'static HeaderValue {
        static LOCALHOST_8080: OnceLock<HeaderValue> = OnceLock::new();
        LOCALHOST_8080.get_or_init(|| {
            "http://localhost:8386"
                .parse()
                .unwrap_or_else(|_| HeaderValue::from_static("http://localhost:8386"))
        })
    }

    /// Get 127.0.0.1:3000 header value
    pub fn localhost_ip_3000() -> &'static HeaderValue {
        static LOCALHOST_IP_3000: OnceLock<HeaderValue> = OnceLock::new();
        LOCALHOST_IP_3000.get_or_init(|| {
            "http://127.0.0.1:3000"
                .parse()
                .unwrap_or_else(|_| HeaderValue::from_static("http://127.0.0.1:3000"))
        })
    }

    /// Get all development origins as a slice
    pub fn dev_origins() -> &'static [HeaderValue] {
        static DEV_ORIGINS: OnceLock<Vec<HeaderValue>> = OnceLock::new();
        DEV_ORIGINS.get_or_init(|| {
            vec![
                Self::localhost_3000().clone(),
                Self::localhost_3001().clone(),
                Self::localhost_8080().clone(),
                Self::localhost_ip_3000().clone(),
            ]
        })
    }
}
