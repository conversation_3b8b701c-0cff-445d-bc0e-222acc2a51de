use anyhow::{Result, anyhow};
use std::env;

/// Security utilities for JWT and secret management
pub struct SecurityUtils;

impl SecurityUtils {
    /// Generate secure JWT secret if not provided
    pub fn get_or_generate_jwt_secret() -> Result<String> {
        // Try to get from environment first
        if let Ok(secret) = env::var("JWT_SECRET") {
            if secret.len() >= 32 {
                return Ok(secret);
            } else {
                tracing::warn!("JWT_SECRET is too short (< 32 chars), generating new one");
            }
        }

        // Generate secure random secret
        Self::generate_secure_secret()
    }

    /// Generate cryptographically secure secret
    pub fn generate_secure_secret() -> Result<String> {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        use std::time::{SystemTime, UNIX_EPOCH};

        let mut hasher = DefaultHasher::new();

        // Mix multiple entropy sources
        SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_nanos()
            .hash(&mut hasher);
        std::process::id().hash(&mut hasher);

        // Get hostname for additional entropy
        if let Ok(hostname) = env::var("HOSTNAME") {
            hostname.hash(&mut hasher);
        }

        let hash = hasher.finish();

        // Create 64-character hex string
        Ok(format!(
            "{:016x}{:016x}{:016x}{:016x}",
            hash,
            hash.wrapping_mul(16777619),
            hash.wrapping_mul(2654435761),
            hash.wrapping_mul(40503)
        ))
    }

    /// Validate JWT secret strength
    pub fn validate_jwt_secret(secret: &str) -> Result<()> {
        if secret.len() < 32 {
            return Err(anyhow!("JWT secret must be at least 32 characters long"));
        }

        if secret == "your-secret-key" || secret == "secret" || secret == "jwt-secret" {
            return Err(anyhow!("JWT secret must not be a default/common value"));
        }

        // Check for minimum complexity
        let has_upper = secret.chars().any(|c| c.is_uppercase());
        let has_lower = secret.chars().any(|c| c.is_lowercase());
        let has_digit = secret.chars().any(|c| c.is_ascii_digit());
        let has_special = secret.chars().any(|c| !c.is_alphanumeric());

        if ![has_upper, has_lower, has_digit, has_special]
            .iter()
            .filter(|&&x| x)
            .count()
            >= 3
        {
            tracing::warn!(
                "JWT secret should contain at least 3 of: uppercase, lowercase, digits, special chars"
            );
        }

        Ok(())
    }

    /// Hash sensitive data for logging (show only first/last chars)
    pub fn hash_for_log(sensitive: &str) -> String {
        if sensitive.len() <= 8 {
            return "*".repeat(sensitive.len());
        }

        format!(
            "{}...{}",
            &sensitive[..3],
            &sensitive[sensitive.len() - 3..]
        )
    }

    /// Constant-time string comparison to prevent timing attacks
    pub fn constant_time_eq(a: &str, b: &str) -> bool {
        if a.len() != b.len() {
            return false;
        }

        a.bytes()
            .zip(b.bytes())
            .fold(0, |acc, (x, y)| acc | (x ^ y))
            == 0
    }
}
