use lazy_static::lazy_static;
use regex::Regex;

lazy_static! {
    /// Pre-compiled regex patterns to avoid runtime compilation and potential panics
    /// Email validation regex
    pub static ref EMAIL_REGEX: Regex = Regex::new(r"^[^\s@]+@[^\s@]+\.[^\s@]+$")
        .unwrap_or_else(|_| Regex::new("").unwrap());

    /// Alphanumeric characters regex
    pub static ref ALPHANUMERIC_REGEX: Regex = Regex::new(r"[A-Za-z0-9]+")
        .unwrap_or_else(|_| Regex::new("").unwrap());

    /// HTML tag removal regex
    pub static ref HTML_TAG_REGEX: Regex = Regex::new(r"<[^>]*>")
        .unwrap_or_else(|_| Regex::new("").unwrap());

    /// Script tag removal regex
    pub static ref SCRIPT_TAG_REGEX: Regex = Regex::new(r"(?i)<script[^>]*>.*?</script>")
        .unwrap_or_else(|_| Regex::new("").unwrap());

    /// SQL injection detection regex
    pub static ref SQL_INJECTION_REGEX: Regex = Regex::new(r"(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)")
        .unwrap_or_else(|_| Regex::new("").unwrap());

    /// Username validation regex
    pub static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_]{3,30}$")
        .unwrap_or_else(|_| Regex::new("").unwrap());
}

/// Safe regex compilation helper
pub fn compile_regex_safe(pattern: &str) -> Regex {
    Regex::new(pattern).unwrap_or_else(|err| {
        tracing::error!("Failed to compile regex '{}': {}", pattern, err);
        // Return a regex that matches nothing instead of panicking
        Regex::new("$^").unwrap_or_else(|_| {
            // This should never fail, but if it does, we have a bigger problem
            panic!("Failed to compile fallback regex - this should never happen");
        })
    })
}
