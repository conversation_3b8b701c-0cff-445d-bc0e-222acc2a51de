use crate::utils::regex_constants::{
    EMAIL_REGEX, HTML_TAG_REGEX, SCRIPT_TAG_REGEX, SQL_INJECTION_REGEX, USERNAME_REGEX,
};

/// HTML/XSS sanitization
pub struct InputSanitizer;

impl InputSanitizer {
    /// Remove potentially dangerous characters from input
    pub fn sanitize_string(input: &str) -> String {
        let mut sanitized = input.to_string();

        // Remove script tags
        sanitized = SCRIPT_TAG_REGEX.replace_all(&sanitized, "").to_string();

        // Remove HTML tags
        sanitized = HTML_TAG_REGEX.replace_all(&sanitized, "").to_string();

        // Check for SQL injection patterns
        if SQL_INJECTION_REGEX.is_match(&sanitized.to_lowercase()) {
            tracing::warn!("Potential SQL injection attempt detected: {}", sanitized);
            // Replace dangerous SQL keywords
            sanitized = SQL_INJECTION_REGEX
                .replace_all(&sanitized, "[FILTERED]")
                .to_string();
        }

        // HTML encode special characters
        sanitized = sanitized
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#x27;")
            .replace("/", "&#x2F;");

        sanitized.trim().to_string()
    }

    /// Validate email format more strictly
    pub fn validate_email(email: &str) -> bool {
        EMAIL_REGEX.is_match(email) && email.len() <= 254
    }

    /// Validate username (alphanumeric + underscore only)
    pub fn validate_username(username: &str) -> bool {
        USERNAME_REGEX.is_match(username)
    }

    /// Check for common passwords
    pub fn is_weak_password(password: &str) -> bool {
        const WEAK_PASSWORDS: &[&str] = &[
            "password",
            "123456",
            "123456789",
            "qwerty",
            "abc123",
            "password123",
            "admin",
            "letmein",
            "welcome",
            "monkey",
        ];

        WEAK_PASSWORDS.contains(&password.to_lowercase().as_str()) || password.len() < 8
    }
}
