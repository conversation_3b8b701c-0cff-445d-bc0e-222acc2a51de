use crate::errors::{AppError, Result};
use serde::{Deserialize, Serialize};
use validator::Validate;

/// Structured validation error for better client handling
#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationError {
    pub field: String,
    pub code: String,
    pub message: String,
}

/// Validation result containing multiple field errors
#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationResult {
    pub errors: Vec<ValidationError>,
}

impl Default for ValidationResult {
    fn default() -> Self {
        Self::new()
    }
}

impl ValidationResult {
    pub fn new() -> Self {
        Self { errors: Vec::new() }
    }

    pub fn add_error(&mut self, field: &str, code: &str, message: &str) {
        self.errors.push(ValidationError {
            field: field.to_string(),
            code: code.to_string(),
            message: message.to_string(),
        });
    }

    pub fn is_valid(&self) -> bool {
        self.errors.is_empty()
    }

    pub fn first_error(&self) -> Option<String> {
        self.errors
            .first()
            .map(|e| format!("{}: {}", e.field, e.message))
    }

    /// Convert to structured error for API responses
    pub fn to_structured_error(&self) -> serde_json::Value {
        serde_json::json!({
            "validation_errors": self.errors,
            "error_count": self.errors.len()
        })
    }
}

/// Enhanced validation trait với structured errors
pub trait ValidateRequestEnhanced {
    fn validate_enhanced(&self) -> ValidationResult;
}

/// Shared validation trait để giảm boilerplate code
pub trait ValidateRequest {
    fn validate_request(&self) -> Result<()>;
}

/// Blanket implementation cho tất cả types implement Validate
impl<T: Validate> ValidateRequest for T {
    fn validate_request(&self) -> Result<()> {
        self.validate()
            .map_err(|e| AppError::Validation(e.to_string()))
    }
}

/// Helper functions cho common validation patterns
pub mod helpers {
    use super::*;

    /// Validate và execute operation - generic helper
    pub async fn validate_and_execute<T, F, Fut, R>(request: T, operation: F) -> Result<R>
    where
        T: ValidateRequest,
        F: FnOnce(T) -> Fut,
        Fut: std::future::Future<Output = Result<R>>,
    {
        request.validate_request()?;
        operation(request).await
    }

    /// Enhanced validation và execute với structured errors
    pub async fn validate_enhanced_and_execute<T, F, Fut, R>(request: T, operation: F) -> Result<R>
    where
        T: ValidateRequestEnhanced,
        F: FnOnce(T) -> Fut,
        Fut: std::future::Future<Output = Result<R>>,
    {
        let validation_result = request.validate_enhanced();
        if !validation_result.is_valid() {
            return Err(AppError::ValidationDetailed(validation_result));
        }
        operation(request).await
    }

    /// Check entity existence helper
    pub fn ensure_exists<T>(entity: Option<T>, entity_name: &str) -> Result<T> {
        entity.ok_or_else(|| AppError::NotFound(format!("{entity_name} not found")))
    }

    /// Check for conflicts helper
    pub fn ensure_no_conflict(condition: bool, message: &str) -> Result<()> {
        if condition {
            Err(AppError::Conflict(message.to_string()))
        } else {
            Ok(())
        }
    }
}

/// Helper function to validate string length with custom error codes
pub fn validate_string_length(
    value: &str,
    field_name: &str,
    min: usize,
    max: usize,
    errors: &mut ValidationResult,
) {
    let len = value.len();
    if len < min {
        errors.add_error(
            field_name,
            "STRING_TOO_SHORT",
            &format!("{field_name} must be at least {min} characters"),
        );
    } else if len > max {
        errors.add_error(
            field_name,
            "STRING_TOO_LONG",
            &format!("{field_name} must be at most {max} characters"),
        );
    }
}

/// Enhanced validation helpers for common patterns
pub mod entity_validation {
    use super::*;
    use crate::utils::ErrorHelper;
    use uuid::Uuid;

    /// Ensure entity exists or return not found error
    pub fn ensure_entity_exists<T>(entity: Option<T>, entity_type: &str, id: &Uuid) -> Result<T> {
        entity.ok_or_else(|| ErrorHelper::not_found(entity_type, id))
    }

    /// Ensure entity exists by name or return not found error
    pub fn ensure_entity_exists_by_name<T>(
        entity: Option<T>,
        entity_type: &str,
        name: &str,
    ) -> Result<T> {
        entity.ok_or_else(|| ErrorHelper::not_found_by_name(entity_type, name))
    }

    /// Ensure entity exists by email or return not found error
    pub fn ensure_entity_exists_by_email<T>(
        entity: Option<T>,
        entity_type: &str,
        email: &str,
    ) -> Result<T> {
        entity.ok_or_else(|| ErrorHelper::not_found_by_email(entity_type, email))
    }

    /// Ensure no duplicate entity exists
    pub fn ensure_no_duplicate(
        condition: bool,
        entity_type: &str,
        field: &str,
        value: &str,
    ) -> Result<()> {
        if condition {
            Err(ErrorHelper::conflict(entity_type, field, value))
        } else {
            Ok(())
        }
    }

    /// Ensure no duplicate entity exists by name
    pub fn ensure_no_duplicate_by_name(
        condition: bool,
        entity_type: &str,
        name: &str,
    ) -> Result<()> {
        ensure_no_duplicate(condition, entity_type, "name", name)
    }

    /// Ensure no duplicate entity exists by email
    pub fn ensure_no_duplicate_by_email(
        condition: bool,
        entity_type: &str,
        email: &str,
    ) -> Result<()> {
        ensure_no_duplicate(condition, entity_type, "email", email)
    }

    /// Ensure no duplicate entity exists by username
    pub fn ensure_no_duplicate_by_username(
        condition: bool,
        entity_type: &str,
        username: &str,
    ) -> Result<()> {
        ensure_no_duplicate(condition, entity_type, "username", username)
    }

    /// Ensure no duplicate entity exists by slug
    pub fn ensure_no_duplicate_by_slug(
        condition: bool,
        entity_type: &str,
        slug: &str,
    ) -> Result<()> {
        ensure_no_duplicate(condition, entity_type, "slug", slug)
    }

    /// Validate entity ownership
    pub fn ensure_entity_owner(
        entity_user_id: &Uuid,
        current_user_id: &Uuid,
        entity_type: &str,
    ) -> Result<()> {
        if entity_user_id != current_user_id {
            Err(ErrorHelper::forbidden(&format!(
                "You don't have permission to access this {entity_type}"
            )))
        } else {
            Ok(())
        }
    }

    /// Validate entity status
    pub fn ensure_entity_status<T>(
        _entity: &T,
        _expected_status: &str,
        _entity_type: &str,
    ) -> Result<()>
    where
        T: std::fmt::Display,
    {
        // This is a generic implementation - specific entities should implement their own status validation
        // For now, we'll assume the entity has a status field that can be converted to string
        Ok(())
    }
}

/// Helper function to validate required fields
pub fn validate_required_string(value: &str, field_name: &str, errors: &mut ValidationResult) {
    if value.trim().is_empty() {
        errors.add_error(
            field_name,
            "FIELD_REQUIRED",
            &format!("{field_name} is required"),
        );
    }
}
