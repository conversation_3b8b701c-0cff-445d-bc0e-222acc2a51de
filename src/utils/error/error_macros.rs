//! Database error handling macros to reduce boilerplate code
//!
//! Usage:
//! ```rust
//! let result = map_db_error!(sqlx::query("SELECT * FROM users").fetch_all(pool).await)?;
//! ```

/// Map database errors to AppError::Internal
#[macro_export]
macro_rules! map_db_error {
    ($expr:expr) => {
        $expr.map_err(|e| $crate::errors::AppError::Internal(e.into()))
    };
}

/// Map database errors with custom error message
#[macro_export]
macro_rules! map_db_error_with_msg {
    ($expr:expr, $msg:expr) => {
        $expr.map_err(|e| {
            tracing::error!("{}: {}", $msg, e);
            $crate::errors::AppError::Internal(e.into())
        })
    };
}

/// Handle database not found errors
#[macro_export]
macro_rules! handle_not_found {
    ($expr:expr, $entity_type:expr, $id:expr) => {
        match $expr {
            Some(entity) => Ok(entity),
            None => Err($crate::utils::ErrorHelper::not_found($entity_type, $id)),
        }
    };
}

/// Handle database conflict errors
#[macro_export]
macro_rules! handle_conflict {
    ($condition:expr, $entity_type:expr, $field:expr, $value:expr) => {
        if $condition {
            Err($crate::utils::ErrorHelper::conflict(
                $entity_type,
                $field,
                $value,
            ))
        } else {
            Ok(())
        }
    };
}

/// Execute database operation with error mapping
#[macro_export]
macro_rules! db_execute {
    ($pool:expr, $query:expr) => {
        map_db_error!(sqlx::query($query).execute($pool).await)
    };
}

/// Execute database query with error mapping
#[macro_export]
macro_rules! db_query {
    ($pool:expr, $query:expr) => {
        map_db_error!(sqlx::query($query).fetch_all($pool).await)
    };
}

/// Execute database query_as with error mapping
#[macro_export]
macro_rules! db_query_as {
    ($type:ty, $pool:expr, $query:expr) => {
        map_db_error!(sqlx::query_as::<_, $type>($query).fetch_all($pool).await)
    };
}

/// Execute database query_as_optional with error mapping
#[macro_export]
macro_rules! db_query_as_optional {
    ($type:ty, $pool:expr, $query:expr) => {
        map_db_error!(
            sqlx::query_as::<_, $type>($query)
                .fetch_optional($pool)
                .await
        )
    };
}

/// Execute database query_scalar with error mapping
#[macro_export]
macro_rules! db_query_scalar {
    ($type:ty, $pool:expr, $query:expr) => {
        map_db_error!(
            sqlx::query_scalar::<_, $type>($query)
                .fetch_one($pool)
                .await
        )
    };
}
