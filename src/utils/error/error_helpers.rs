use crate::errors::AppError;
use axum::response::IntoResponse;
use uuid::Uuid;

/// Error helper utilities to avoid DRY violations
pub struct <PERSON>rrorHelper;

impl ErrorHelper {
    /// Create not found error for entity by ID
    pub fn not_found(entity_type: &str, id: &Uuid) -> AppError {
        AppError::NotFound(format!("{entity_type} with ID '{id}' not found"))
    }

    /// Create not found error for entity by ID string
    pub fn not_found_by_id_str(entity_type: &str, id: &str) -> AppError {
        AppError::NotFound(format!("{entity_type} with ID '{id}' not found"))
    }

    /// Create not found error for entity by ID (backward compatibility)
    pub fn not_found_with_option(entity_type: &str, id: Option<&str>) -> AppError {
        match id {
            Some(id) => AppError::NotFound(format!("{entity_type} with id '{id}' not found")),
            None => AppError::NotFound(format!("{entity_type} not found")),
        }
    }

    /// Create not found error for entity by name
    pub fn not_found_by_name(entity_type: &str, name: &str) -> AppError {
        AppError::NotFound(format!("{entity_type} with name '{name}' not found"))
    }

    /// Create not found error for entity by email
    pub fn not_found_by_email(entity_type: &str, email: &str) -> AppError {
        AppError::NotFound(format!("{entity_type} with email '{email}' not found"))
    }

    /// Create not found error for entity by username
    pub fn not_found_by_username(entity_type: &str, username: &str) -> AppError {
        AppError::NotFound(format!(
            "{entity_type} with username '{username}' not found"
        ))
    }

    /// Create not found error for entity by slug
    pub fn not_found_by_slug(entity_type: &str, slug: &str) -> AppError {
        AppError::NotFound(format!("{entity_type} with slug '{slug}' not found"))
    }

    /// Create not found error for slug and type combination
    pub fn not_found_by_slug_and_type(
        entity_type: &str,
        slug: &str,
        category_type: &str,
    ) -> AppError {
        AppError::NotFound(format!(
            "{entity_type} with slug '{slug}' and type '{category_type}' not found"
        ))
    }

    /// Create a custom error response with correct path for handlers
    pub fn custom_error_response(
        path: &str,
        error_code: &str,
        message: &str,
        details: Option<serde_json::Value>,
    ) -> crate::response::ApiResponse<serde_json::Value> {
        crate::response::ApiResponse {
            timestamp: chrono::Utc::now().to_rfc3339(),
            path: path.to_string(),
            status: 400,
            code: error_code.to_string(),
            message: message.to_string(),
            data: details,
            error: None,
        }
    }

    /// Create conflict error for duplicate entity
    pub fn conflict(entity_type: &str, field: &str, value: &str) -> AppError {
        AppError::Conflict(format!(
            "{entity_type} with {field} '{value}' already exists"
        ))
    }

    /// Create validation error for invalid input
    pub fn validation_error(field: &str, message: &str) -> AppError {
        AppError::Validation(format!("Invalid {field}: {message}"))
    }

    /// Create unauthorized error
    pub fn unauthorized(message: &str) -> AppError {
        AppError::Unauthorized(message.to_string())
    }

    /// Create forbidden error
    pub fn forbidden(message: &str) -> AppError {
        AppError::Forbidden(message.to_string())
    }

    /// Create bad request error
    pub fn bad_request(message: &str) -> AppError {
        AppError::BadRequest(message.to_string())
    }

    /// Create internal server error
    pub fn internal_error(message: &str) -> AppError {
        AppError::Internal(anyhow::anyhow!(message.to_string()))
    }

    /// Create database error
    pub fn database_error(error: impl std::error::Error + Send + Sync + 'static) -> AppError {
        AppError::Internal(anyhow::anyhow!(error))
    }

    /// Create serialization error
    pub fn serialization_error(error: serde_json::Error) -> AppError {
        AppError::Serialization(error)
    }

    /// Create error response for handlers
    pub fn create_error_response(
        path: String,
        status: axum::http::StatusCode,
        code: &str,
        error_message: &str,
    ) -> axum::response::Response {
        use crate::response::error_response;
        error_response(path, status, code, error_message).into_response()
    }
}

/// Macro to handle service calls with proper error path handling
#[macro_export]
macro_rules! handle_service_result {
    ($service_call:expr, $path:expr) => {
        match $service_call.await {
            Ok(result) => result,
            Err($crate::errors::AppError::NotFound(error_msg)) => {
                let response = $crate::utils::ErrorHelper::create_error_response(
                    $path.to_string(),
                    axum::http::StatusCode::NOT_FOUND,
                    "SYS06",
                    &error_msg,
                );
                return Ok(response);
            }
            Err(e) => return Err(e),
        }
    };
}
