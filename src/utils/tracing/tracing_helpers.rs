use axum::{extract::Request, http::HeaderMap};
use tracing::{debug, error, info, warn};

/// Extract correlation ID from request headers
pub fn get_correlation_id_from_headers(headers: &HeaderMap) -> Option<String> {
    headers
        .get("x-correlation-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
}

/// Extract correlation ID from request extensions
pub fn get_correlation_id_from_request(request: &Request) -> Option<String> {
    use crate::routes::middleware::request_tracing::RequestInfo;

    request
        .extensions()
        .get::<RequestInfo>()
        .map(|info| info.correlation_id.clone())
}

/// Structured logging macros with correlation ID support
#[macro_export]
macro_rules! log_info {
    ($correlation_id:expr, $($arg:tt)*) => {
        if let Some(id) = $correlation_id {
            tracing::info!(correlation_id = %id, $($arg)*);
        } else {
            tracing::info!($($arg)*);
        }
    };
}

#[macro_export]
macro_rules! log_warn {
    ($correlation_id:expr, $($arg:tt)*) => {
        if let Some(id) = $correlation_id {
            tracing::warn!(correlation_id = %id, $($arg)*);
        } else {
            tracing::warn!($($arg)*);
        }
    };
}

#[macro_export]
macro_rules! log_error {
    ($correlation_id:expr, $($arg:tt)*) => {
        if let Some(id) = $correlation_id {
            tracing::error!(correlation_id = %id, $($arg)*);
        } else {
            tracing::error!($($arg)*);
        }
    };
}

#[macro_export]
macro_rules! log_debug {
    ($correlation_id:expr, $($arg:tt)*) => {
        if let Some(id) = $correlation_id {
            tracing::debug!(correlation_id = %id, $($arg)*);
        } else {
            tracing::debug!($($arg)*);
        }
    };
}

/// Helper struct for structured logging with correlation ID
pub struct CorrelatedLogger {
    correlation_id: Option<String>,
}

impl CorrelatedLogger {
    /// Create logger from request headers
    pub fn from_headers(headers: &HeaderMap) -> Self {
        Self {
            correlation_id: get_correlation_id_from_headers(headers),
        }
    }

    /// Create logger from request
    pub fn from_request(request: &Request) -> Self {
        Self {
            correlation_id: get_correlation_id_from_request(request),
        }
    }

    /// Create logger with explicit correlation ID
    pub fn with_id(correlation_id: String) -> Self {
        Self {
            correlation_id: Some(correlation_id),
        }
    }

    /// Create logger without correlation ID
    pub fn without_id() -> Self {
        Self {
            correlation_id: None,
        }
    }

    /// Log info message
    pub fn info(&self, message: &str) {
        if let Some(ref id) = self.correlation_id {
            info!(correlation_id = %id, "{}", message);
        } else {
            info!("{}", message);
        }
    }

    /// Log info message with fields
    pub fn info_with_fields<F>(&self, message: &str, fields: F)
    where
        F: FnOnce() -> Vec<(&'static str, String)>,
    {
        let field_pairs = fields();
        if let Some(ref id) = self.correlation_id {
            info!(correlation_id = %id, "{}", message);
            for (key, value) in field_pairs {
                // Note: This is simplified - in practice you'd use structured logging
                info!(correlation_id = %id, field_key = %key, field_value = %value, "{}", message);
            }
        } else {
            info!("{}", message);
            for (key, value) in field_pairs {
                info!(field_key = %key, field_value = %value, "{}", message);
            }
        }
    }

    /// Log warning message
    pub fn warn(&self, message: &str) {
        if let Some(ref id) = self.correlation_id {
            warn!(correlation_id = %id, "{}", message);
        } else {
            warn!("{}", message);
        }
    }

    /// Log error message
    pub fn error(&self, message: &str) {
        if let Some(ref id) = self.correlation_id {
            error!(correlation_id = %id, "{}", message);
        } else {
            error!("{}", message);
        }
    }

    /// Log debug message
    pub fn debug(&self, message: &str) {
        if let Some(ref id) = self.correlation_id {
            debug!(correlation_id = %id, "{}", message);
        } else {
            debug!("{}", message);
        }
    }

    /// Get correlation ID
    pub fn correlation_id(&self) -> Option<&str> {
        self.correlation_id.as_deref()
    }
}

/// Trait for services that support correlated logging
pub trait CorrelatedLogging {
    /// Log with correlation ID if available
    fn log_info(&self, correlation_id: Option<&str>, message: &str) {
        if let Some(id) = correlation_id {
            info!(correlation_id = %id, "{}", message);
        } else {
            info!("{}", message);
        }
    }

    fn log_warn(&self, correlation_id: Option<&str>, message: &str) {
        if let Some(id) = correlation_id {
            warn!(correlation_id = %id, "{}", message);
        } else {
            warn!("{}", message);
        }
    }

    fn log_error(&self, correlation_id: Option<&str>, message: &str) {
        if let Some(id) = correlation_id {
            error!(correlation_id = %id, "{}", message);
        } else {
            error!("{}", message);
        }
    }

    fn log_debug(&self, correlation_id: Option<&str>, message: &str) {
        if let Some(id) = correlation_id {
            debug!(correlation_id = %id, "{}", message);
        } else {
            debug!("{}", message);
        }
    }
}
