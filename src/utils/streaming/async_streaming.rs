use crate::errors::Result;
use base64::Engine;
use futures::stream::{self, Stream};
use serde::{Deserialize, Serialize};
use std::pin::Pin;
use tokio::sync::mpsc;
use uuid::Uuid;

/// Async streaming response for large datasets
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct StreamingResponse<T> {
    pub items: Vec<T>,
    pub has_more: bool,
    pub cursor: Option<String>,
    pub total_processed: usize,
}

impl<T> StreamingResponse<T> {
    pub fn new(
        items: Vec<T>,
        has_more: bool,
        cursor: Option<String>,
        total_processed: usize,
    ) -> Self {
        Self {
            items,
            has_more,
            cursor,
            total_processed,
        }
    }
}

/// Cursor-based pagination request
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CursorPaginationRequest {
    pub cursor: Option<String>,
    pub limit: usize,
    pub batch_size: Option<usize>,
}

impl Default for CursorPaginationRequest {
    fn default() -> Self {
        Self {
            cursor: None,
            limit: 100,
            batch_size: Some(50),
        }
    }
}

/// Streaming pagination request
#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct StreamingPaginationRequest {
    pub initial_cursor: Option<String>,
    pub page_size: usize,
    pub max_items: Option<usize>,
    pub timeout_seconds: Option<u64>,
}

impl Default for StreamingPaginationRequest {
    fn default() -> Self {
        Self {
            initial_cursor: None,
            page_size: 50,
            max_items: Some(1000),
            timeout_seconds: Some(30),
        }
    }
}

/// Trait for entities that support cursor-based pagination
pub trait CursorPaginated {
    fn get_cursor(&self) -> String;
    fn from_cursor(cursor: &str) -> Result<Self>
    where
        Self: Sized;
}

/// Trait for repositories that support streaming
#[async_trait::async_trait]
pub trait StreamingRepository<T>: Send + Sync
where
    T: Send + Sync + CursorPaginated,
{
    /// Stream items with cursor-based pagination
    async fn stream_items(
        &self,
        request: StreamingPaginationRequest,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<T>> + Send>>>;

    /// Get items with cursor-based pagination
    async fn get_items_cursor(
        &self,
        request: CursorPaginationRequest,
    ) -> Result<StreamingResponse<T>>;
}

/// Async streaming utilities
pub struct AsyncStreamingUtils;

impl AsyncStreamingUtils {
    /// Create a streaming response from a vector with cursor pagination
    pub fn create_streaming_response<T: CursorPaginated + Clone>(
        items: Vec<T>,
        limit: usize,
        _cursor: Option<String>,
    ) -> StreamingResponse<T> {
        let has_more = items.len() > limit;
        let actual_items = if has_more {
            items[..limit].to_vec()
        } else {
            items
        };

        let next_cursor = if has_more {
            actual_items.last().map(|item| item.get_cursor())
        } else {
            None
        };

        let items_len = actual_items.len();
        StreamingResponse::new(actual_items, has_more, next_cursor, items_len)
    }

    /// Process items in batches with progress tracking
    pub async fn process_batches<T, F, Fut>(
        items: Vec<T>,
        batch_size: usize,
        processor: F,
    ) -> Result<Vec<T>>
    where
        T: Send + Sync + Clone,
        F: Fn(Vec<T>) -> Fut + Send + Sync,
        Fut: std::future::Future<Output = Result<Vec<T>>> + Send,
    {
        let mut results = Vec::new();
        let chunks: Vec<Vec<T>> = items
            .chunks(batch_size)
            .map(|chunk| chunk.to_vec())
            .collect();
        let total_chunks = chunks.len();

        for (i, chunk) in chunks.into_iter().enumerate() {
            tracing::debug!(
                "Processing batch {}/{} with {} items",
                i + 1,
                total_chunks,
                chunk.len()
            );
            let processed_chunk = processor(chunk).await?;
            results.extend(processed_chunk);
        }

        Ok(results)
    }

    /// Create a stream from a vector with backpressure control
    pub fn create_controlled_stream<T>(
        items: Vec<T>,
        buffer_size: usize,
    ) -> Pin<Box<dyn Stream<Item = T> + Send>>
    where
        T: Send + 'static,
    {
        let (tx, rx) = mpsc::channel(buffer_size);
        let items = items.into_iter();

        tokio::spawn(async move {
            for item in items {
                if tx.send(item).await.is_err() {
                    break;
                }
            }
        });

        Box::pin(stream::unfold(rx, |mut rx| async move {
            rx.recv().await.map(|item| (item, rx))
        }))
    }

    /// Merge multiple streams with ordering
    pub fn merge_streams<T>(
        streams: Vec<Pin<Box<dyn Stream<Item = T> + Send>>>,
    ) -> Pin<Box<dyn Stream<Item = T> + Send>>
    where
        T: Send + 'static,
    {
        Box::pin(stream::select_all(streams))
    }
}

/// UUID-based cursor implementation
pub struct UuidCursor {
    pub id: Uuid,
    pub timestamp: i64,
}

impl UuidCursor {
    pub fn new(id: Uuid, timestamp: i64) -> Self {
        Self { id, timestamp }
    }

    pub fn encode(&self) -> String {
        base64::engine::general_purpose::STANDARD.encode(format!("{}:{}", self.id, self.timestamp))
    }

    pub fn decode(cursor: &str) -> Result<Self> {
        let decoded = base64::engine::general_purpose::STANDARD
            .decode(cursor)
            .map_err(|e| crate::errors::AppError::BadRequest(format!("Invalid cursor: {e}")))?;

        let cursor_str = String::from_utf8(decoded).map_err(|e| {
            crate::errors::AppError::BadRequest(format!("Invalid cursor format: {e}"))
        })?;

        let parts: Vec<&str> = cursor_str.split(':').collect();
        if parts.len() != 2 {
            return Err(crate::errors::AppError::BadRequest(
                "Invalid cursor format".into(),
            ));
        }

        let id = Uuid::parse_str(parts[0]).map_err(|e| {
            crate::errors::AppError::BadRequest(format!("Invalid UUID in cursor: {e}"))
        })?;
        let timestamp = parts[1].parse::<i64>().map_err(|e| {
            crate::errors::AppError::BadRequest(format!("Invalid timestamp in cursor: {e}"))
        })?;

        Ok(Self { id, timestamp })
    }
}

/// Progress tracking for long-running operations
#[derive(Debug, Clone, Serialize)]
pub struct ProgressTracker {
    pub total_items: usize,
    pub processed_items: usize,
    pub current_batch: usize,
    pub total_batches: usize,
    pub estimated_remaining_seconds: Option<f64>,
}

impl ProgressTracker {
    pub fn new(total_items: usize, batch_size: usize) -> Self {
        let total_batches = total_items.div_ceil(batch_size);
        Self {
            total_items,
            processed_items: 0,
            current_batch: 0,
            total_batches,
            estimated_remaining_seconds: None,
        }
    }

    pub fn update_progress(&mut self, processed: usize, elapsed_seconds: f64) {
        self.processed_items += processed;
        self.current_batch += 1;

        if elapsed_seconds > 0.0 && self.processed_items > 0 {
            let items_per_second = self.processed_items as f64 / elapsed_seconds;
            let remaining_items = self.total_items - self.processed_items;
            self.estimated_remaining_seconds = Some(remaining_items as f64 / items_per_second);
        }
    }

    pub fn progress_percentage(&self) -> f64 {
        if self.total_items == 0 {
            0.0
        } else {
            (self.processed_items as f64 / self.total_items as f64) * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    #[test]
    fn test_uuid_cursor_encoding_decoding() {
        let id = Uuid::new_v4();
        let timestamp = 1234567890;
        let cursor = UuidCursor::new(id, timestamp);

        let encoded = cursor.encode();
        let decoded = UuidCursor::decode(&encoded).unwrap();

        assert_eq!(decoded.id, id);
        assert_eq!(decoded.timestamp, timestamp);
    }

    #[test]
    fn test_progress_tracker() {
        let mut tracker = ProgressTracker::new(100, 10);
        assert_eq!(tracker.total_batches, 10);
        assert_eq!(tracker.progress_percentage(), 0.0);

        tracker.update_progress(10, 1.0);
        assert_eq!(tracker.processed_items, 10);
        assert_eq!(tracker.progress_percentage(), 10.0);
        assert!(tracker.estimated_remaining_seconds.is_some());
    }
}
