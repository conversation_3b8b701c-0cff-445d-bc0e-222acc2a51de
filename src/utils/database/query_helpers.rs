use crate::errors::Result;
use crate::map_db_error;
use sqlx::{FromRow, PgPool, postgres::PgRow};
use uuid::Uuid;

/// Database query helper functions to reduce duplicate code
pub struct QueryHelper;

impl QueryHelper {
    /// Find entity by ID with error mapping
    pub async fn find_by_id<T>(pool: &PgPool, query: &str, id: &Uuid) -> Result<Option<T>>
    where
        T: for<'r> FromRow<'r, PgRow> + Send + Unpin,
    {
        let entity = map_db_error!(
            sqlx::query_as::<_, T>(query)
                .bind(id)
                .fetch_optional(pool)
                .await
        )?;
        Ok(entity)
    }

    /// Find entity by string field with error mapping
    pub async fn find_by_string_field<T>(
        pool: &PgPool,
        query: &str,
        value: &str,
    ) -> Result<Option<T>>
    where
        T: for<'r> FromRow<'r, PgRow> + Send + Unpin,
    {
        let entity = map_db_error!(
            sqlx::query_as::<_, T>(query)
                .bind(value)
                .fetch_optional(pool)
                .await
        )?;
        Ok(entity)
    }

    /// Find all entities with error mapping
    pub async fn find_all<T>(pool: &PgPool, query: &str) -> Result<Vec<T>>
    where
        T: for<'r> FromRow<'r, PgRow> + Send + Unpin,
    {
        let entities = map_db_error!(sqlx::query_as::<_, T>(query).fetch_all(pool).await)?;
        Ok(entities)
    }

    /// Execute update/insert/delete query
    pub async fn execute(pool: &PgPool, query: &str) -> Result<u64> {
        let result = map_db_error!(sqlx::query(query).execute(pool).await)?;
        Ok(result.rows_affected())
    }

    /// Check if entity exists by ID
    pub async fn exists_by_id(pool: &PgPool, table: &str, id: &Uuid) -> Result<bool> {
        let query = format!("SELECT EXISTS(SELECT 1 FROM {table} WHERE id = $1)");
        let exists: bool = map_db_error!(
            sqlx::query_scalar::<_, bool>(&query)
                .bind(id)
                .fetch_one(pool)
                .await
        )?;
        Ok(exists)
    }

    /// Count entities in table
    pub async fn count(pool: &PgPool, table: &str) -> Result<i64> {
        let query = format!("SELECT COUNT(*) FROM {table}");
        let count: i64 = map_db_error!(sqlx::query_scalar::<_, i64>(&query).fetch_one(pool).await)?;
        Ok(count)
    }
}

/// Pagination query helper
pub struct PaginationQueryHelper;

impl PaginationQueryHelper {
    /// Get paginated results with count
    pub async fn get_paginated<T>(
        pool: &PgPool,
        base_query: &str,
        count_query: &str,
        page: i32,
        limit: i32,
    ) -> Result<(Vec<T>, i64)>
    where
        T: for<'r> FromRow<'r, PgRow> + Send + Unpin,
    {
        let offset = (page - 1) * limit;

        // Get data
        let data = map_db_error!(
            sqlx::query_as::<_, T>(base_query)
                .bind(limit)
                .bind(offset)
                .fetch_all(pool)
                .await
        )?;

        // Get total count
        let total: i64 = map_db_error!(
            sqlx::query_scalar::<_, i64>(count_query)
                .fetch_one(pool)
                .await
        )?;

        Ok((data, total))
    }
}
