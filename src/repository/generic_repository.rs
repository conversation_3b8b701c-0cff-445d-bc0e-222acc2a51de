use crate::errors::Result;
use std::future::Future;
use uuid::Uuid;

/// Generic repository trait to standardize repository interfaces
/// This reduces duplicate code across different repository implementations
pub trait GenericRepository<T, CreateParams, UpdateParams> {
    /// Find entity by ID
    fn find_by_id(&self, id: &Uuid) -> impl Future<Output = Result<Option<T>>> + Send;

    /// Find entity by string field (email, username, slug, etc.)
    fn find_by_string_field(
        &self,
        field: &str,
        value: &str,
    ) -> impl Future<Output = Result<Option<T>>> + Send;

    /// Create new entity
    fn create(&self, params: CreateParams) -> impl Future<Output = Result<T>> + Send;

    /// Update existing entity
    fn update(&self, id: &Uuid, params: UpdateParams) -> impl Future<Output = Result<T>> + Send;

    /// Delete entity by ID
    fn delete(&self, id: &Uuid) -> impl Future<Output = Result<()>> + Send;

    /// Find all entities
    fn find_all(&self) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Check if entity exists by ID
    fn exists_by_id(&self, id: &Uuid) -> impl Future<Output = Result<bool>> + Send;

    /// Check if entity exists by string field
    fn exists_by_string_field(
        &self,
        field: &str,
        value: &str,
    ) -> impl Future<Output = Result<bool>> + Send;

    /// Count total entities
    fn count(&self) -> impl Future<Output = Result<i64>> + Send;
}

/// Generic repository trait for entities with pagination support
pub trait GenericPaginatedRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Find all entities with pagination
    fn find_all_paginated(
        &self,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send;

    /// Find entities by condition with pagination
    fn find_by_condition_paginated<F>(
        &self,
        condition: F,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send
    where
        F: FnOnce(&str) -> String + Send + Sync;
}

/// Generic repository trait for entities with search support
pub trait GenericSearchableRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Search entities by query
    fn search(&self, query: &str) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Search entities by query with pagination
    fn search_paginated(
        &self,
        query: &str,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send;
}

/// Generic repository trait for entities with soft delete support
pub trait GenericSoftDeleteRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Soft delete entity (mark as deleted)
    fn soft_delete(&self, id: &Uuid) -> impl Future<Output = Result<()>> + Send;

    /// Restore soft deleted entity
    fn restore(&self, id: &Uuid) -> impl Future<Output = Result<()>> + Send;

    /// Find all entities including soft deleted
    fn find_all_with_deleted(&self) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Find entity by ID including soft deleted
    fn find_by_id_with_deleted(&self, id: &Uuid) -> impl Future<Output = Result<Option<T>>> + Send;
}

/// Generic repository trait for entities with status support
pub trait GenericStatusRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Update entity status
    fn update_status(&self, id: &Uuid, status: &str) -> impl Future<Output = Result<T>> + Send;

    /// Find entities by status
    fn find_by_status(&self, status: &str) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Find entities by status with pagination
    fn find_by_status_paginated(
        &self,
        status: &str,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send;

    /// Count entities by status
    fn count_by_status(&self, status: &str) -> impl Future<Output = Result<i64>> + Send;
}

/// Generic repository trait for entities with category support
pub trait GenericCategorizedRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Find entities by category ID
    fn find_by_category_id(
        &self,
        category_id: &Uuid,
    ) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Find entities by category ID with pagination
    fn find_by_category_id_paginated(
        &self,
        category_id: &Uuid,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send;

    /// Count entities by category ID
    fn count_by_category_id(&self, category_id: &Uuid) -> impl Future<Output = Result<i64>> + Send;
}

/// Generic repository trait for entities with user ownership
pub trait GenericUserOwnedRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Find entities by user ID
    fn find_by_user_id(&self, user_id: &Uuid) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Find entities by user ID with pagination
    fn find_by_user_id_paginated(
        &self,
        user_id: &Uuid,
        page: i32,
        limit: i32,
    ) -> impl Future<Output = Result<(Vec<T>, i64)>> + Send;

    /// Count entities by user ID
    fn count_by_user_id(&self, user_id: &Uuid) -> impl Future<Output = Result<i64>> + Send;

    /// Check if entity belongs to user
    fn belongs_to_user(
        &self,
        entity_id: &Uuid,
        user_id: &Uuid,
    ) -> impl Future<Output = Result<bool>> + Send;
}

/// Generic repository trait for entities with batch operations
pub trait GenericBatchRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Create multiple entities
    fn create_batch(
        &self,
        params: Vec<CreateParams>,
    ) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Update multiple entities
    fn update_batch(
        &self,
        updates: Vec<(Uuid, UpdateParams)>,
    ) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Delete multiple entities
    fn delete_batch(&self, ids: Vec<Uuid>) -> impl Future<Output = Result<()>> + Send;

    /// Find multiple entities by IDs
    fn find_by_ids(&self, ids: Vec<Uuid>) -> impl Future<Output = Result<Vec<T>>> + Send;
}

/// Generic repository trait for entities with audit trail
pub trait GenericAuditableRepository<T, CreateParams, UpdateParams>:
    GenericRepository<T, CreateParams, UpdateParams>
{
    /// Get entity audit trail
    fn get_audit_trail(&self, id: &Uuid) -> impl Future<Output = Result<Vec<AuditEntry>>> + Send;

    /// Get entity version history
    fn get_version_history(&self, id: &Uuid) -> impl Future<Output = Result<Vec<T>>> + Send;

    /// Revert entity to specific version
    fn revert_to_version(&self, id: &Uuid, version: i32) -> impl Future<Output = Result<T>> + Send;
}

/// Audit entry for tracking changes
#[derive(Debug, Clone)]
pub struct AuditEntry {
    pub id: Uuid,
    pub entity_id: Uuid,
    pub entity_type: String,
    pub action: String,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub changed_by: Uuid,
    pub changed_at: chrono::DateTime<chrono::Utc>,
}

/// Common repository error types
#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Entity not found: {entity_type} with ID {id}")]
    NotFound { entity_type: String, id: Uuid },

    #[error("Entity already exists: {entity_type} with {field} '{value}'")]
    AlreadyExists {
        entity_type: String,
        field: String,
        value: String,
    },

    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),
}

/// Repository result type
pub type RepositoryResult<T> = std::result::Result<T, RepositoryError>;
