use crate::{database::Database, errors::Result};
use sqlx::{PgPool, Postgres, Transaction};

/// Async repository wrapper for database operations
#[derive(Clone)]
pub struct AsyncRepository {
    database: Database,
}

impl AsyncRepository {
    pub fn new(database: Database) -> Self {
        Self { database }
    }

    /// Execute a read-only database operation
    pub async fn execute_readonly<F, R>(&self, operation: F) -> Result<R>
    where
        F: for<'a> FnOnce(
            &'a PgPool,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R>> + Send + 'a>,
        >,
        R: Send + 'static,
    {
        operation(self.database.pool()).await
    }

    /// Execute a read-write database operation
    pub async fn execute<F, R>(&self, operation: F) -> Result<R>
    where
        F: for<'a> FnOnce(
            &'a PgPool,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R>> + Send + 'a>,
        >,
        R: Send + 'static,
    {
        operation(self.database.pool()).await
    }

    /// Execute a database transaction
    pub async fn execute_transaction<F, R>(&self, operation: F) -> Result<R>
    where
        F: for<'a> FnOnce(
            &'a mut Transaction<'_, Postgres>,
        ) -> std::pin::Pin<
            Box<dyn std::future::Future<Output = Result<R>> + Send + 'a>,
        >,
        R: Send + 'static,
    {
        let mut tx = self
            .database
            .pool()
            .begin()
            .await
            .map_err(|e| crate::errors::AppError::Internal(e.into()))?;

        let result = operation(&mut tx).await;

        match result {
            Ok(value) => {
                tx.commit()
                    .await
                    .map_err(|e| crate::errors::AppError::Internal(e.into()))?;
                Ok(value)
            }
            Err(e) => {
                tx.rollback()
                    .await
                    .map_err(|rollback_e| crate::errors::AppError::Internal(rollback_e.into()))?;
                Err(e)
            }
        }
    }
}

/// Helper function for database operations
pub async fn execute_db_operation<T, F>(db: Database, operation: F) -> Result<T>
where
    F: for<'a> FnOnce(
        &'a PgPool,
    ) -> std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<T>> + Send + 'a>,
    >,
    T: Send + 'static,
{
    operation(db.pool()).await
}
