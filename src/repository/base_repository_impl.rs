use crate::{database::Database, errors::Result, utils::QueryHelper};
use std::marker::PhantomData;
use uuid::Uuid;

/// Base repository implementation providing default implementations
/// for generic repository traits
pub struct BaseRepositoryImpl<T, CreateParams, UpdateParams> {
    database: Database,
    table_name: String,
    entity_type: String,
    _phantom: PhantomData<(T, CreateParams, UpdateParams)>,
}

impl<T, CreateParams, UpdateParams> BaseRepositoryImpl<T, CreateParams, UpdateParams> {
    /// Create new base repository implementation
    pub fn new(database: Database, table_name: String, entity_type: String) -> Self {
        Self {
            database,
            table_name,
            entity_type,
            _phantom: PhantomData,
        }
    }

    /// Get database pool
    fn pool(&self) -> &sqlx::PgPool {
        self.database.pool()
    }

    /// Get table name
    fn table_name(&self) -> &str {
        &self.table_name
    }

    /// Get entity type
    fn entity_type(&self) -> &str {
        &self.entity_type
    }
}

/// Default implementation for basic repository operations
impl<T, CreateParams, UpdateParams> BaseRepositoryImpl<T, CreateParams, UpdateParams> {
    /// Find entity by ID
    pub async fn find_by_id(&self, id: &Uuid) -> Result<Option<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
    {
        let query = format!("SELECT * FROM {} WHERE id = $1", self.table_name());
        QueryHelper::find_by_id(self.pool(), &query, id).await
    }

    /// Find entity by string field
    pub async fn find_by_string_field(&self, field: &str, value: &str) -> Result<Option<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
    {
        let query = format!("SELECT * FROM {} WHERE {} = $1", self.table_name(), field);
        QueryHelper::find_by_string_field(self.pool(), &query, value).await
    }

    /// Delete entity by ID
    pub async fn delete(&self, id: &Uuid) -> Result<()> {
        let query = format!("DELETE FROM {} WHERE id = $1", self.table_name());
        let rows_affected = QueryHelper::execute(self.pool(), &query).await?;

        if rows_affected == 0 {
            return Err(crate::errors::AppError::NotFound(format!(
                "{} with ID '{}' not found",
                self.entity_type(),
                id
            )));
        }

        Ok(())
    }

    /// Find all entities
    pub async fn find_all(&self, page: Option<i64>, limit: Option<i64>) -> Result<Vec<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
    {
        let mut query = format!(
            "SELECT * FROM {} ORDER BY created_at DESC",
            self.table_name()
        );
        if let (Some(p), Some(l)) = (page, limit) {
            let offset = (p - 1) * l;
            query = format!("{} LIMIT {} OFFSET {}", query, l, offset);
        }
        QueryHelper::find_all(self.pool(), &query).await
    }

    /// Check if entity exists by ID
    pub async fn exists_by_id(&self, id: &Uuid) -> Result<bool> {
        QueryHelper::exists_by_id(self.pool(), self.table_name(), id).await
    }

    /// Check if entity exists by string field
    pub async fn exists_by_string_field(&self, field: &str, value: &str) -> Result<bool> {
        let query = format!(
            "SELECT EXISTS(SELECT 1 FROM {} WHERE {} = $1)",
            self.table_name(),
            field
        );
        let exists: bool = crate::map_db_error!(
            sqlx::query_scalar::<_, bool>(&query)
                .bind(value)
                .fetch_one(self.pool())
                .await
        )?;
        Ok(exists)
    }

    /// Count total entities
    pub async fn count(&self) -> Result<i64> {
        QueryHelper::count(self.pool(), self.table_name()).await
    }
}

/// Builder for creating base repository implementations
pub struct BaseRepositoryBuilder<T, CreateParams, UpdateParams> {
    database: Option<Database>,
    table_name: Option<String>,
    entity_type: Option<String>,
    _phantom: PhantomData<(T, CreateParams, UpdateParams)>,
}

impl<T, CreateParams, UpdateParams> BaseRepositoryBuilder<T, CreateParams, UpdateParams> {
    pub fn new() -> Self {
        Self {
            database: None,
            table_name: None,
            entity_type: None,
            _phantom: PhantomData,
        }
    }

    pub fn with_database(mut self, database: Database) -> Self {
        self.database = Some(database);
        self
    }

    pub fn with_table_name(mut self, table_name: String) -> Self {
        self.table_name = Some(table_name);
        self
    }

    pub fn with_entity_type(mut self, entity_type: String) -> Self {
        self.entity_type = Some(entity_type);
        self
    }

    pub fn build(self) -> Result<BaseRepositoryImpl<T, CreateParams, UpdateParams>> {
        let database = self.database.ok_or_else(|| {
            crate::errors::AppError::Internal(anyhow::anyhow!("Database is required"))
        })?;

        let table_name = self.table_name.ok_or_else(|| {
            crate::errors::AppError::Internal(anyhow::anyhow!("Table name is required"))
        })?;

        let entity_type = self.entity_type.ok_or_else(|| {
            crate::errors::AppError::Internal(anyhow::anyhow!("Entity type is required"))
        })?;

        Ok(BaseRepositoryImpl::new(database, table_name, entity_type))
    }
}

impl<T, CreateParams, UpdateParams> Default
    for BaseRepositoryBuilder<T, CreateParams, UpdateParams>
{
    fn default() -> Self {
        Self::new()
    }
}
