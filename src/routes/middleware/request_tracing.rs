use crate::errors::{clear_current_path, set_current_path};
use axum::{
    extract::{ConnectInfo, MatchedPath, Request},
    http::{HeaderMap, HeaderValue},
    middleware::Next,
    response::Response,
};
use std::{net::SocketAddr, time::Instant};
use tracing::{Instrument, info, info_span, warn};
use uuid::Uuid;

/// Request information for tracing
#[derive(Debug, Clone)]
pub struct RequestInfo {
    pub correlation_id: String,
    pub method: String,
    pub path: String,
    pub user_agent: Option<String>,
    pub client_ip: String,
    pub start_time: Instant,
}

/// Request tracing middleware that adds correlation ID and structured logging
pub async fn request_tracing_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    matched_path: Option<MatchedPath>,
    mut request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();

    // Generate correlation ID
    let correlation_id = Uuid::new_v4().to_string();

    // Extract request information
    let method = request.method().to_string();
    let uri = request.uri().clone();
    let path = matched_path
        .map(|mp| mp.as_str().to_string())
        .unwrap_or_else(|| uri.path().to_string());
    let client_ip = addr.ip().to_string();

    // Extract User-Agent
    let user_agent = request
        .headers()
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    // Add correlation ID to request headers
    request.headers_mut().insert(
        "x-correlation-id",
        HeaderValue::from_str(&correlation_id)
            .unwrap_or_else(|_| HeaderValue::from_static("invalid")),
    );

    // Store request info in extensions
    let request_info = RequestInfo {
        correlation_id: correlation_id.clone(),
        method: method.clone(),
        path: path.clone(),
        user_agent: user_agent.clone(),
        client_ip: client_ip.clone(),
        start_time,
    };
    request.extensions_mut().insert(request_info);

    // Create tracing span for this request
    let span = info_span!(
        "http_request",
        correlation_id = %correlation_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent.as_deref().unwrap_or("unknown")
    );

    // Log request start
    info!(
        correlation_id = %correlation_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent.as_deref().unwrap_or("unknown"),
        "Request started"
    );

    // Process request within the span
    async move {
        let response = next.run(request).await;

        let duration = start_time.elapsed();
        let status = response.status().as_u16();

        // Log request completion
        if status >= 400 {
            if status >= 500 {
                warn!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed with server error"
                );
            } else {
                info!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed with client error"
                );
            }
        } else {
            info!(
                correlation_id = %correlation_id,
                method = %method,
                path = %path,
                status = %status,
                duration_ms = %duration.as_millis(),
                "Request completed successfully"
            );
        }

        response
    }
    .instrument(span)
    .await
}

/// Extract correlation ID from request headers
pub fn extract_correlation_id(headers: &HeaderMap) -> Option<String> {
    headers
        .get("x-correlation-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
}

/// Extract request info from request extensions
pub fn extract_request_info(request: &Request) -> Option<&RequestInfo> {
    request.extensions().get::<RequestInfo>()
}

/// Helper macro for logging with correlation ID
#[macro_export]
macro_rules! log_with_correlation {
    ($level:ident, $request:expr, $($arg:tt)*) => {
        if let Some(info) = $crate::routes::middleware::request_tracing::extract_request_info($request) {
            tracing::$level!(
                correlation_id = %info.correlation_id,
                $($arg)*
            );
        } else {
            tracing::$level!($($arg)*);
        }
    };
}

/// Helper function to add correlation ID to response headers
pub fn add_correlation_id_to_response(mut response: Response, correlation_id: &str) -> Response {
    if let Ok(header_value) = HeaderValue::from_str(correlation_id) {
        response
            .headers_mut()
            .insert("x-correlation-id", header_value);
    }
    response
}

/// Enhanced request tracing middleware with response correlation ID
pub async fn enhanced_request_tracing_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    matched_path: Option<MatchedPath>,
    mut request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();

    // Generate or extract correlation ID
    let correlation_id = request
        .headers()
        .get("x-correlation-id")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    // Extract request information
    let method = request.method().to_string();
    let uri = request.uri().clone();
    let path = matched_path
        .map(|mp| mp.as_str().to_string())
        .unwrap_or_else(|| uri.path().to_string());
    let client_ip = addr.ip().to_string();

    // Extract User-Agent
    let user_agent = request
        .headers()
        .get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    // Add correlation ID to request headers if not present
    if !request.headers().contains_key("x-correlation-id") {
        request.headers_mut().insert(
            "x-correlation-id",
            HeaderValue::from_str(&correlation_id)
                .unwrap_or_else(|_| HeaderValue::from_static("invalid")),
        );
    }

    // Store request info in extensions
    let request_info = RequestInfo {
        correlation_id: correlation_id.clone(),
        method: method.clone(),
        path: path.clone(),
        user_agent: user_agent.clone(),
        client_ip: client_ip.clone(),
        start_time,
    };
    request.extensions_mut().insert(request_info);

    // Create tracing span for this request
    let span = info_span!(
        "http_request",
        correlation_id = %correlation_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent.as_deref().unwrap_or("unknown")
    );

    // Log request start
    info!(
        correlation_id = %correlation_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent.as_deref().unwrap_or("unknown"),
        "Request started"
    );

    // Clone correlation_id for use after the async block
    let correlation_id_for_response = correlation_id.clone();

    // Clone path for use after the async block
    let path_for_response = path.clone();

    // Process request within the span
    let mut response = async move {
        let response = next.run(request).await;

        let duration = start_time.elapsed();
        let status = response.status().as_u16();

        // Log request completion with appropriate level
        match status {
            200..=299 => {
                info!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed successfully"
                );
            }
            400..=499 => {
                info!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed with client error"
                );
            }
            500..=599 => {
                warn!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed with server error"
                );
            }
            _ => {
                info!(
                    correlation_id = %correlation_id,
                    method = %method,
                    path = %path,
                    status = %status,
                    duration_ms = %duration.as_millis(),
                    "Request completed"
                );
            }
        }

        response
    }
    .instrument(span)
    .await;

    // Add correlation ID to response headers
    response = add_correlation_id_to_response(response, &correlation_id_for_response);

    // Clear current path after request processing
    clear_current_path();

    // Set current path for error handling (in case of errors)
    set_current_path(path_for_response);

    response
}
