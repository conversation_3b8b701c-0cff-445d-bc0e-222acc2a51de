use crate::{constants::system as system_codes, response::ApiResponse};
use axum::{
    Json,
    extract::rejection::JsonRejection,
    http::StatusCode,
    response::{IntoResponse, Response},
};
/// Global error handler for various rejection types
pub async fn handle_error(err: Box<dyn std::error::Error + Send + Sync>) -> Response {
    // Try to downcast to JsonRejection
    if let Some(json_rejection) = err.downcast_ref::<JsonRejection>() {
        return handle_json_rejection_ref(json_rejection).await;
    }

    // Default error response for other types
    let error_response = ApiResponse::<()>::error(
        "unknown".to_string(),
        500,
        system_codes::ERROR_INTERNAL.to_string(),
        "Internal server error".to_string(),
    );

    (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response()
}

/// Handle JSON rejection specifically (by reference)
async fn handle_json_rejection_ref(rejection: &JsonRejection) -> Response {
    let (status, error_message) = match rejection {
        JsonRejection::JsonDataError(err) => (
            StatusCode::UNPROCESSABLE_ENTITY,
            format!("Failed to deserialize the JSON body into the target type: {err}"),
        ),
        JsonRejection::JsonSyntaxError(err) => {
            (StatusCode::BAD_REQUEST, format!("JSON syntax error: {err}"))
        }
        JsonRejection::MissingJsonContentType(_) => (
            StatusCode::UNSUPPORTED_MEDIA_TYPE,
            "Expected request with `Content-Type: application/json`".to_string(),
        ),
        _ => (StatusCode::BAD_REQUEST, "Invalid JSON request".to_string()),
    };

    let error_response = ApiResponse::<()>::error(
        "unknown".to_string(), // We don't have access to request path here
        status.as_u16(),
        system_codes::ERROR_VALIDATION.to_string(),
        error_message,
    );

    (status, Json(error_response)).into_response()
}
