use axum::{
    body::Body,
    http::{Request, Response, StatusCode},
    middleware::Next,
};
use std::time::Instant;
use tower_http::compression::{CompressionLayer, CompressionLevel, Predicate};
use tracing::{debug, info};

/// Compression configuration
#[derive(Debug, Clone)]
pub struct CompressionConfig {
    pub enabled: bool,
    pub level: CompressionLevel,
    pub min_size: usize,
    pub max_size: usize,
    pub content_types: Vec<String>,
}

impl Default for CompressionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            level: CompressionLevel::Default,
            min_size: 1024,             // 1KB minimum
            max_size: 10 * 1024 * 1024, // 10MB maximum
            content_types: vec![
                "application/json".to_string(),
                "text/plain".to_string(),
                "text/html".to_string(),
                "text/css".to_string(),
                "application/javascript".to_string(),
                "text/javascript".to_string(),
            ],
        }
    }
}

/// Custom predicate for compression
#[derive(Debug, <PERSON>lone)]
pub struct CustomCompressionPredicate {
    config: CompressionConfig,
}

impl CustomCompressionPredicate {
    pub fn new(config: CompressionConfig) -> Self {
        Self { config }
    }
}

impl Predicate for CustomCompressionPredicate {
    fn should_compress<B>(&self, response: &Response<B>) -> bool {
        if !self.config.enabled {
            return false;
        }

        // Check content type
        if let Some(content_type) = response.headers().get("content-type") {
            if let Ok(content_type_str) = content_type.to_str() {
                let should_compress = self
                    .config
                    .content_types
                    .iter()
                    .any(|ct| content_type_str.starts_with(ct));

                if !should_compress {
                    debug!(
                        "Skipping compression for content type: {}",
                        content_type_str
                    );
                    return false;
                }
            }
        }

        // Check content length
        if let Some(content_length) = response.headers().get("content-length") {
            if let Ok(length_str) = content_length.to_str() {
                if let Ok(length) = length_str.parse::<usize>() {
                    if length < self.config.min_size {
                        debug!("Skipping compression for small content: {} bytes", length);
                        return false;
                    }
                    if length > self.config.max_size {
                        debug!("Skipping compression for large content: {} bytes", length);
                        return false;
                    }
                }
            }
        }

        // Check status code
        let status = response.status();
        if !status.is_success() && status != StatusCode::NOT_MODIFIED {
            debug!("Skipping compression for status code: {}", status);
            return false;
        }

        true
    }
}

/// Compression middleware with metrics
pub async fn compression_middleware(
    request: Request<Body>,
    next: Next,
) -> Result<Response<Body>, StatusCode> {
    let start = Instant::now();
    let path = request.uri().path().to_string();
    let method = request.method().clone();

    // Check if client accepts compression
    let accepts_compression = request
        .headers()
        .get("accept-encoding")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.contains("gzip") || s.contains("deflate") || s.contains("br"))
        .unwrap_or(false);

    if !accepts_compression {
        debug!("Client does not accept compression for {} {}", method, path);
        let response = next.run(request).await;
        return Ok(response);
    }

    let response = next.run(request).await;
    let elapsed = start.elapsed();

    // Log compression metrics
    if let Some(content_length) = response.headers().get("content-length") {
        if let Ok(length_str) = content_length.to_str() {
            if let Ok(length) = length_str.parse::<usize>() {
                info!(
                    "Response compression metrics - Path: {} {}, Size: {} bytes, Time: {:?}",
                    method, path, length, elapsed
                );
            }
        }
    }

    Ok(response)
}

/// Create compression layer with custom configuration
pub fn create_compression_layer(
    config: CompressionConfig,
) -> CompressionLayer<CustomCompressionPredicate> {
    let predicate = CustomCompressionPredicate::new(config.clone());

    CompressionLayer::new()
        .quality(config.level)
        .compress_when(predicate)
}

/// Compression utilities
pub struct CompressionUtils;

impl CompressionUtils {
    /// Estimate compression ratio based on content type and size
    pub fn estimate_compression_ratio(content_type: &str, size: usize) -> f64 {
        match content_type {
            "application/json" => {
                if size < 1024 {
                    0.9 // 10% compression
                } else if size < 10240 {
                    0.7 // 30% compression
                } else {
                    0.5 // 50% compression
                }
            }
            "text/plain" => {
                if size < 1024 {
                    0.8 // 20% compression
                } else {
                    0.6 // 40% compression
                }
            }
            "text/html" => {
                if size < 1024 {
                    0.85 // 15% compression
                } else {
                    0.65 // 35% compression
                }
            }
            _ => 0.9, // Default: 10% compression
        }
    }

    /// Calculate bandwidth savings
    pub fn calculate_bandwidth_savings(
        original_size: usize,
        compressed_size: usize,
        requests_per_second: f64,
    ) -> f64 {
        let savings_per_request = (original_size - compressed_size) as f64;
        savings_per_request * requests_per_second
    }

    /// Get recommended compression level based on server load
    pub fn get_recommended_compression_level(
        cpu_usage: f64,
        memory_usage: f64,
    ) -> CompressionLevel {
        match (cpu_usage, memory_usage) {
            (cpu, _) if cpu > 80.0 => CompressionLevel::Fastest,
            (cpu, _) if cpu > 60.0 => CompressionLevel::Default,
            (_, mem) if mem > 80.0 => CompressionLevel::Fastest,
            _ => CompressionLevel::Default,
        }
    }
}

/// Compression metrics
#[derive(Debug, Clone)]
pub struct CompressionMetrics {
    pub total_requests: u64,
    pub compressed_requests: u64,
    pub total_original_size: u64,
    pub total_compressed_size: u64,
    pub average_compression_ratio: f64,
    pub bandwidth_saved_mb: f64,
}

impl Default for CompressionMetrics {
    fn default() -> Self {
        Self::new()
    }
}

impl CompressionMetrics {
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            compressed_requests: 0,
            total_original_size: 0,
            total_compressed_size: 0,
            average_compression_ratio: 0.0,
            bandwidth_saved_mb: 0.0,
        }
    }

    pub fn update(&mut self, original_size: usize, compressed_size: usize) {
        self.total_requests += 1;
        self.compressed_requests += 1;
        self.total_original_size += original_size as u64;
        self.total_compressed_size += compressed_size as u64;

        self.average_compression_ratio =
            self.total_compressed_size as f64 / self.total_original_size as f64;

        self.bandwidth_saved_mb =
            (self.total_original_size - self.total_compressed_size) as f64 / (1024.0 * 1024.0);
    }

    pub fn compression_percentage(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            (self.compressed_requests as f64 / self.total_requests as f64) * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compression_ratio_estimation() {
        let json_ratio = CompressionUtils::estimate_compression_ratio("application/json", 2048);
        assert!(json_ratio > 0.0 && json_ratio < 1.0);

        let text_ratio = CompressionUtils::estimate_compression_ratio("text/plain", 5120);
        assert!(text_ratio > 0.0 && text_ratio < 1.0);
    }

    #[test]
    fn test_compression_metrics() {
        let mut metrics = CompressionMetrics::new();
        metrics.update(1000, 700);
        metrics.update(2000, 1200);

        assert_eq!(metrics.total_requests, 2);
        assert_eq!(metrics.compressed_requests, 2);
        assert_eq!(metrics.compression_percentage(), 100.0);
        assert!(metrics.average_compression_ratio > 0.0);
    }

    #[test]
    fn test_compression_level_recommendation() {
        let level = CompressionUtils::get_recommended_compression_level(85.0, 50.0);
        assert_eq!(level, CompressionLevel::Fastest);

        let level = CompressionUtils::get_recommended_compression_level(30.0, 30.0);
        assert_eq!(level, CompressionLevel::Default);
    }
}
