use axum::{
    extract::{ConnectInfo, Request},
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use std::{
    collections::HashMap,
    net::SocketAddr,
    sync::{Arc, OnceLock},
    time::{Duration, Instant},
};

use tokio::sync::Mutex;
use tracing::{debug, info, warn};

/// Advanced in-memory rate limiter with memory leak prevention
///
/// Improvements:
/// - Background cleanup task to prevent memory leaks
/// - Maximum size limit to prevent unlimited growth
/// - Periodic global cleanup of expired entries
/// - Monitoring capabilities for observability
#[derive(Clone)]
pub struct RateLimiter {
    limits: Arc<Mutex<HashMap<String, Vec<Instant>>>>,
    window: Duration,
    max_requests: u32,
    max_entries: usize, // Maximum number of IP entries to prevent unlimited growth
    cleanup_handle: Arc<tokio::sync::OnceCell<tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON><()>>>,
}

impl RateLimiter {
    pub fn new(window: Duration, max_requests: u32) -> Self {
        Self::with_max_entries(window, max_requests, 10000) // Default max 10k IP entries
    }

    pub fn with_max_entries(window: Duration, max_requests: u32, max_entries: usize) -> Self {
        let rate_limiter = Self {
            limits: Arc::new(Mutex::new(HashMap::new())),
            window,
            max_requests,
            max_entries,
            cleanup_handle: Arc::new(tokio::sync::OnceCell::new()),
        };

        // Start background cleanup task
        rate_limiter.start_background_cleanup();
        rate_limiter
    }

    /// Start background cleanup task to prevent memory leaks
    fn start_background_cleanup(&self) {
        let limits = self.limits.clone();
        let window = self.window;
        let max_entries = self.max_entries;
        let cleanup_handle = self.cleanup_handle.clone();

        tokio::spawn(async move {
            let handle = tokio::spawn(async move {
                let mut interval = tokio::time::interval(Duration::from_secs(60)); // Cleanup every minute

                loop {
                    interval.tick().await;

                    match limits.try_lock() {
                        Ok(mut map) => {
                            let now = Instant::now();
                            // Remove expired entries
                            let removed_entries: Vec<_> = map
                                .extract_if(|_, times| {
                                    times.retain(|&time| now.duration_since(time) <= window);
                                    times.is_empty()
                                })
                                .collect();

                            let removed = removed_entries.len();

                            if removed > 0 {
                                info!("Rate limiter cleanup: removed {} expired entries", removed);
                            }

                            // If still over limit, remove oldest entries
                            if map.len() > max_entries {
                                // Collect keys to remove (avoid borrow checker issues)
                                let mut entries: Vec<_> = map
                                    .iter()
                                    .map(|(key, times)| {
                                        (
                                            key.clone(),
                                            times.first().copied().unwrap_or_else(Instant::now),
                                        )
                                    })
                                    .collect();
                                entries.sort_by_key(|(_, first_time)| *first_time);

                                let to_remove = map.len() - max_entries;
                                let keys_to_remove: Vec<_> = entries
                                    .iter()
                                    .take(to_remove)
                                    .map(|(key, _)| key.clone())
                                    .collect();

                                for key in keys_to_remove {
                                    map.remove(&key);
                                }

                                warn!(
                                    "Rate limiter: removed {} oldest entries to stay under limit",
                                    to_remove
                                );
                            }

                            if map.len() > max_entries / 2 {
                                debug!(
                                    "Rate limiter size: {} entries (max: {})",
                                    map.len(),
                                    max_entries
                                );
                            }
                        }
                        Err(_) => {
                            // Couldn't acquire lock, skip this cleanup cycle
                            debug!("Rate limiter cleanup skipped - mutex busy");
                        }
                    }
                }
            });

            let _ = cleanup_handle.set(handle);
        });
    }

    pub async fn check_rate_limit(&self, key: &str) -> bool {
        let mut limits = self.limits.lock().await;
        let now = Instant::now();

        // Clean up old entries for this key
        if let Some(times) = limits.get_mut(key) {
            times.retain(|&time| now.duration_since(time) <= self.window);
        }

        // Check if under limit
        let current_requests = limits.get(key).map(|v| v.len()).unwrap_or(0);

        if current_requests >= self.max_requests as usize {
            return false;
        }

        // Prevent unlimited growth - if we're at max capacity, reject new IPs
        if limits.len() >= self.max_entries && !limits.contains_key(key) {
            warn!(
                "Rate limiter at capacity ({} entries), rejecting new IP: {}",
                self.max_entries, key
            );
            return false;
        }

        // Add new request
        limits
            .entry(key.to_string())
            .or_insert_with(Vec::new)
            .push(now);
        true
    }

    /// Get current statistics (for monitoring)
    pub async fn get_stats(&self) -> RateLimiterStats {
        let limits = self.limits.lock().await;
        let now = Instant::now();
        let mut active_entries = 0;
        let mut total_requests = 0;

        for times in limits.values() {
            let active_requests: Vec<_> = times
                .iter()
                .filter(|&&time| now.duration_since(time) <= self.window)
                .collect();

            if !active_requests.is_empty() {
                active_entries += 1;
                total_requests += active_requests.len();
            }
        }

        RateLimiterStats {
            total_entries: limits.len(),
            active_entries,
            total_requests,
            max_entries: self.max_entries,
            window_seconds: self.window.as_secs(),
            max_requests_per_window: self.max_requests,
        }
    }

    /// Force cleanup of all expired entries (useful for testing)
    pub async fn force_cleanup(&self) -> usize {
        let mut limits = self.limits.lock().await;
        let now = Instant::now();

        let removed_entries: Vec<_> = limits
            .extract_if(|_, times| {
                times.retain(|&time| now.duration_since(time) <= self.window);
                times.is_empty()
            })
            .collect();

        let removed = removed_entries.len();
        if removed > 0 {
            info!("Force cleanup removed {} expired entries", removed);
        }

        removed
    }
}

/// Statistics for rate limiter monitoring
#[derive(Debug, Clone)]
pub struct RateLimiterStats {
    pub total_entries: usize,
    pub active_entries: usize,
    pub total_requests: usize,
    pub max_entries: usize,
    pub window_seconds: u64,
    pub max_requests_per_window: u32,
}

/// Rate limiting middleware for general API endpoints
pub async fn rate_limit_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    static RATE_LIMITER: OnceLock<RateLimiter> = OnceLock::new();
    let rate_limiter = RATE_LIMITER.get_or_init(|| RateLimiter::new(Duration::from_secs(1), 100));

    let client_ip = addr.ip().to_string();

    if !rate_limiter.check_rate_limit(&client_ip).await {
        tracing::warn!("Rate limit exceeded for IP: {}", client_ip);
        return Err(StatusCode::TOO_MANY_REQUESTS);
    }

    Ok(next.run(request).await)
}

/// Relaxed rate limiting for auth endpoints (including OAuth)
pub async fn auth_rate_limit_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    static AUTH_RATE_LIMITER: OnceLock<RateLimiter> = OnceLock::new();
    let auth_rate_limiter = AUTH_RATE_LIMITER.get_or_init(|| {
        RateLimiter::new(Duration::from_secs(30), 15) // 15 requests per 30 seconds
    });

    let client_ip = addr.ip().to_string();

    if !auth_rate_limiter.check_rate_limit(&client_ip).await {
        tracing::warn!("Auth rate limit exceeded for IP: {}", client_ip);
        return Err(StatusCode::TOO_MANY_REQUESTS);
    }

    Ok(next.run(request).await)
}
