use crate::{config::CorsConfig, utils::cors_constants::CorsConstants};
use axum::http::{HeaderName, HeaderValue, Method};
use tower_http::cors::{Any, CorsLayer};

/// Create CORS layer from configuration
pub fn create_cors_layer(config: &CorsConfig) -> CorsLayer {
    let mut cors = CorsLayer::new()
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
            Method::PATCH,
        ])
        .allow_credentials(config.allow_credentials);

    // Set allowed origins - NEVER use Any(*) with credentials=true
    if config.allow_credentials {
        // With credentials, must specify exact origins
        let origins: Result<Vec<HeaderValue>, _> = config
            .allowed_origins
            .iter()
            .map(|origin| origin.parse())
            .collect();

        if let Ok(valid_origins) = origins {
            cors = cors.allow_origin(valid_origins);
        } else {
            // Fallback to safe defaults for development using pre-validated constants
            cors = cors.allow_origin([
                CorsConstants::localhost_3000().clone(),
                CorsConstants::localhost_3001().clone(),
                CorsConstants::localhost_ip_3000().clone(),
            ]);
        }
    } else {
        // Without credentials, can use Any
        cors = cors.allow_origin(Any);
    }

    // Set allowed headers - explicitly include Authorization
    let mut headers: Vec<HeaderName> = vec![
        axum::http::header::AUTHORIZATION,
        axum::http::header::CONTENT_TYPE,
        axum::http::header::ACCEPT,
    ];

    // Add custom headers from config
    for header in &config.allowed_headers {
        if let Ok(header_name) = HeaderName::from_lowercase(header.to_lowercase().as_bytes()) {
            if !headers.contains(&header_name) {
                headers.push(header_name);
            }
        }
    }

    cors.allow_headers(headers)
}

/// Create a development CORS layer with explicit origins (SAFE)
pub fn create_permissive_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin(CorsConstants::dev_origins().to_vec())
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
            Method::PATCH,
        ])
        .allow_headers([
            axum::http::header::AUTHORIZATION,
            axum::http::header::CONTENT_TYPE,
            axum::http::header::ACCEPT,
            HeaderName::from_static("x-api-type"),
            HeaderName::from_static("x-requested-with"),
        ])
        .allow_credentials(true)
}

/// Create a development-friendly CORS layer
pub fn create_dev_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin([
            CorsConstants::localhost_3000().clone(),
            CorsConstants::localhost_3001().clone(),
            CorsConstants::localhost_8080().clone(),
            CorsConstants::localhost_ip_3000().clone(),
        ])
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
            Method::PATCH,
        ])
        .allow_headers([
            axum::http::header::AUTHORIZATION,
            axum::http::header::CONTENT_TYPE,
            axum::http::header::ACCEPT,
            HeaderName::from_static("x-api-type"),
        ])
        .allow_credentials(true)
}

/// Create a production CORS layer with strict origins
pub fn create_prod_cors_layer(allowed_origins: &[String]) -> CorsLayer {
    let origins: Vec<HeaderValue> = allowed_origins
        .iter()
        .filter_map(|origin| origin.parse().ok())
        .collect();

    CorsLayer::new()
        .allow_origin(origins)
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
        ])
        .allow_headers([
            axum::http::header::AUTHORIZATION,
            axum::http::header::CONTENT_TYPE,
            axum::http::header::ACCEPT,
            HeaderName::from_static("x-api-type"),
        ])
        .allow_credentials(true)
}

/// Create CORS layer without credentials (can use Any origin)
pub fn create_public_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
        ])
        .allow_headers([axum::http::header::CONTENT_TYPE, axum::http::header::ACCEPT])
        .allow_credentials(false) // No credentials = can use Any
}
