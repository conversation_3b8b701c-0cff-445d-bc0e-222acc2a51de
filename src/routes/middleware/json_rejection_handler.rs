use crate::{constants::system as system_codes, response::ApiResponse};
use axum::{
    Json,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};

/// Middleware to handle JSON rejection errors and convert them to proper API responses
pub async fn json_rejection_handler(req: Request<axum::body::Body>, next: Next) -> Response {
    let path = req.uri().path().to_string();

    let response = next.run(req).await;

    // Check if the response is a JSON rejection error (status 422)
    if response.status() == StatusCode::UNPROCESSABLE_ENTITY {
        // Extract the error message from the response body if possible
        // For now, we'll create a generic JSON error response
        let error_response = ApiResponse::<()>::error(
            path,
            422,
            system_codes::ERROR_VALIDATION.to_string(),
            "Failed to deserialize the JSON body into the target type".to_string(),
        );

        return (StatusCode::UNPROCESSABLE_ENTITY, <PERSON><PERSON>(error_response)).into_response();
    }

    response
}
