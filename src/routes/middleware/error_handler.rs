use crate::{
    errors::{App<PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON>ontext},
    routes::middleware::request_tracing::extract_request_info,
};
use axum::{extract::Request, response::Response};

/// Custom error handler for Axum that automatically adds request context to errors
pub async fn custom_error_handler(err: AppError, req: Request) -> Response {
    // Extract path from request info
    let path = extract_request_info(&req)
        .map(|info| info.path.clone())
        .unwrap_or_else(|| "unknown".to_string());

    // Create error context
    let context = ErrorContext { path };

    // Create error response with context
    err.into_response_with_context(context)
}

/// Helper function to create error response with request context
pub fn create_error_response(error: AppError, request: &Request) -> Response {
    let path = extract_request_info(request)
        .map(|info| info.path.clone())
        .unwrap_or_else(|| "unknown".to_string());

    let context = ErrorContext { path };
    error.into_response_with_context(context)
}
