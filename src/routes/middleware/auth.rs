use crate::{
    constants::HEADER_API_TYPE, container::ServiceContainer, errors::AppError,
    modules::auth::models::TokenClaims,
};
use axum::{
    extract::{Request, State},
    http::HeaderMap,
    middleware::Next,
    response::Response,
};
use std::{future::Future, pin::Pin, sync::Arc};
use uuid::Uuid;

// Type aliases to reduce complexity
type AuthMiddlewareFuture = Pin<Box<dyn Future<Output = Result<Response, AppError>> + Send>>;

/// User information extracted from JWT token
#[derive(Debug, Clone)]
pub struct AuthenticatedUser {
    pub user_id: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
    pub permission_version: i32,
}

impl From<TokenClaims> for AuthenticatedUser {
    fn from(claims: TokenClaims) -> Self {
        Self {
            user_id: claims.sub,
            roles: claims.roles,
            permissions: claims.permissions,
            permission_version: claims.pver,
        }
    }
}

/// Extracts user information and stores in request extensions
pub async fn jwt_auth_middleware(
    State(container): State<Arc<ServiceContainer>>,
    mut request: Request,
    next: Next,
) -> Result<Response, AppError> {
    let headers = request.headers();

    // Extract user info from JWT
    let user_info = extract_user_from_headers(&container, headers).await?;

    // Store user info in request extensions for later use
    request.extensions_mut().insert(user_info);

    Ok(next.run(request).await)
}

/// Private API middleware - requires X-API-Type: private header and valid JWT
pub async fn private_api_middleware(
    State(container): State<Arc<ServiceContainer>>,
    mut request: Request,
    next: Next,
) -> Result<Response, AppError> {
    let headers = request.headers();

    // 1. Check X-API-Type header MUST be "private" using let chains
    if let Some(header) = headers.get(HEADER_API_TYPE)
        && let Ok(api_type_header) = header.to_str()
        && api_type_header == "private"
    {
        // Header is valid, continue
    } else {
        return Err(AppError::BadRequest(
            "Missing or invalid X-API-Type header".into(),
        ));
    }

    // 2. Extract and verify JWT token
    let user_info = extract_user_from_headers(&container, headers).await?;

    // Store user info in request extensions
    request.extensions_mut().insert(user_info);

    Ok(next.run(request).await)
}

/// Middleware factory to check specific permission
pub fn require_permission(
    permission: &'static str,
) -> impl Fn(Request, Next) -> AuthMiddlewareFuture + Clone {
    move |request: Request, next: Next| {
        let permission_to_check = permission;
        Box::pin(async move {
            let user_info =
                request
                    .extensions()
                    .get::<AuthenticatedUser>()
                    .ok_or(AppError::Unauthorized(
                        "Authenticated user not found in request".into(),
                    ))?;

            // Check for admin:all permission or specific permission
            let has_permission = user_info
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == permission_to_check);

            if !has_permission {
                return Err(AppError::Forbidden("Insufficient permissions".into()));
            }

            Ok(next.run(request).await)
        })
    }
}

/// Middleware factory to check specific role
pub fn require_role(role: &'static str) -> impl Fn(Request, Next) -> AuthMiddlewareFuture + Clone {
    move |request: Request, next: Next| {
        let role_to_check = role;
        Box::pin(async move {
            let user_info =
                request
                    .extensions()
                    .get::<AuthenticatedUser>()
                    .ok_or(AppError::Unauthorized(
                        "Authenticated user not found in request".into(),
                    ))?;

            if !user_info.roles.iter().any(|r| r == role_to_check) {
                return Err(AppError::Forbidden("Insufficient role".into()));
            }

            Ok(next.run(request).await)
        })
    }
}

/// Helper function to extract user information from headers
pub async fn extract_user_from_headers(
    container: &ServiceContainer,
    headers: &HeaderMap,
) -> Result<AuthenticatedUser, AppError> {
    let auth_header = headers
        .get("Authorization")
        .and_then(|header| header.to_str().ok());

    let auth_service = container.auth_service();

    // Get token from header using let chains
    let token = if let Some(header) = auth_header
        && let Some(token) = header.strip_prefix("Bearer ")
    {
        token
    } else {
        return Err(AppError::Unauthorized(
            "Missing or invalid Authorization header".into(),
        ));
    };

    // Verify token and extract claims
    let claims = auth_service.verify_token(token)?;

    // Parse user_id from claims
    let user_id = Uuid::parse_str(&claims.sub)
        .map_err(|_| AppError::Unauthorized("Invalid user ID in token".into()))?;

    // Get current permission version from database
    let user_service = container.user_service();
    let current_permission_version = user_service
        .get_permission_version(&user_id)
        .await?
        .ok_or(AppError::Unauthorized("User not found".into()))?; // User not found

    // Compare token permission version with database version
    if claims.pver < current_permission_version {
        return Err(AppError::PermissionChanged); // Token is outdated
    }

    Ok(AuthenticatedUser::from(claims))
}

/// Optional authentication middleware for hybrid routes
///
/// This middleware attempts to extract user information from JWT token if present,
/// but does NOT fail if authentication is missing or invalid.
///
/// Use cases:
/// - Routes that support both public and private access (e.g., categories, laptops)
/// - When combined with ApiType middleware to determine access level
///
/// If authentication succeeds, AuthenticatedUser is stored in request extensions.
/// If authentication fails or is missing, the request continues without user info.
pub async fn optional_auth_middleware(
    State(container): State<Arc<ServiceContainer>>,
    mut request: Request,
    next: Next,
) -> Response {
    let headers = request.headers();

    // Only try to extract user info if Authorization header is present
    if let Some(_auth_header) = headers.get("Authorization")
        && let Ok(user_info) = extract_user_from_headers(&container, headers).await
    {
        // Store user info in request extensions if authentication succeeded
        request.extensions_mut().insert(user_info);
    }
    // If no Authorization header is present, continue without user info
    // This allows the route to handle both authenticated and unauthenticated requests

    next.run(request).await
}
