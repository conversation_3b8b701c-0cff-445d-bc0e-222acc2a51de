pub mod api_type;
pub mod auth;
pub mod compression;
pub mod cors;
pub mod error_handler;
pub mod global_error_handler;
pub mod json_rejection_handler;
pub mod rate_limit;
pub mod request_tracing;
pub mod security_headers;

pub use api_type::{ApiType, api_type_middleware};

pub use auth::{
    AuthenticatedUser, jwt_auth_middleware, optional_auth_middleware, private_api_middleware,
    require_permission, require_role,
};

pub use cors::{
    create_cors_layer, create_dev_cors_layer, create_permissive_cors_layer, create_prod_cors_layer,
    create_public_cors_layer,
};

pub use request_tracing::{
    RequestInfo, enhanced_request_tracing_middleware, extract_correlation_id, extract_request_info,
    request_tracing_middleware,
};

pub use compression::{
    CompressionConfig, CompressionMetrics, CompressionUtils, compression_middleware,
    create_compression_layer,
};
pub use security_headers::security_headers_middleware;
