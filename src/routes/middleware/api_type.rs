use axum::{extract::Request, http::HeaderValue, middleware::Next, response::Response};
use serde::{Deserialize, Serialize};
use std::str::FromStr;

use crate::constants::HEADER_API_TYPE;

/// API Type enum to distinguish between Public and Private API access
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize, Default)]
pub enum ApiType {
    /// Public API - no authentication required, limited data
    #[default]
    Public,
    /// Private API - authentication required, full data access
    Private,
}

impl FromStr for ApiType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "public" => Ok(ApiType::Public),
            "private" => Ok(ApiType::Private),
            _ => Err(format!("Invalid API type: {s}")),
        }
    }
}

impl From<ApiType> for HeaderValue {
    fn from(api_type: ApiType) -> Self {
        match api_type {
            ApiType::Public => HeaderValue::from_static("public"),
            ApiType::Private => HeaderValue::from_static("private"),
        }
    }
}

/// Middleware to extract and parse X-API-Type header
///
/// This middleware:
/// 1. Reads the X-API-Type header from the request
/// 2. Defaults to "public" if header is missing
/// 3. Parses the header value to ApiType enum
/// 4. Injects the ApiType into request extensions for handlers to use
///
/// Note: This middleware only determines API type, it does NOT handle authentication
pub async fn api_type_middleware(
    mut request: Request,
    next: Next,
) -> Result<Response, axum::response::Response> {
    let headers = request.headers();

    // Extract X-API-Type header, default to "public" if not present
    let api_type_str = headers
        .get(HEADER_API_TYPE)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("public");

    // Parse to ApiType enum
    let api_type = match ApiType::from_str(api_type_str) {
        Ok(api_type) => api_type,
        Err(_) => {
            // If invalid header value, default to public and continue
            // Could also return an error here depending on requirements
            ApiType::Public
        }
    };

    // Inject ApiType into request extensions for handlers to access
    request.extensions_mut().insert(api_type);

    Ok(next.run(request).await)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_type_from_str() {
        assert_eq!(ApiType::from_str("public").unwrap(), ApiType::Public);
        assert_eq!(ApiType::from_str("private").unwrap(), ApiType::Private);
        assert_eq!(ApiType::from_str("PUBLIC").unwrap(), ApiType::Public);
        assert_eq!(ApiType::from_str("PRIVATE").unwrap(), ApiType::Private);
        assert!(ApiType::from_str("invalid").is_err());
    }

    #[test]
    fn test_api_type_default() {
        assert_eq!(ApiType::default(), ApiType::Public);
    }
}
