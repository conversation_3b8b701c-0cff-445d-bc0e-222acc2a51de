use chrono::{DateTime, NaiveDate, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::schema::{Currency, LaptopStatus, MarketRegion, PriceRegion, RamType, ScreenResolution};

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxLaptop {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct SqlxSpecification {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub cpu_brand: String,
    pub cpu_model: String,
    pub ram_size: i32,
    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: bool,
    pub storage_type: String,
    pub storage_capacity: i32,
    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,
    pub gpu_type: String,
    pub gpu_model: Option<String>,
    pub screen_size: Decimal,
    pub screen_resolution: ScreenResolution,
    pub refresh_rate: i32,
    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxPrice {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub currency: Currency,
    pub source: Option<String>,
    pub region: PriceRegion,
    pub effective_date: NaiveDate,
    pub is_current: bool,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

// Note: From implementations moved to models.rs to avoid conflicts

// ===== CREATE PARAMS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateLaptopParams {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSpecificationParams {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub cpu_brand: String,
    pub cpu_model: String,
    pub ram_size: i32,
    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: bool,
    pub storage_type: String,
    pub storage_capacity: i32,
    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,
    pub gpu_type: String,
    pub gpu_model: Option<String>,
    pub screen_size: Decimal,
    pub screen_resolution: ScreenResolution,
    pub refresh_rate: i32,
    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePriceParams {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub currency: Currency,
    pub source: Option<String>,
    pub region: PriceRegion,
    pub effective_date: NaiveDate,
    pub is_current: bool,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl From<super::models::NewLaptop> for CreateLaptopParams {
    fn from(new_laptop: super::models::NewLaptop) -> Self {
        Self {
            id: new_laptop.id,
            category_id: new_laptop.category_id,
            brand: new_laptop.brand,
            model: new_laptop.model,
            full_name: new_laptop.full_name,
            slug: new_laptop.slug,
            sku: new_laptop.sku,
            market_region: new_laptop.market_region,
            release_date: new_laptop.release_date,
            description: new_laptop.description,
            image_urls: new_laptop
                .image_urls
                .unwrap_or_default()
                .into_iter()
                .flatten()
                .collect(),
            status: new_laptop.status,
            is_featured: new_laptop.is_featured,
            view_count: 0,
            created_by: new_laptop.created_by,
            updated_by: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }
}

impl From<super::models::NewSpecification> for CreateSpecificationParams {
    fn from(new_spec: super::models::NewSpecification) -> Self {
        Self {
            id: new_spec.id,
            laptop_id: new_spec.laptop_id,
            cpu_brand: new_spec.cpu_brand,
            cpu_model: new_spec.cpu_model,
            ram_size: new_spec.ram_size,
            ram_type: new_spec.ram_type,
            ram_slots_total: new_spec.ram_slots_total,
            ram_slots_available: new_spec.ram_slots_available,
            ram_max_capacity: new_spec.ram_max_capacity,
            ram_soldered: new_spec.ram_soldered,
            storage_type: new_spec.storage_type,
            storage_capacity: new_spec.storage_capacity,
            storage_slots_total: new_spec.storage_slots_total,
            storage_slots_available: new_spec.storage_slots_available,
            storage_max_capacity: new_spec.storage_max_capacity,
            gpu_type: new_spec.gpu_type,
            gpu_model: new_spec.gpu_model,
            screen_size: new_spec.screen_size,
            screen_resolution: new_spec.screen_resolution,
            refresh_rate: new_spec.refresh_rate,
            weight: new_spec.weight,
            operating_system: new_spec.operating_system,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }
}

impl From<super::models::NewPrice> for CreatePriceParams {
    fn from(new_price: super::models::NewPrice) -> Self {
        Self {
            id: new_price.id,
            laptop_id: new_price.laptop_id,
            min_price: new_price.min_price,
            max_price: new_price.max_price,
            currency: new_price.currency,
            source: new_price.source,
            region: new_price.region,
            effective_date: new_price.effective_date,
            is_current: new_price.is_current,
            created_by: new_price.created_by,
            updated_by: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }
}
