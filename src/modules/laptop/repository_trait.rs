use crate::errors::Result;
use crate::modules::laptop::models::{
    LaptopFullView, LaptopPaginationRequest, LaptopStatus, NewLaptop, PaginatedLaptopsDetailed,
    PaginatedLaptopsFull, PaginatedLaptopsPublic,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait LaptopRepositoryTrait: Send + Sync {
    async fn create(&self, laptop: NewLaptop) -> Result<LaptopFullView>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<LaptopFullView>>;
    async fn find_by_slug(&self, slug: &str) -> Result<Option<LaptopFullView>>;
    async fn update(&self, id: &Uuid, laptop: NewLaptop) -> Result<LaptopFullView>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn get_public_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic>;
    async fn get_detailed_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed>;
    async fn get_full_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull>;
    async fn update_status(&self, id: &Uuid, status: LaptopStatus, updated_by: &Uuid)
    -> Result<()>;
    async fn update_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()>;
    async fn increment_view_count(&self, id: &Uuid) -> Result<()>;
    async fn exists_by_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
    async fn exists_by_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
}

pub type DynLaptopRepo = Arc<dyn LaptopRepositoryTrait>;
