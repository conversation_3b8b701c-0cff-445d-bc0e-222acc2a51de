use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::laptop::models::{
        LaptopDetailedView, LaptopFullView, LaptopPaginationRequest, LaptopPublicView,
        LaptopStatus, NewLaptop, NewPrice, NewSpecification, PaginatedLaptopsDetailed,
        PaginatedLaptopsFull, PaginatedLaptopsPublic, Price, Specification,
    },
    modules::laptop::sqlx_models::{
        CreateLaptopParams, CreatePriceParams, CreateSpecificationParams, SqlxLaptop, SqlxPrice,
        SqlxSpecification,
    },
    repository::base_repository::AsyncRepository,
    schema::queries,
    utils::pagination::PaginationMeta,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

use super::repository_trait::LaptopRepositoryTrait;

// ===== SQLX LAPTOP REPOSITORY =====

#[derive(Clone)]
pub struct SqlxLaptopRepository {
    base: AsyncRepository,
}

impl SqlxLaptopRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl LaptopRepositoryTrait for SqlxLaptopRepository {
    async fn create(&self, laptop: NewLaptop) -> Result<LaptopFullView> {
        let params = CreateLaptopParams::from(laptop);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let laptop = sqlx::query_as::<_, SqlxLaptop>(queries::CREATE_LAPTOP)
                        .bind(params.id)
                        .bind(params.category_id)
                        .bind(&params.brand)
                        .bind(&params.model)
                        .bind(&params.full_name)
                        .bind(&params.slug)
                        .bind(params.sku.as_ref())
                        .bind(params.market_region)
                        .bind(params.release_date)
                        .bind(params.description.as_ref())
                        .bind(&params.image_urls)
                        .bind(params.status)
                        .bind(params.is_featured)
                        .bind(params.view_count)
                        .bind(params.created_by)
                        .bind(params.updated_by)
                        .bind(params.created_at)
                        .bind(params.updated_at)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(LaptopFullView::from(laptop))
                })
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<LaptopFullView>> {
        let id = *id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let laptop = sqlx::query_as::<_, SqlxLaptop>(queries::FIND_LAPTOP_BY_ID)
                        .bind(id)
                        .fetch_optional(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(laptop.map(LaptopFullView::from))
                })
            })
            .await
    }

    async fn find_by_slug(&self, slug: &str) -> Result<Option<LaptopFullView>> {
        let slug = slug.to_string();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let laptop = sqlx::query_as::<_, SqlxLaptop>(queries::FIND_LAPTOP_BY_SLUG)
                        .bind(&slug)
                        .fetch_optional(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(laptop.map(LaptopFullView::from))
                })
            })
            .await
    }

    async fn update(&self, id: &Uuid, laptop: NewLaptop) -> Result<LaptopFullView> {
        let id = *id;
        let params = CreateLaptopParams::from(laptop);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let laptop = sqlx::query_as::<_, SqlxLaptop>(queries::UPDATE_LAPTOP)
                        .bind(id)
                        .bind(params.category_id)
                        .bind(&params.brand)
                        .bind(&params.model)
                        .bind(&params.full_name)
                        .bind(&params.slug)
                        .bind(params.sku.as_ref())
                        .bind(params.market_region)
                        .bind(params.release_date)
                        .bind(params.description.as_ref())
                        .bind(&params.image_urls)
                        .bind(params.status)
                        .bind(params.is_featured)
                        .bind(params.updated_by)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(LaptopFullView::from(laptop))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query(queries::DELETE_LAPTOP)
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Laptop not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn get_public_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let page = request.page.unwrap_or(1);
                    let per_page = request.per_page.unwrap_or(10);
                    let offset = (page - 1) * per_page;
                    let limit = per_page;

                    // Simplified query for now

                    // Execute query (simplified - in real implementation you'd need to handle dynamic params properly)
                    let base_query = "SELECT * FROM laptops WHERE status = 'published' ORDER BY created_at DESC LIMIT $1 OFFSET $2";
                    let laptops = sqlx::query_as::<_, SqlxLaptop>(base_query)
                        .bind(limit)
                        .bind(offset)
                        .fetch_all(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    // Get total count
                    let total_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM laptops WHERE status = 'published'")
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    let laptops: Vec<LaptopPublicView> = laptops.into_iter().map(LaptopPublicView::from).collect();

                    let pagination_meta = PaginationMeta::new(
                        page,
                        per_page,
                        total_count,
                    );

                    Ok(PaginatedLaptopsPublic {
                        laptops,
                        meta: pagination_meta,
                    })
                })
            })
            .await
    }

    async fn get_detailed_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let page = request.page.unwrap_or(1);
                    let per_page = request.per_page.unwrap_or(10);
                    let offset = (page - 1) * per_page;
                    let limit = per_page;

                    let laptops = sqlx::query_as::<_, SqlxLaptop>(
                        "SELECT * FROM laptops ORDER BY created_at DESC LIMIT $1 OFFSET $2",
                    )
                    .bind(limit)
                    .bind(offset)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    let total_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM laptops")
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    let laptops: Vec<LaptopDetailedView> =
                        laptops.into_iter().map(LaptopDetailedView::from).collect();

                    let pagination_meta = PaginationMeta::new(page, per_page, total_count);

                    Ok(PaginatedLaptopsDetailed {
                        laptops,
                        meta: pagination_meta,
                    })
                })
            })
            .await
    }

    async fn get_full_with_pagination(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let page = request.page.unwrap_or(1);
                    let per_page = request.per_page.unwrap_or(10);
                    let offset = (page - 1) * per_page;
                    let limit = per_page;

                    let laptops = sqlx::query_as::<_, SqlxLaptop>(
                        "SELECT * FROM laptops ORDER BY created_at DESC LIMIT $1 OFFSET $2",
                    )
                    .bind(limit)
                    .bind(offset)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    let total_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM laptops")
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    let laptops: Vec<LaptopFullView> =
                        laptops.into_iter().map(LaptopFullView::from).collect();

                    let pagination_meta = PaginationMeta::new(page, per_page, total_count);

                    Ok(PaginatedLaptopsFull {
                        laptops,
                        meta: pagination_meta,
                    })
                })
            })
            .await
    }

    async fn update_status(
        &self,
        id: &Uuid,
        status: LaptopStatus,
        updated_by: &Uuid,
    ) -> Result<()> {
        let id = *id;
        let updated_by = *updated_by;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query(queries::UPDATE_LAPTOP_STATUS)
                        .bind(id)
                        .bind(status)
                        .bind(updated_by)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Laptop not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn update_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()> {
        let id = *id;
        let updated_by = *updated_by;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query(queries::UPDATE_LAPTOP_FEATURED)
                        .bind(id)
                        .bind(is_featured)
                        .bind(updated_by)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Laptop not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn increment_view_count(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    sqlx::query(queries::INCREMENT_LAPTOP_VIEW_COUNT)
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }

    async fn exists_by_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let slug = slug.to_string();
        let exclude_id = exclude_id.copied();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let exists: bool = if let Some(exclude_id) = exclude_id {
                        sqlx::query_scalar(queries::EXISTS_LAPTOP_BY_SLUG)
                            .bind(&slug)
                            .bind(exclude_id)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?
                    } else {
                        sqlx::query_scalar("SELECT EXISTS(SELECT 1 FROM laptops WHERE slug = $1)")
                            .bind(&slug)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?
                    };

                    Ok(exists)
                })
            })
            .await
    }

    async fn exists_by_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let sku = sku.to_string();
        let exclude_id = exclude_id.copied();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let exists: bool = if let Some(exclude_id) = exclude_id {
                        sqlx::query_scalar(queries::EXISTS_LAPTOP_BY_SKU)
                            .bind(&sku)
                            .bind(exclude_id)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?
                    } else {
                        sqlx::query_scalar("SELECT EXISTS(SELECT 1 FROM laptops WHERE sku = $1)")
                            .bind(&sku)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?
                    };

                    Ok(exists)
                })
            })
            .await
    }
}

// ===== SPECIFICATION REPOSITORY =====

#[async_trait]
pub trait SpecificationRepositoryTrait: Send + Sync {
    async fn create(&self, spec: NewSpecification) -> Result<Specification>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Specification>>;
    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>>;
    async fn find_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Specification>>;
    async fn update(&self, id: &Uuid, spec: NewSpecification) -> Result<Specification>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
}

pub type DynSpecificationRepo = Arc<dyn SpecificationRepositoryTrait>;

#[derive(Clone)]
pub struct SqlxSpecificationRepository {
    base: AsyncRepository,
}

impl SqlxSpecificationRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl SpecificationRepositoryTrait for SqlxSpecificationRepository {
    async fn create(&self, spec: NewSpecification) -> Result<Specification> {
        let params = CreateSpecificationParams::from(spec);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let spec =
                        sqlx::query_as::<_, SqlxSpecification>(queries::CREATE_SPECIFICATION)
                            .bind(params.id)
                            .bind(params.laptop_id)
                            .bind(&params.cpu_brand)
                            .bind(&params.cpu_model)
                            .bind(params.ram_size)
                            .bind(params.ram_type)
                            .bind(params.ram_slots_total)
                            .bind(params.ram_slots_available)
                            .bind(params.ram_max_capacity)
                            .bind(params.ram_soldered)
                            .bind(&params.storage_type)
                            .bind(params.storage_capacity)
                            .bind(params.storage_slots_total)
                            .bind(params.storage_slots_available)
                            .bind(params.storage_max_capacity)
                            .bind(&params.gpu_type)
                            .bind(&params.gpu_model)
                            .bind(params.screen_size)
                            .bind(params.screen_resolution)
                            .bind(params.refresh_rate)
                            .bind(params.weight)
                            .bind(&params.operating_system)
                            .bind(params.created_at)
                            .bind(params.updated_at)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Specification::from(spec))
                })
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Specification>> {
        let id = *id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let spec =
                        sqlx::query_as::<_, SqlxSpecification>(queries::FIND_SPECIFICATION_BY_ID)
                            .bind(id)
                            .fetch_optional(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(spec.map(Specification::from))
                })
            })
            .await
    }

    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>> {
        let laptop_id = *laptop_id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let spec = sqlx::query_as::<_, SqlxSpecification>(
                        queries::FIND_SPECIFICATION_BY_LAPTOP_ID,
                    )
                    .bind(laptop_id)
                    .fetch_optional(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(spec.map(Specification::from))
                })
            })
            .await
    }

    async fn find_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Specification>> {
        if laptop_ids.is_empty() {
            return Ok(vec![]);
        }
        self.base
            .execute_readonly(|pool| {
                let laptop_ids = laptop_ids.to_vec();
                Box::pin(async move {
                    let query =
                        "SELECT * FROM specifications WHERE laptop_id = ANY($1)".to_string();
                    let specs = sqlx::query_as::<_, SqlxSpecification>(&query)
                        .bind(&laptop_ids)
                        .fetch_all(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;
                    Ok(specs.into_iter().map(Specification::from).collect())
                })
            })
            .await
    }

    async fn update(&self, id: &Uuid, spec: NewSpecification) -> Result<Specification> {
        let id = *id;
        let params = CreateSpecificationParams::from(spec);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let spec =
                        sqlx::query_as::<_, SqlxSpecification>(queries::UPDATE_SPECIFICATION)
                            .bind(id)
                            .bind(params.laptop_id)
                            .bind(&params.cpu_brand)
                            .bind(&params.cpu_model)
                            .bind(params.ram_size)
                            .bind(params.ram_type)
                            .bind(params.ram_slots_total)
                            .bind(params.ram_slots_available)
                            .bind(params.ram_max_capacity)
                            .bind(params.ram_soldered)
                            .bind(&params.storage_type)
                            .bind(params.storage_capacity)
                            .bind(params.storage_slots_total)
                            .bind(params.storage_slots_available)
                            .bind(params.storage_max_capacity)
                            .bind(&params.gpu_type)
                            .bind(&params.gpu_model)
                            .bind(params.screen_size)
                            .bind(params.screen_resolution)
                            .bind(params.refresh_rate)
                            .bind(params.weight)
                            .bind(&params.operating_system)
                            .fetch_one(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Specification::from(spec))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query(queries::DELETE_SPECIFICATION)
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Specification not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    sqlx::query(queries::DELETE_SPECIFICATION_BY_LAPTOP_ID)
                        .bind(laptop_id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }
}

// ===== PRICE REPOSITORY =====

#[async_trait]
pub trait PriceRepositoryTrait: Send + Sync {
    async fn create(&self, price: NewPrice) -> Result<Price>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Price>>;
    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn find_current_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn find_current_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Price>>;
    async fn update(&self, id: &Uuid, price: NewPrice) -> Result<Price>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()>;
}

pub type DynPriceRepo = Arc<dyn PriceRepositoryTrait>;

#[derive(Clone)]
pub struct SqlxPriceRepository {
    base: AsyncRepository,
}

impl SqlxPriceRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl PriceRepositoryTrait for SqlxPriceRepository {
    async fn create(&self, price: NewPrice) -> Result<Price> {
        let params = CreatePriceParams::from(price);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let price = sqlx::query_as::<_, SqlxPrice>(queries::CREATE_PRICE)
                        .bind(params.id)
                        .bind(params.laptop_id)
                        .bind(params.min_price)
                        .bind(params.max_price)
                        .bind(params.currency)
                        .bind(&params.source)
                        .bind(params.region)
                        .bind(params.effective_date)
                        .bind(params.is_current)
                        .bind(params.created_by)
                        .bind(params.updated_by)
                        .bind(params.created_at)
                        .bind(params.updated_at)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Price::from(price))
                })
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Price>> {
        let id = *id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let price = sqlx::query_as::<_, SqlxPrice>(queries::FIND_PRICE_BY_ID)
                        .bind(id)
                        .fetch_optional(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(price.map(Price::from))
                })
            })
            .await
    }

    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        let laptop_id = *laptop_id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let prices = sqlx::query_as::<_, SqlxPrice>(queries::FIND_PRICES_BY_LAPTOP_ID)
                        .bind(laptop_id)
                        .fetch_all(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(prices.into_iter().map(Price::from).collect())
                })
            })
            .await
    }

    async fn update(&self, id: &Uuid, price: NewPrice) -> Result<Price> {
        let id = *id;
        let params = CreatePriceParams::from(price);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let price = sqlx::query_as::<_, SqlxPrice>(queries::UPDATE_PRICE)
                        .bind(id)
                        .bind(params.laptop_id)
                        .bind(params.min_price)
                        .bind(params.max_price)
                        .bind(params.currency)
                        .bind(&params.source)
                        .bind(params.region)
                        .bind(params.effective_date)
                        .bind(params.is_current)
                        .bind(params.updated_by)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Price::from(price))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query(queries::DELETE_PRICE)
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Price not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    sqlx::query(queries::DELETE_PRICES_BY_LAPTOP_ID)
                        .bind(laptop_id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }

    async fn find_current_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        let laptop_id = *laptop_id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let prices = sqlx::query_as::<_, SqlxPrice>(
                        "SELECT * FROM prices WHERE laptop_id = $1 AND is_current = true ORDER BY effective_date DESC"
                    )
                    .bind(laptop_id)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(prices.into_iter().map(Price::from).collect())
                })
            })
            .await
    }

    async fn find_current_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Price>> {
        if laptop_ids.is_empty() {
            return Ok(vec![]);
        }
        self.base
            .execute_readonly(|pool| {
                let laptop_ids = laptop_ids.to_vec();
                Box::pin(async move {
                    let query = "SELECT * FROM prices WHERE laptop_id = ANY($1) AND is_current = true ORDER BY effective_date DESC".to_string();
                    let prices = sqlx::query_as::<_, SqlxPrice>(&query)
                        .bind(&laptop_ids)
                        .fetch_all(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;
                    Ok(prices.into_iter().map(Price::from).collect())
                })
            })
            .await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;
        let price_id = *price_id;
        self.base
            .execute_transaction(|tx| {
                Box::pin(async move {
                    // First, set all prices for this laptop to not current
                    sqlx::query("UPDATE prices SET is_current = false WHERE laptop_id = $1")
                        .bind(laptop_id)
                        .execute(&mut **tx)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    // Then set the specific price to current
                    let rows_affected = sqlx::query(
                        "UPDATE prices SET is_current = true WHERE id = $1 AND laptop_id = $2",
                    )
                    .bind(price_id)
                    .bind(laptop_id)
                    .execute(&mut **tx)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?
                    .rows_affected();

                    if rows_affected == 0 {
                        return Err(AppError::NotFound("Price not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }
}
