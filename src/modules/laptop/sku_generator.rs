use crate::errors::{AppError, Result};
use chrono::{Datelike, Utc};
use std::collections::HashMap;

/// SKU Generator for laptops
/// Format: {BRAND_CODE}-{MODEL_CODE}-{CATEGORY_CODE}-{TIMESTAMP}-{SEQUENCE}
/// Example: ASU-G15-GAM-240315-001
pub struct SkuGenerator;

impl SkuGenerator {
    /// Generate SKU for laptop
    pub async fn generate_sku<F, Fut>(
        brand: &str,
        model: &str,
        category_name: &str,
        check_uniqueness: F,
    ) -> Result<String>
    where
        F: Fn(&str) -> Fut,
        Fut: std::future::Future<Output = Result<bool>>,
    {
        let brand_code = Self::generate_brand_code(brand);
        let model_code = Self::generate_model_code(model);
        let category_code = Self::generate_category_code(category_name);
        let timestamp = Self::generate_timestamp();

        // Try with sequence 001-999
        for sequence in 1..=999 {
            let sequence_str = format!("{sequence:03}");
            let sku =
                format!("{brand_code}-{model_code}-{category_code}-{timestamp}-{sequence_str}");

            // Check if SKU is unique
            if !check_uniqueness(&sku).await? {
                return Ok(sku);
            }
        }

        // If all sequences are taken, add random suffix
        let base_sku = format!("{brand_code}-{model_code}-{category_code}-{timestamp}-001");
        Self::generate_with_random_suffix(&base_sku, check_uniqueness).await
    }

    /// Generate brand code (3 characters)
    fn generate_brand_code(brand: &str) -> String {
        let cleaned = Self::clean_string(brand);
        if cleaned.len() >= 3 {
            cleaned[..3].to_uppercase()
        } else {
            format!("{:0<3}", cleaned.to_uppercase()) // Pad with zeros if needed
        }
    }

    /// Generate model code (3-4 characters)
    fn generate_model_code(model: &str) -> String {
        let cleaned = Self::clean_string(model);

        // Extract meaningful parts (numbers and key letters)
        use crate::utils::regex_constants::ALPHANUMERIC_REGEX;
        let re = &*ALPHANUMERIC_REGEX;
        let parts: Vec<&str> = re.find_iter(&cleaned).map(|m| m.as_str()).collect();

        let mut code = String::new();
        for part in parts {
            // Prioritize parts with numbers or short meaningful words
            if part.chars().any(|c| c.is_numeric()) || part.len() <= 3 {
                code.push_str(part);
                if code.len() >= 3 {
                    break;
                }
            }
        }

        // If still not enough, take first 3 chars
        if code.len() < 3 {
            let fallback = cleaned.chars().take(3).collect::<String>();
            code = fallback;
        }

        // Limit to 4 characters max
        if code.len() > 4 {
            code = code[..4].to_string();
        }

        code.to_uppercase()
    }

    /// Generate category code (3 characters)
    fn generate_category_code(category: &str) -> String {
        let cleaned = Self::clean_string(category);

        // Common category mappings
        let category_map: HashMap<&str, &str> = [
            ("gaming", "GAM"),
            ("business", "BUS"),
            ("ultrabook", "ULT"),
            ("workstation", "WRK"),
            ("budget", "BDG"),
            ("premium", "PRM"),
            ("student", "STU"),
            ("creator", "CRT"),
        ]
        .iter()
        .cloned()
        .collect();

        // Check for known categories
        let lower_category = cleaned.to_lowercase();
        for (key, code) in &category_map {
            if lower_category.contains(key) {
                return code.to_string();
            }
        }

        // Fallback: take first 3 characters
        if cleaned.len() >= 3 {
            cleaned[..3].to_uppercase()
        } else {
            format!("{:0<3}", cleaned.to_uppercase())
        }
    }

    /// Generate timestamp (YYMMDD format)
    fn generate_timestamp() -> String {
        let now = Utc::now();
        format!("{:02}{:02}{:02}", now.year() % 100, now.month(), now.day())
    }

    /// Generate SKU with random suffix if base is taken
    async fn generate_with_random_suffix<F, Fut>(
        base_sku: &str,
        check_uniqueness: F,
    ) -> Result<String>
    where
        F: Fn(&str) -> Fut,
        Fut: std::future::Future<Output = Result<bool>>,
    {
        for suffix_char in b'A'..=b'Z' {
            let suffix = suffix_char as char;
            let sku_with_suffix = format!("{base_sku}{suffix}");

            if !check_uniqueness(&sku_with_suffix).await? {
                return Ok(sku_with_suffix);
            }
        }

        Err(AppError::BadRequest(
            "Unable to generate unique SKU after multiple attempts".into(),
        ))
    }

    /// Clean string by removing special characters and spaces
    fn clean_string(input: &str) -> String {
        input.chars().filter(|c| c.is_alphanumeric()).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_brand_code_generation() {
        assert_eq!(SkuGenerator::generate_brand_code("ASUS"), "ASU");
        assert_eq!(SkuGenerator::generate_brand_code("Lenovo"), "LEN");
        assert_eq!(SkuGenerator::generate_brand_code("HP"), "HP0");
        assert_eq!(SkuGenerator::generate_brand_code("Dell"), "DEL");
    }

    #[test]
    fn test_model_code_generation() {
        assert_eq!(SkuGenerator::generate_model_code("ROG Strix G15"), "ROGS");
        assert_eq!(SkuGenerator::generate_model_code("Legion Pro 7i"), "LEGI");
        assert_eq!(SkuGenerator::generate_model_code("Pavilion Gaming"), "PAV");
    }

    #[test]
    fn test_category_code_generation() {
        assert_eq!(
            SkuGenerator::generate_category_code("Gaming Laptops"),
            "GAM"
        );
        assert_eq!(
            SkuGenerator::generate_category_code("Business Laptops"),
            "BUS"
        );
        assert_eq!(SkuGenerator::generate_category_code("Ultrabooks"), "ULT");
        assert_eq!(
            SkuGenerator::generate_category_code("Unknown Category"),
            "UNK"
        );
    }

    #[test]
    fn test_timestamp_generation() {
        let timestamp = SkuGenerator::generate_timestamp();
        assert_eq!(timestamp.len(), 6);
        assert!(timestamp.chars().all(|c| c.is_numeric()));
    }
}
