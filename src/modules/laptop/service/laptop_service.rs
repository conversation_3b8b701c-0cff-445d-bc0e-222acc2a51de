use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::{
        category::service_trait::CategoryServiceTrait,
        laptop::{
            models::{
                CreateLaptopRequest, LaptopFullView, LaptopPaginationRequest, LaptopPublicView,
                LaptopStatus, MarketRegion, NewLaptop, PaginatedLaptopsDetailed,
                PaginatedLaptopsFull, PaginatedLaptopsPublic, UpdateLaptopRequest,
            },
            repository_trait::DynLaptopRepo,
            service_trait::LaptopServiceTrait,
            sqlx_repository::SqlxLaptopRepository,
        },
        redis::RedisServiceTrait,
    },
    utils::{
        Error<PERSON>elper,
        cache_helpers::{CacheKeyGenerator, CacheOperations, CacheTTL},
        validation::helpers::validate_enhanced_and_execute,
    },
};
use async_trait::async_trait;
use std::{sync::Arc, time::Duration};
use uuid::Uuid;

#[derive(Clone)]
pub struct LaptopService {
    repository: DynLaptopRepo,
    category_service: Arc<dyn CategoryServiceTrait>,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
}

impl LaptopService {
    pub fn new(
        database: Database,
        category_service: Arc<dyn CategoryServiceTrait>,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        Self {
            repository: Arc::new(SqlxLaptopRepository::new(database)),
            category_service,
            redis_service,
        }
    }

    /// Generate cache key for laptop details
    fn laptop_cache_key(laptop_id: &Uuid) -> String {
        CacheKeyGenerator::entity_details("laptop", laptop_id)
    }

    /// Generate cache key for laptop view count
    fn laptop_view_count_cache_key(laptop_id: &Uuid) -> String {
        format!("laptop:view_count:{laptop_id}")
    }

    /// Generate cache key for popular laptops
    fn popular_laptops_cache_key() -> String {
        CacheKeyGenerator::popular_items("laptops")
    }

    /// Cache TTL for laptop details (1 hour)
    fn laptop_details_ttl() -> Duration {
        CacheTTL::entity_details()
    }

    /// Invalidate laptop cache when laptop is updated
    async fn invalidate_laptop_cache(&self, laptop_id: &Uuid) -> Result<()> {
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::laptop_cache_key(laptop_id);
            let popular_cache_key = Self::popular_laptops_cache_key();

            // Delete laptop details cache
            if let Err(e) = redis.delete(&cache_key).await {
                CacheOperations::log_cache_invalidation_failure(
                    "Laptop",
                    &laptop_id.to_string(),
                    &e.to_string(),
                );
            }

            // Delete popular laptops cache
            if let Err(e) = redis.delete(&popular_cache_key).await {
                CacheOperations::log_cache_invalidation_failure(
                    "Popular Laptops",
                    "all",
                    &e.to_string(),
                );
            }
        }
        Ok(())
    }
}

#[async_trait]
impl LaptopServiceTrait for LaptopService {
    async fn create_laptop(
        &self,
        request: CreateLaptopRequest,
        created_by: &Uuid,
    ) -> Result<LaptopFullView> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate category exists
            self.category_service
                .validate_category_exists(&req.category_id)
                .await?;

            // Validate slug uniqueness
            self.validate_laptop_slug(&req.slug, None).await?;

            // Validate SKU uniqueness if provided
            if let Some(ref sku) = req.sku {
                self.validate_laptop_sku(sku, None).await?;
            }

            let new_laptop = NewLaptop {
                id: Uuid::new_v4(),
                category_id: req.category_id,
                brand: req.brand,
                model: req.model,
                full_name: req.full_name,
                slug: req.slug,
                sku: req.sku,
                market_region: req.market_region.unwrap_or(MarketRegion::Global),
                release_date: req.release_date,
                description: req.description,
                image_urls: req
                    .image_urls
                    .map(|urls| urls.into_iter().map(Some).collect()),
                status: LaptopStatus::Draft,
                is_featured: req.is_featured.unwrap_or(false),
                created_by: *created_by,
            };

            self.repository.create(new_laptop).await
        })
        .await
    }

    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView> {
        // Try to get from cache first
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::laptop_cache_key(id);
            if let Ok(Some(cached_json)) = redis.get(&cache_key).await {
                if let Ok(laptop) = serde_json::from_str::<LaptopFullView>(&cached_json) {
                    CacheOperations::log_cache_hit("Laptop", &id.to_string());
                    return Ok(laptop);
                }
            }
            CacheOperations::log_cache_miss("Laptop", &id.to_string());
        }

        // Get from database
        let laptop =
            self.repository.find_by_id(id).await?.ok_or_else(|| {
                ErrorHelper::not_found_with_option("Laptop", Some(&id.to_string()))
            })?;

        // Cache the result
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::laptop_cache_key(id);
            let ttl = Self::laptop_details_ttl();
            if let Ok(json) = serde_json::to_string(&laptop) {
                if let Err(e) = redis.set(&cache_key, &json, Some(ttl)).await {
                    CacheOperations::log_cache_set_failure(
                        "Laptop",
                        &id.to_string(),
                        &e.to_string(),
                    );
                } else {
                    CacheOperations::log_cache_set("Laptop", &id.to_string(), ttl.as_secs());
                }
            }
        }

        Ok(laptop)
    }

    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView> {
        self.repository
            .find_by_slug(slug)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_slug("Laptop", slug))
    }

    async fn update_laptop(
        &self,
        id: &Uuid,
        request: UpdateLaptopRequest,
        _updated_by: &Uuid,
    ) -> Result<LaptopFullView> {
        let result = validate_enhanced_and_execute(request, |req| async move {
            // Check if laptop exists
            let existing = self.get_laptop_by_id(id).await?;

            // Validate optional fields using let chains for cleaner code
            if let Some(category_id) = req.category_id {
                self.category_service
                    .validate_category_exists(&category_id)
                    .await?;
            }

            if let Some(ref slug) = req.slug {
                self.validate_laptop_slug(slug, Some(id)).await?;
            }

            if let Some(ref sku) = req.sku {
                self.validate_laptop_sku(sku, Some(id)).await?;
            }

            let updated_laptop = NewLaptop {
                id: existing.id,
                category_id: req.category_id.unwrap_or(existing.category_id),
                brand: req.brand.unwrap_or(existing.brand),
                model: req.model.unwrap_or(existing.model),
                full_name: req.full_name.unwrap_or(existing.full_name),
                slug: req.slug.unwrap_or(existing.slug),
                sku: req.sku.or(existing.sku),
                market_region: req.market_region.unwrap_or(existing.market_region),
                release_date: req.release_date.or(existing.release_date),
                description: req.description.or(existing.description),
                image_urls: req
                    .image_urls
                    .map(|urls| urls.into_iter().map(Some).collect())
                    .or_else(|| Some(existing.image_urls.into_iter().map(Some).collect())),
                status: existing.status,
                is_featured: req.is_featured.unwrap_or(existing.is_featured),
                created_by: existing.created_by,
            };

            self.repository.update(id, updated_laptop).await
        })
        .await?;

        // Invalidate cache after successful update
        self.invalidate_laptop_cache(id).await?;

        Ok(result)
    }

    async fn delete_laptop(&self, id: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;

        self.repository.delete(id).await
    }

    async fn get_public_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic> {
        self.repository.get_public_with_pagination(request).await
    }

    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView> {
        let laptop = self
            .repository
            .find_by_slug(slug)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_slug("Laptop", slug))?;

        // Only return if laptop is published
        if laptop.status != LaptopStatus::Published {
            return Err(ErrorHelper::not_found_by_slug("Laptop", slug));
        }

        Ok(LaptopPublicView {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        })
    }

    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()> {
        // Increment view count in Redis first (for performance)
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::laptop_view_count_cache_key(id);
            match redis.increment(&cache_key, 1).await {
                Ok(new_count) => {
                    tracing::debug!(
                        laptop_id = %id,
                        new_count = new_count,
                        "Incremented view count in Redis"
                    );

                    // Sync to database every 10 views or after 1 hour
                    if new_count % 10 == 0 {
                        if let Err(e) = self.repository.increment_view_count(id).await {
                            tracing::warn!(
                                laptop_id = %id,
                                error = %e,
                                "Failed to sync view count to database"
                            );
                        }
                    }
                    return Ok(());
                }
                Err(e) => {
                    tracing::warn!(
                        laptop_id = %id,
                        error = %e,
                        "Failed to increment view count in Redis, falling back to database"
                    );
                }
            }
        }

        // Fallback to database if Redis is not available
        self.repository.increment_view_count(id).await
    }

    async fn get_detailed_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed> {
        self.repository.get_detailed_with_pagination(request).await
    }

    async fn get_full_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull> {
        self.repository.get_full_with_pagination(request).await
    }

    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;

        self.repository
            .update_status(id, LaptopStatus::Published, updated_by)
            .await
    }

    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;

        self.repository
            .update_status(id, LaptopStatus::Archived, updated_by)
            .await
    }

    async fn set_laptop_featured(
        &self,
        id: &Uuid,
        is_featured: bool,
        updated_by: &Uuid,
    ) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;

        self.repository
            .update_featured(id, is_featured, updated_by)
            .await
    }

    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_slug(slug, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Laptop with slug '{slug}' already exists"
            )));
        }
        Ok(())
    }

    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_sku(sku, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Laptop with SKU '{sku}' already exists"
            )));
        }
        Ok(())
    }
}
