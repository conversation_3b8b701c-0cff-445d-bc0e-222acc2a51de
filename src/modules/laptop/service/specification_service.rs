use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::laptop::{
        models::{
            CreateSpecificationRequest, NewSpecification, Specification, UpdateSpecificationRequest,
        },
        service_trait::{LaptopServiceTrait, SpecificationServiceTrait},
        sqlx_repository::{DynSpecificationRepo, SqlxSpecificationRepository},
    },
    utils::{ErrorHelper, validation::helpers::validate_enhanced_and_execute},
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct SpecificationService {
    repository: DynSpecificationRepo,
    laptop_service: Arc<dyn LaptopServiceTrait>,
}

impl SpecificationService {
    pub fn new(database: Database, laptop_service: Arc<dyn LaptopServiceTrait>) -> Self {
        Self {
            repository: Arc::new(SqlxSpecificationRepository::new(database)),
            laptop_service,
        }
    }
}

#[async_trait]
impl SpecificationServiceTrait for SpecificationService {
    async fn create_specification(
        &self,
        request: CreateSpecificationRequest,
    ) -> Result<Specification> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate laptop exists
            self.laptop_service.get_laptop_by_id(&req.laptop_id).await?;

            // Check if specification already exists for this laptop
            if self
                .repository
                .find_by_laptop_id(&req.laptop_id)
                .await?
                .is_some()
            {
                return Err(AppError::Conflict(format!(
                    "Specification already exists for laptop {}",
                    req.laptop_id
                )));
            }

            let new_spec = NewSpecification {
                id: Uuid::new_v4(),
                laptop_id: req.laptop_id,
                cpu_brand: req.cpu_brand,
                cpu_model: req.cpu_model,
                ram_size: req.ram_size,
                ram_type: req.ram_type,
                ram_slots_total: req.ram_slots_total,
                ram_slots_available: req.ram_slots_available,
                ram_max_capacity: req.ram_max_capacity,
                ram_soldered: req.ram_soldered.unwrap_or(false),
                storage_type: match req.storage_type {
                    crate::modules::laptop::models::StorageType::SSD => "SSD".to_string(),
                    crate::modules::laptop::models::StorageType::HDD => "HDD".to_string(),
                    crate::modules::laptop::models::StorageType::Hybrid => "Hybrid".to_string(),
                },
                storage_capacity: req.storage_capacity,
                storage_slots_total: req.storage_slots_total,
                storage_slots_available: req.storage_slots_available,
                storage_max_capacity: req.storage_max_capacity,
                gpu_type: match req.gpu_type {
                    crate::modules::laptop::models::GpuType::Integrated => "Integrated".to_string(),
                    crate::modules::laptop::models::GpuType::Dedicated => "Dedicated".to_string(),
                },
                gpu_model: req.gpu_model,
                screen_size: req.screen_size,
                screen_resolution: req.screen_resolution,
                refresh_rate: req.refresh_rate,
                weight: req.weight,
                operating_system: req.operating_system,
            };

            self.repository.create(new_spec).await
        })
        .await
    }

    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification> {
        self.repository.find_by_id(id).await?.ok_or_else(|| {
            ErrorHelper::not_found_with_option("Specification", Some(&id.to_string()))
        })
    }

    async fn get_specification_by_laptop_id(
        &self,
        laptop_id: &Uuid,
    ) -> Result<Option<Specification>> {
        self.repository.find_by_laptop_id(laptop_id).await
    }

    async fn update_specification(
        &self,
        id: &Uuid,
        request: UpdateSpecificationRequest,
    ) -> Result<Specification> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if specification exists
            let existing = self.get_specification_by_id(id).await?;

            let updated_spec = NewSpecification {
                id: existing.id,
                laptop_id: existing.laptop_id,
                cpu_brand: req.cpu_brand.unwrap_or(existing.cpu_brand),
                cpu_model: req.cpu_model.unwrap_or(existing.cpu_model),
                ram_size: req.ram_size.unwrap_or(existing.ram_size),
                ram_type: req.ram_type.or(existing.ram_type),
                ram_slots_total: req.ram_slots_total.or(existing.ram_slots_total),
                ram_slots_available: req.ram_slots_available.or(existing.ram_slots_available),
                ram_max_capacity: req.ram_max_capacity.or(existing.ram_max_capacity),
                ram_soldered: req.ram_soldered.unwrap_or(existing.ram_soldered),
                storage_type: req
                    .storage_type
                    .map(|t| match t {
                        crate::modules::laptop::models::StorageType::SSD => "SSD".to_string(),
                        crate::modules::laptop::models::StorageType::HDD => "HDD".to_string(),
                        crate::modules::laptop::models::StorageType::Hybrid => "Hybrid".to_string(),
                    })
                    .unwrap_or(existing.storage_type),
                storage_capacity: req.storage_capacity.unwrap_or(existing.storage_capacity),
                storage_slots_total: req.storage_slots_total.or(existing.storage_slots_total),
                storage_slots_available: req
                    .storage_slots_available
                    .or(existing.storage_slots_available),
                storage_max_capacity: req.storage_max_capacity.or(existing.storage_max_capacity),
                gpu_type: req
                    .gpu_type
                    .map(|t| match t {
                        crate::modules::laptop::models::GpuType::Integrated => {
                            "Integrated".to_string()
                        }
                        crate::modules::laptop::models::GpuType::Dedicated => {
                            "Dedicated".to_string()
                        }
                    })
                    .unwrap_or(existing.gpu_type),
                gpu_model: req.gpu_model.or(existing.gpu_model),
                screen_size: req.screen_size.unwrap_or(existing.screen_size),
                screen_resolution: req.screen_resolution.unwrap_or(existing.screen_resolution),
                refresh_rate: req.refresh_rate.unwrap_or(existing.refresh_rate),
                weight: req.weight.or(existing.weight),
                operating_system: req.operating_system.or(existing.operating_system),
            };

            self.repository.update(id, updated_spec).await
        })
        .await
    }

    async fn delete_specification(&self, id: &Uuid) -> Result<()> {
        // Check if specification exists
        self.get_specification_by_id(id).await?;

        self.repository.delete(id).await
    }

    async fn delete_specification_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        self.repository.delete_by_laptop_id(laptop_id).await
    }

    async fn get_specifications_by_laptop_ids(
        &self,
        laptop_ids: &[Uuid],
    ) -> Result<Vec<Specification>> {
        self.repository.find_by_laptop_ids(laptop_ids).await
    }
}
