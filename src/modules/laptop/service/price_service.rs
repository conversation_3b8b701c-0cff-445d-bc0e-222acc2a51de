use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::laptop::{
        models::{CreatePriceRequest, Currency, NewPrice, Price, PriceRegion, UpdatePriceRequest},
        service_trait::{LaptopServiceTrait, PriceServiceTrait},
        sqlx_repository::{DynPriceRepo, SqlxPriceRepository},
    },
    utils::{ErrorHelper, validation::helpers::validate_enhanced_and_execute},
};
use async_trait::async_trait;
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct PriceService {
    repository: DynPriceRepo,
    laptop_service: Arc<dyn LaptopServiceTrait>,
}

impl PriceService {
    pub fn new(database: Database, laptop_service: Arc<dyn LaptopServiceTrait>) -> Self {
        Self {
            repository: Arc::new(SqlxPriceRepository::new(database)),
            laptop_service,
        }
    }
}

#[async_trait]
impl PriceServiceTrait for PriceService {
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate laptop exists
            self.laptop_service.get_laptop_by_id(&req.laptop_id).await?;

            // Validate price range
            if req.max_price < req.min_price {
                return Err(AppError::Validation(
                    "Max price must be greater than or equal to min price".to_string(),
                ));
            }

            let new_price = NewPrice {
                id: Uuid::new_v4(),
                laptop_id: req.laptop_id,
                min_price: req.min_price,
                max_price: req.max_price,
                currency: req.currency.unwrap_or(Currency::USD),
                source: req.source,
                region: req.region.unwrap_or(PriceRegion::Global),
                effective_date: req
                    .effective_date
                    .unwrap_or_else(|| Utc::now().date_naive()),
                is_current: req.is_current.unwrap_or(true),
                created_by: *created_by,
            };

            self.repository.create(new_price).await
        })
        .await
    }

    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price> {
        self.repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_with_option("Price", Some(&id.to_string())))
    }

    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.repository.find_by_laptop_id(laptop_id).await
    }

    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.repository.find_current_by_laptop_id(laptop_id).await
    }

    async fn get_current_prices_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Price>> {
        self.repository.find_current_by_laptop_ids(laptop_ids).await
    }

    async fn update_price(
        &self,
        id: &Uuid,
        request: UpdatePriceRequest,
        _updated_by: &Uuid,
    ) -> Result<Price> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if price exists
            let existing = self.get_price_by_id(id).await?;

            // Validate price range if both are provided
            let min_price = req.min_price.unwrap_or(existing.min_price);
            let max_price = req.max_price.unwrap_or(existing.max_price);

            if max_price < min_price {
                return Err(AppError::Validation(
                    "Max price must be greater than or equal to min price".to_string(),
                ));
            }

            let updated_price = NewPrice {
                id: existing.id,
                laptop_id: existing.laptop_id,
                min_price,
                max_price,
                currency: req.currency.unwrap_or(existing.currency),
                source: req.source.or(existing.source),
                region: req.region.unwrap_or(existing.region),
                effective_date: req.effective_date.unwrap_or(existing.effective_date),
                is_current: req.is_current.unwrap_or(existing.is_current),
                created_by: existing.created_by,
            };

            self.repository.update(id, updated_price).await
        })
        .await
    }

    async fn delete_price(&self, id: &Uuid) -> Result<()> {
        // Check if price exists
        self.get_price_by_id(id).await?;

        self.repository.delete(id).await
    }

    async fn delete_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        self.repository.delete_by_laptop_id(laptop_id).await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        // Validate laptop exists
        self.laptop_service.get_laptop_by_id(laptop_id).await?;

        // Validate price exists and belongs to the laptop
        let price = self.get_price_by_id(price_id).await?;
        if price.laptop_id != *laptop_id {
            return Err(AppError::BadRequest(
                "Price does not belong to the specified laptop".to_string(),
            ));
        }

        self.repository.set_current_price(laptop_id, price_id).await
    }
}
