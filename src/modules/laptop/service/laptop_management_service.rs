use crate::{
    database::Database,
    errors::Result,
    modules::{
        category::service_trait::CategoryServiceTrait,
        laptop::{
            models::{
                CompleteLaptopView, CreateCompleteLaptopRequest, CreateLaptopRequest,
                CreatePriceRequest, CreateSpecificationRequest, LaptopFullDetailView,
                LaptopFullView, LaptopPaginationRequest, LaptopPublicDetailView, LaptopPublicView,
                LaptopStatus, PaginatedLaptopsDetailed, PaginatedLaptopsFull,
                PaginatedLaptopsFullDetail, PaginatedLaptopsPublic, PaginatedLaptopsPublicDetail,
                Price, Specification, UpdateLaptopRequest, UpdatePriceRequest,
                UpdateSpecificationRequest,
            },
            service_trait::{
                LaptopManagementServiceTrait, LaptopServiceTrait, PriceServiceTrait,
                SpecificationServiceTrait,
            },
        },
        redis::RedisServiceTrait,
    },
    utils::{Error<PERSON><PERSON><PERSON>, validation::helpers::validate_enhanced_and_execute},
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

use super::{LaptopService, PriceService, SpecificationService};

#[derive(Clone)]
pub struct LaptopManagementService {
    laptop_service: Arc<dyn LaptopServiceTrait>,
    specification_service: Arc<dyn SpecificationServiceTrait>,
    price_service: Arc<dyn PriceServiceTrait>,
}

impl LaptopManagementService {
    pub fn new(
        database: Database,
        category_service: Arc<dyn CategoryServiceTrait>,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        let laptop_service = Arc::new(LaptopService::new(
            database.clone(),
            category_service,
            redis_service.clone(),
        ));
        let specification_service = Arc::new(SpecificationService::new(
            database.clone(),
            laptop_service.clone(),
        ));
        let price_service = Arc::new(PriceService::new(database, laptop_service.clone()));

        Self {
            laptop_service,
            specification_service,
            price_service,
        }
    }
}

#[async_trait]
impl LaptopManagementServiceTrait for LaptopManagementService {
    // Laptop operations - delegate to laptop service
    async fn create_laptop(
        &self,
        request: CreateLaptopRequest,
        created_by: &Uuid,
    ) -> Result<LaptopFullView> {
        self.laptop_service.create_laptop(request, created_by).await
    }

    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView> {
        self.laptop_service.get_laptop_by_id(id).await
    }

    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView> {
        self.laptop_service.get_laptop_by_slug(slug).await
    }

    async fn update_laptop(
        &self,
        id: &Uuid,
        request: UpdateLaptopRequest,
        updated_by: &Uuid,
    ) -> Result<LaptopFullView> {
        self.laptop_service
            .update_laptop(id, request, updated_by)
            .await
    }

    async fn delete_laptop(&self, id: &Uuid) -> Result<()> {
        // Delete related data first
        self.specification_service
            .delete_specification_by_laptop_id(id)
            .await?;
        self.price_service.delete_prices_by_laptop_id(id).await?;

        // Then delete the laptop
        self.laptop_service.delete_laptop(id).await
    }

    // Public API operations
    async fn get_public_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic> {
        self.laptop_service.get_public_laptops(request).await
    }

    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView> {
        self.laptop_service.get_public_laptop_by_slug(slug).await
    }

    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()> {
        self.laptop_service.increment_laptop_view_count(id).await
    }

    // Private API operations
    async fn get_detailed_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed> {
        self.laptop_service.get_detailed_laptops(request).await
    }

    async fn get_full_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull> {
        self.laptop_service.get_full_laptops(request).await
    }

    // Status management
    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        self.laptop_service.publish_laptop(id, updated_by).await
    }

    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        self.laptop_service.archive_laptop(id, updated_by).await
    }

    async fn set_laptop_featured(
        &self,
        id: &Uuid,
        is_featured: bool,
        updated_by: &Uuid,
    ) -> Result<()> {
        self.laptop_service
            .set_laptop_featured(id, is_featured, updated_by)
            .await
    }

    // Specification operations - delegate to specification service
    async fn create_specification(
        &self,
        request: CreateSpecificationRequest,
    ) -> Result<Specification> {
        self.specification_service
            .create_specification(request)
            .await
    }

    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification> {
        self.specification_service.get_specification_by_id(id).await
    }

    async fn get_specification_by_laptop_id(
        &self,
        laptop_id: &Uuid,
    ) -> Result<Option<Specification>> {
        self.specification_service
            .get_specification_by_laptop_id(laptop_id)
            .await
    }

    async fn update_specification(
        &self,
        id: &Uuid,
        request: UpdateSpecificationRequest,
    ) -> Result<Specification> {
        self.specification_service
            .update_specification(id, request)
            .await
    }

    async fn delete_specification(&self, id: &Uuid) -> Result<()> {
        self.specification_service.delete_specification(id).await
    }

    // Price operations - delegate to price service
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price> {
        self.price_service.create_price(request, created_by).await
    }

    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price> {
        self.price_service.get_price_by_id(id).await
    }

    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.price_service.get_prices_by_laptop_id(laptop_id).await
    }

    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.price_service
            .get_current_prices_by_laptop_id(laptop_id)
            .await
    }

    async fn update_price(
        &self,
        id: &Uuid,
        request: UpdatePriceRequest,
        updated_by: &Uuid,
    ) -> Result<Price> {
        self.price_service
            .update_price(id, request, updated_by)
            .await
    }

    async fn delete_price(&self, id: &Uuid) -> Result<()> {
        self.price_service.delete_price(id).await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        self.price_service
            .set_current_price(laptop_id, price_id)
            .await
    }

    // Validation operations
    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        self.laptop_service
            .validate_laptop_slug(slug, exclude_id)
            .await
    }

    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        self.laptop_service
            .validate_laptop_sku(sku, exclude_id)
            .await
    }

    async fn create_complete_laptop(
        &self,
        request: CreateCompleteLaptopRequest,
        created_by: &Uuid,
    ) -> Result<CompleteLaptopView> {
        validate_enhanced_and_execute(request, |req| async move {
            // Use transaction wrapper to ensure atomicity
            // Note: In a production system, use proper database-level transactions
            // This approach ensures if any step fails, cleanup is handled properly

            // Create the basic laptop first
            // Get category name for SKU generation
            // For now, use a simple category name based on common patterns
            // TODO: Implement proper category lookup
            let category_name = "Gaming"; // Default category name for SKU generation

            // Generate SKU automatically
            let generated_sku = crate::modules::laptop::sku_generator::SkuGenerator::generate_sku(
                &req.brand,
                &req.model,
                category_name,
                |_sku: &str| async move {
                    // For now, assume SKU doesn't exist (proper check to be implemented)
                    // TODO: Implement proper SKU uniqueness check
                    Ok(false)
                },
            )
            .await?;

            let laptop_request = CreateLaptopRequest {
                brand: req.brand,
                model: req.model,
                full_name: req.full_name,
                slug: req.slug,
                category_id: req.category_id,
                sku: Some(generated_sku),
                market_region: req.market_region,
                release_date: req.release_date,
                description: req.description,
                image_urls: req.image_urls,
                is_featured: req.is_featured,
            };

            let laptop = match self
                .laptop_service
                .create_laptop(laptop_request, created_by)
                .await
            {
                Ok(laptop) => laptop,
                Err(e) => return Err(e),
            };

            // Create specification if provided (with rollback on failure)
            let specification = if let Some(spec_req) = req.specification {
                let spec_request = CreateSpecificationRequest {
                    laptop_id: laptop.id,
                    cpu_brand: spec_req.cpu_brand,
                    cpu_model: spec_req.cpu_model,
                    ram_size: spec_req.ram_size,
                    ram_type: spec_req.ram_type,
                    ram_slots_total: spec_req.ram_slots_total,
                    ram_slots_available: spec_req.ram_slots_available,
                    ram_max_capacity: spec_req.ram_max_capacity,
                    ram_soldered: spec_req.ram_soldered,
                    storage_type: spec_req.storage_type,
                    storage_capacity: spec_req.storage_capacity,
                    storage_slots_total: spec_req.storage_slots_total,
                    storage_slots_available: spec_req.storage_slots_available,
                    storage_max_capacity: spec_req.storage_max_capacity,
                    gpu_type: spec_req.gpu_type,
                    gpu_model: spec_req.gpu_model,
                    screen_size: spec_req.screen_size,
                    screen_resolution: spec_req.screen_resolution,
                    refresh_rate: spec_req.refresh_rate,
                    weight: spec_req.weight,
                    operating_system: spec_req.operating_system,
                };

                match self
                    .specification_service
                    .create_specification(spec_request)
                    .await
                {
                    Ok(spec) => Some(spec),
                    Err(e) => {
                        // Rollback: delete the laptop that was created
                        let _ = self.laptop_service.delete_laptop(&laptop.id).await;
                        return Err(e);
                    }
                }
            } else {
                None
            };

            // Create price if provided (with rollback on failure)
            let price = if let Some(price_req) = req.price {
                let price_request = CreatePriceRequest {
                    laptop_id: laptop.id,
                    min_price: price_req.min_price,
                    max_price: price_req.max_price,
                    currency: price_req.currency,
                    source: price_req.source,
                    region: price_req.region,
                    effective_date: price_req.effective_date,
                    is_current: price_req.is_current,
                };

                match self
                    .price_service
                    .create_price(price_request, created_by)
                    .await
                {
                    Ok(price) => Some(price),
                    Err(e) => {
                        // Rollback: delete spec and laptop
                        if specification.is_some() {
                            let _ = self
                                .specification_service
                                .delete_specification_by_laptop_id(&laptop.id)
                                .await;
                        }
                        let _ = self.laptop_service.delete_laptop(&laptop.id).await;
                        return Err(e);
                    }
                }
            } else {
                None
            };

            Ok(CompleteLaptopView {
                laptop,
                specification,
                price,
            })
        })
        .await
    }

    async fn get_public_laptop_by_id_with_details(
        &self,
        id: &Uuid,
    ) -> Result<LaptopPublicDetailView> {
        // Execute laptop, spec, and price queries concurrently
        let (laptop_result, spec_result, price_result) = tokio::join!(
            self.laptop_service.get_laptop_by_id(id),
            self.specification_service
                .get_specification_by_laptop_id(id),
            self.price_service.get_current_prices_by_laptop_id(id)
        );

        let laptop = laptop_result?;

        // Only return if laptop is published
        if laptop.status != LaptopStatus::Published {
            return Err(ErrorHelper::not_found_with_option(
                "Laptop",
                Some(&id.to_string()),
            ));
        }

        let specification = spec_result.ok().flatten();
        let current_price = price_result
            .ok()
            .and_then(|prices| prices.into_iter().next());

        Ok(LaptopPublicDetailView {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
            specification,
            current_price,
        })
    }

    async fn get_public_laptop_by_slug_with_details(
        &self,
        slug: &str,
    ) -> Result<LaptopPublicDetailView> {
        let laptop = self.laptop_service.get_laptop_by_slug(slug).await?;

        // Only return if laptop is published
        if laptop.status != LaptopStatus::Published {
            return Err(ErrorHelper::not_found_by_slug("Laptop", slug));
        }

        let specification = self
            .specification_service
            .get_specification_by_laptop_id(&laptop.id)
            .await?;
        let current_price = self
            .price_service
            .get_current_prices_by_laptop_id(&laptop.id)
            .await?
            .into_iter()
            .next();

        Ok(LaptopPublicDetailView {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
            specification,
            current_price,
        })
    }

    async fn get_laptop_by_id_with_details(&self, id: &Uuid) -> Result<LaptopFullDetailView> {
        // Execute all queries concurrently for better performance
        let (laptop_result, spec_result, price_result) = tokio::join!(
            self.laptop_service.get_laptop_by_id(id),
            self.specification_service
                .get_specification_by_laptop_id(id),
            self.price_service.get_current_prices_by_laptop_id(id)
        );

        let laptop = laptop_result?;
        let specification = spec_result.ok().flatten();
        let current_price = price_result
            .ok()
            .and_then(|prices| prices.into_iter().next());

        Ok(LaptopFullDetailView {
            id: laptop.id,
            category_id: laptop.category_id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            sku: laptop.sku,
            market_region: laptop.market_region,
            release_date: laptop.release_date,
            description: laptop.description,
            image_urls: laptop.image_urls,
            status: laptop.status,
            is_featured: laptop.is_featured,
            view_count: laptop.view_count,
            created_by: laptop.created_by,
            updated_by: laptop.updated_by,
            created_at: laptop.created_at,
            updated_at: laptop.updated_at,
            specification,
            current_price,
        })
    }

    async fn get_laptop_by_slug_with_details(&self, slug: &str) -> Result<LaptopFullDetailView> {
        let laptop = self.laptop_service.get_laptop_by_slug(slug).await?;
        let specification = self
            .specification_service
            .get_specification_by_laptop_id(&laptop.id)
            .await?;
        let current_price = self
            .price_service
            .get_current_prices_by_laptop_id(&laptop.id)
            .await?
            .into_iter()
            .next();

        Ok(LaptopFullDetailView {
            id: laptop.id,
            category_id: laptop.category_id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            sku: laptop.sku,
            market_region: laptop.market_region,
            release_date: laptop.release_date,
            description: laptop.description,
            image_urls: laptop.image_urls,
            status: laptop.status,
            is_featured: laptop.is_featured,
            view_count: laptop.view_count,
            created_by: laptop.created_by,
            updated_by: laptop.updated_by,
            created_at: laptop.created_at,
            updated_at: laptop.updated_at,
            specification,
            current_price,
        })
    }

    async fn get_public_laptops_with_details(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublicDetail> {
        let paginated_laptops = self.laptop_service.get_public_laptops(request).await?;
        let laptop_ids: Vec<Uuid> = paginated_laptops.laptops.iter().map(|l| l.id).collect();
        let specifications = self
            .specification_service
            .get_specifications_by_laptop_ids(&laptop_ids)
            .await?;
        let prices = self
            .price_service
            .get_current_prices_by_laptop_ids(&laptop_ids)
            .await?;
        use std::collections::HashMap;
        let spec_map: HashMap<Uuid, Specification> = specifications
            .into_iter()
            .map(|s| (s.laptop_id, s))
            .collect();
        let mut price_map: HashMap<Uuid, Price> = HashMap::new();
        for price in prices {
            price_map.entry(price.laptop_id).or_insert(price);
        }
        let enhanced_laptops = paginated_laptops
            .laptops
            .into_iter()
            .map(|laptop| LaptopPublicDetailView {
                id: laptop.id,
                brand: laptop.brand,
                model: laptop.model,
                full_name: laptop.full_name,
                slug: laptop.slug,
                market_region: laptop.market_region,
                image_urls: laptop.image_urls,
                status: laptop.status,
                view_count: laptop.view_count,
                specification: spec_map.get(&laptop.id).cloned(),
                current_price: price_map.get(&laptop.id).cloned(),
            })
            .collect();
        Ok(PaginatedLaptopsPublicDetail {
            laptops: enhanced_laptops,
            meta: paginated_laptops.meta,
        })
    }

    async fn get_full_laptops_with_details(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFullDetail> {
        let paginated_laptops = self.laptop_service.get_full_laptops(request).await?;
        let laptop_ids: Vec<Uuid> = paginated_laptops.laptops.iter().map(|l| l.id).collect();
        let specifications = self
            .specification_service
            .get_specifications_by_laptop_ids(&laptop_ids)
            .await?;
        let prices = self
            .price_service
            .get_current_prices_by_laptop_ids(&laptop_ids)
            .await?;
        use std::collections::HashMap;
        let spec_map: HashMap<Uuid, Specification> = specifications
            .into_iter()
            .map(|s| (s.laptop_id, s))
            .collect();
        let mut price_map: HashMap<Uuid, Price> = HashMap::new();
        for price in prices {
            price_map.entry(price.laptop_id).or_insert(price);
        }
        let enhanced_laptops = paginated_laptops
            .laptops
            .into_iter()
            .map(|laptop| LaptopFullDetailView {
                id: laptop.id,
                category_id: laptop.category_id,
                brand: laptop.brand,
                model: laptop.model,
                full_name: laptop.full_name,
                slug: laptop.slug,
                sku: laptop.sku,
                market_region: laptop.market_region,
                release_date: laptop.release_date,
                description: laptop.description,
                image_urls: laptop.image_urls,
                status: laptop.status,
                is_featured: laptop.is_featured,
                view_count: laptop.view_count,
                created_by: laptop.created_by,
                updated_by: laptop.updated_by,
                created_at: laptop.created_at,
                updated_at: laptop.updated_at,
                specification: spec_map.get(&laptop.id).cloned(),
                current_price: price_map.get(&laptop.id).cloned(),
            })
            .collect();
        Ok(PaginatedLaptopsFullDetail {
            laptops: enhanced_laptops,
            meta: paginated_laptops.meta,
        })
    }
}
