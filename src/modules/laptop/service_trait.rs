use crate::{
    errors::Result,
    modules::laptop::models::{
        CompleteLaptopView, CreateCompleteLaptopRequest, CreateLaptopRequest, CreatePriceRequest,
        CreateSpecificationRequest, LaptopFullDetailView, LaptopFullView, LaptopPaginationRequest,
        LaptopPublicDetailView, LaptopPublicView, PaginatedLaptopsDetailed, PaginatedLaptopsFull,
        PaginatedLaptopsFullDetail, PaginatedLaptopsPublic, PaginatedLaptopsPublicDetail, Price,
        Specification, UpdateLaptopRequest, UpdatePriceRequest, UpdateSpecificationRequest,
    },
};
use async_trait::async_trait;
use uuid::Uuid;

// ===== LAPTOP SERVICE TRAIT =====

#[async_trait]
pub trait LaptopServiceTrait: Send + Sync {
    // Basic CRUD operations
    async fn create_laptop(
        &self,
        request: CreateLaptopRequest,
        created_by: &Uuid,
    ) -> Result<LaptopFullView>;
    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView>;
    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView>;
    async fn update_laptop(
        &self,
        id: &Uuid,
        request: UpdateLaptopRequest,
        updated_by: &Uuid,
    ) -> Result<LaptopFullView>;
    async fn delete_laptop(&self, id: &Uuid) -> Result<()>;

    // Public API operations (no authentication required)
    async fn get_public_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic>;
    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView>;
    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()>;

    // Private API operations (authentication + permission required)
    async fn get_detailed_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed>;
    async fn get_full_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull>;

    // Status management operations
    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()>;
    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()>;
    async fn set_laptop_featured(
        &self,
        id: &Uuid,
        is_featured: bool,
        updated_by: &Uuid,
    ) -> Result<()>;

    // Validation operations
    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()>;
    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()>;
}

// ===== SPECIFICATION SERVICE TRAIT =====

#[async_trait]
pub trait SpecificationServiceTrait: Send + Sync {
    async fn create_specification(
        &self,
        request: CreateSpecificationRequest,
    ) -> Result<Specification>;
    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification>;
    async fn get_specification_by_laptop_id(
        &self,
        laptop_id: &Uuid,
    ) -> Result<Option<Specification>>;
    async fn get_specifications_by_laptop_ids(
        &self,
        laptop_ids: &[Uuid],
    ) -> Result<Vec<Specification>>;
    async fn update_specification(
        &self,
        id: &Uuid,
        request: UpdateSpecificationRequest,
    ) -> Result<Specification>;
    async fn delete_specification(&self, id: &Uuid) -> Result<()>;
    async fn delete_specification_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
}

// ===== PRICE SERVICE TRAIT =====

#[async_trait]
pub trait PriceServiceTrait: Send + Sync {
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price>;
    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price>;
    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn get_current_prices_by_laptop_ids(&self, laptop_ids: &[Uuid]) -> Result<Vec<Price>>;
    async fn update_price(
        &self,
        id: &Uuid,
        request: UpdatePriceRequest,
        updated_by: &Uuid,
    ) -> Result<Price>;
    async fn delete_price(&self, id: &Uuid) -> Result<()>;
    async fn delete_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()>;
}

// ===== COMBINED LAPTOP MANAGEMENT SERVICE TRAIT =====

#[async_trait]
pub trait LaptopManagementServiceTrait: Send + Sync {
    // Laptop operations
    async fn create_laptop(
        &self,
        request: CreateLaptopRequest,
        created_by: &Uuid,
    ) -> Result<LaptopFullView>;
    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView>;
    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView>;
    async fn update_laptop(
        &self,
        id: &Uuid,
        request: UpdateLaptopRequest,
        updated_by: &Uuid,
    ) -> Result<LaptopFullView>;
    async fn delete_laptop(&self, id: &Uuid) -> Result<()>;

    // Public API operations
    async fn get_public_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublic>;
    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView>;
    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()>;

    // Private API operations
    async fn get_detailed_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsDetailed>;
    async fn get_full_laptops(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFull>;

    // Status management
    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()>;
    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()>;
    async fn set_laptop_featured(
        &self,
        id: &Uuid,
        is_featured: bool,
        updated_by: &Uuid,
    ) -> Result<()>;

    // Specification operations
    async fn create_specification(
        &self,
        request: CreateSpecificationRequest,
    ) -> Result<Specification>;
    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification>;
    async fn get_specification_by_laptop_id(
        &self,
        laptop_id: &Uuid,
    ) -> Result<Option<Specification>>;
    async fn update_specification(
        &self,
        id: &Uuid,
        request: UpdateSpecificationRequest,
    ) -> Result<Specification>;
    async fn delete_specification(&self, id: &Uuid) -> Result<()>;

    // Price operations
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price>;
    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price>;
    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn update_price(
        &self,
        id: &Uuid,
        request: UpdatePriceRequest,
        updated_by: &Uuid,
    ) -> Result<Price>;
    async fn delete_price(&self, id: &Uuid) -> Result<()>;
    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()>;

    // Validation operations
    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()>;
    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()>;

    // Composite operations
    async fn create_complete_laptop(
        &self,
        request: CreateCompleteLaptopRequest,
        created_by: &Uuid,
    ) -> Result<CompleteLaptopView>;

    // Enhanced detail operations (with related data)
    async fn get_public_laptop_by_id_with_details(
        &self,
        id: &Uuid,
    ) -> Result<LaptopPublicDetailView>;
    async fn get_public_laptop_by_slug_with_details(
        &self,
        slug: &str,
    ) -> Result<LaptopPublicDetailView>;
    async fn get_laptop_by_id_with_details(&self, id: &Uuid) -> Result<LaptopFullDetailView>;
    async fn get_laptop_by_slug_with_details(&self, slug: &str) -> Result<LaptopFullDetailView>;
    async fn get_public_laptops_with_details(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsPublicDetail>;
    async fn get_full_laptops_with_details(
        &self,
        request: LaptopPaginationRequest,
    ) -> Result<PaginatedLaptopsFullDetail>;
}
