use crate::config::RedisConfig;
use anyhow::Result;
use bb8_redis::{RedisConnectionManager, bb8};
use std::time::Duration;

pub type RedisPool = bb8::Pool<RedisConnectionManager>;
pub type RedisConnection = bb8::PooledConnection<'static, RedisConnectionManager>;

/// Redis connection pool manager
#[derive(Clone)]
pub struct RedisManager {
    pool: RedisPool,
}

impl RedisManager {
    /// Create a new Redis manager with connection pool
    pub async fn new(config: &RedisConfig) -> Result<Self> {
        let manager = RedisConnectionManager::new(config.url.clone())?;

        let pool = bb8::Pool::builder()
            .max_size(config.max_connections)
            .connection_timeout(Duration::from_secs(config.connection_timeout_secs))
            .idle_timeout(Some(Duration::from_secs(config.idle_timeout_secs)))
            .max_lifetime(Some(Duration::from_secs(config.max_lifetime_secs)))
            .build(manager)
            .await?;

        // Test connection
        {
            let mut conn = pool.get().await?;
            let _: String = bb8_redis::redis::cmd("PING")
                .query_async(&mut *conn)
                .await?;
        }

        tracing::info!("Redis connection pool established successfully");

        Ok(Self { pool })
    }

    /// Get a connection from the pool
    pub async fn get_connection(&self) -> Result<bb8::PooledConnection<RedisConnectionManager>> {
        Ok(self.pool.get().await?)
    }

    /// Get pool statistics
    pub fn pool_status(&self) -> bb8::State {
        self.pool.state()
    }

    /// Health check for Redis connection
    pub async fn health_check(&self) -> Result<bool> {
        match self.get_connection().await {
            Ok(mut conn) => {
                match bb8_redis::redis::cmd("PING")
                    .query_async::<_, String>(&mut *conn)
                    .await
                {
                    Ok(response) => Ok(response == "PONG"),
                    Err(_) => Ok(false),
                }
            }
            Err(_) => Ok(false),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::RedisConfig;

    #[tokio::test]
    async fn test_redis_manager_creation() {
        let config = RedisConfig::default();

        // This test will only pass if Redis is running locally
        if let Ok(manager) = RedisManager::new(&config).await {
            assert!(manager.health_check().await.unwrap_or(false));
        }
    }
}
