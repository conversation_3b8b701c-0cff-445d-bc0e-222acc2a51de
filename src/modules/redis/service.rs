use anyhow::Result;
use async_trait::async_trait;
use bb8_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;

use super::redis_manager::RedisManager;
use crate::utils::error::circuit_breaker::CircuitBreaker;

/// Redis service trait for dependency injection
#[async_trait]
pub trait RedisServiceTrait: Send + Sync {
    /// Set a key-value pair with optional expiration
    async fn set(&self, key: &str, value: &str, expiration: Option<Duration>) -> Result<()>;

    /// Get value by key
    async fn get(&self, key: &str) -> Result<Option<String>>;

    /// Delete a key
    async fn delete(&self, key: &str) -> Result<bool>;

    /// Check if key exists
    async fn exists(&self, key: &str) -> Result<bool>;

    /// Set key expiration
    async fn expire(&self, key: &str, expiration: Duration) -> Result<bool>;

    /// Get time to live for a key
    async fn ttl(&self, key: &str) -> Result<i64>;

    /// Increment a numeric value
    async fn increment(&self, key: &str, delta: i64) -> Result<i64>;

    /// Hash operations
    async fn hset(&self, key: &str, field: &str, value: &str) -> Result<bool>;
    async fn hget(&self, key: &str, field: &str) -> Result<Option<String>>;
    async fn hdel(&self, key: &str, field: &str) -> Result<bool>;
    async fn hgetall(&self, key: &str) -> Result<Vec<(String, String)>>;

    /// List operations  
    async fn lpush(&self, key: &str, value: &str) -> Result<i64>;
    async fn rpush(&self, key: &str, value: &str) -> Result<i64>;
    async fn lpop(&self, key: &str) -> Result<Option<String>>;
    async fn rpop(&self, key: &str) -> Result<Option<String>>;
    async fn llen(&self, key: &str) -> Result<i64>;
    async fn lrange(&self, key: &str, start: i64, stop: i64) -> Result<Vec<String>>;
    async fn brpop(&self, key: &str, timeout_secs: u32) -> Result<Option<String>>;

    /// Set operations
    async fn sadd(&self, key: &str, value: &str) -> Result<bool>;
    async fn srem(&self, key: &str, value: &str) -> Result<bool>;
    async fn sismember(&self, key: &str, value: &str) -> Result<bool>;
    async fn smembers(&self, key: &str) -> Result<Vec<String>>;

    /// Utility methods
    async fn ping(&self) -> Result<String>;
    async fn flushdb(&self) -> Result<()>;

    /// Health check
    async fn health_check(&self) -> Result<bool>;
}

/// Redis service implementation
pub struct RedisService {
    redis_manager: RedisManager,
    circuit_breaker: CircuitBreaker, // New field
}

impl RedisService {
    /// Create new Redis service
    pub fn new(redis_manager: RedisManager) -> Self {
        // Create a simple circuit breaker without Redis persistence for now
        let circuit_breaker = CircuitBreaker::new_simple(5, Duration::from_secs(30));
        Self {
            redis_manager,
            circuit_breaker,
        }
    }

    /// JSON operations (for complex data structures) - These are concrete methods, not trait methods
    pub async fn set_json<T: Serialize + Sync>(
        &self,
        key: &str,
        value: &T,
        expiration: Option<Duration>,
    ) -> Result<()> {
        let json_str = serde_json::to_string(value)?;
        self.set(key, &json_str, expiration).await
    }

    pub async fn get_json<T: for<'de> Deserialize<'de>>(&self, key: &str) -> Result<Option<T>> {
        if let Some(json_str) = self.get(key).await? {
            let value: T = serde_json::from_str(&json_str)?;
            Ok(Some(value))
        } else {
            Ok(None)
        }
    }
}

#[async_trait]
impl RedisServiceTrait for RedisService {
    async fn set(&self, key: &str, value: &str, expiration: Option<Duration>) -> Result<()> {
        let key_owned = key.to_string();
        let key_owned2 = key.to_string();
        let value_owned = value.to_string();
        let redis_manager = self.redis_manager.clone();

        self.circuit_breaker
            .execute(
                move || {
                    let key = key_owned;
                    let value = value_owned;
                    let redis_manager = redis_manager.clone();
                    async move {
                        let mut conn = redis_manager.get_connection().await?;
                        if let Some(exp) = expiration {
                            conn.set_ex::<_, _, ()>(&key, &value, exp.as_secs()).await?;
                        } else {
                            conn.set::<_, _, ()>(&key, &value).await?;
                        }
                        Ok(())
                    }
                },
                move || {
                    let key = key_owned2;
                    async move {
                        tracing::warn!("Redis fallback for set: {}", key);
                        Ok(())
                    }
                },
            )
            .await
    }

    async fn get(&self, key: &str) -> Result<Option<String>> {
        let key_owned = key.to_string();
        let key_owned2 = key.to_string();
        let redis_manager = self.redis_manager.clone();

        self.circuit_breaker
            .execute(
                move || {
                    let key = key_owned;
                    let redis_manager = redis_manager.clone();
                    async move {
                        let mut conn = redis_manager.get_connection().await?;
                        conn.get(&key).await.map_err(|e| anyhow::anyhow!(e))
                    }
                },
                move || {
                    let key = key_owned2;
                    async move {
                        tracing::warn!("Redis fallback for get: {}", key);
                        Ok(None)
                    }
                },
            )
            .await
    }

    async fn delete(&self, key: &str) -> Result<bool> {
        let key_owned = key.to_string();
        let key_owned2 = key.to_string();
        let redis_manager = self.redis_manager.clone();

        self.circuit_breaker
            .execute(
                move || {
                    let key = key_owned;
                    let redis_manager = redis_manager.clone();
                    async move {
                        let mut conn = redis_manager.get_connection().await?;
                        let deleted: i64 = conn.del(&key).await?;
                        Ok(deleted > 0)
                    }
                },
                move || {
                    let key = key_owned2;
                    async move {
                        tracing::warn!("Redis fallback for delete: {}", key);
                        Ok(false)
                    }
                },
            )
            .await
    }

    async fn exists(&self, key: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let exists: bool = conn.exists(key).await?;
        Ok(exists)
    }

    async fn expire(&self, key: &str, expiration: Duration) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: bool = conn.expire(key, expiration.as_secs() as i64).await?;
        Ok(result)
    }

    async fn ttl(&self, key: &str) -> Result<i64> {
        let mut conn = self.redis_manager.get_connection().await?;
        let ttl: i64 = conn.ttl(key).await?;
        Ok(ttl)
    }

    async fn increment(&self, key: &str, delta: i64) -> Result<i64> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.incr(key, delta).await?;
        Ok(result)
    }

    async fn hset(&self, key: &str, field: &str, value: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: bool = conn.hset(key, field, value).await?;
        Ok(result)
    }

    async fn hget(&self, key: &str, field: &str) -> Result<Option<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Option<String> = conn.hget(key, field).await?;
        Ok(result)
    }

    async fn hdel(&self, key: &str, field: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let deleted: i64 = conn.hdel(key, field).await?;
        Ok(deleted > 0)
    }

    async fn hgetall(&self, key: &str) -> Result<Vec<(String, String)>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Vec<(String, String)> = conn.hgetall(key).await?;
        Ok(result)
    }

    async fn lpush(&self, key: &str, value: &str) -> Result<i64> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.lpush(key, value).await?;
        Ok(result)
    }

    async fn rpush(&self, key: &str, value: &str) -> Result<i64> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.rpush(key, value).await?;
        Ok(result)
    }

    async fn lpop(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Option<String> = conn.lpop(key, None).await?;
        Ok(result)
    }

    async fn rpop(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Option<String> = conn.rpop(key, None).await?;
        Ok(result)
    }

    async fn llen(&self, key: &str) -> Result<i64> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.llen(key).await?;
        Ok(result)
    }

    async fn lrange(&self, key: &str, start: i64, stop: i64) -> Result<Vec<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Vec<String> = conn.lrange(key, start as isize, stop as isize).await?;
        Ok(result)
    }

    async fn brpop(&self, key: &str, timeout_secs: u32) -> Result<Option<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Option<(String, String)> = conn.brpop(key, timeout_secs as f64).await?;
        Ok(result.map(|(_, value)| value))
    }

    async fn sadd(&self, key: &str, value: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.sadd(key, value).await?;
        Ok(result > 0)
    }

    async fn srem(&self, key: &str, value: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: i64 = conn.srem(key, value).await?;
        Ok(result > 0)
    }

    async fn sismember(&self, key: &str, value: &str) -> Result<bool> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: bool = conn.sismember(key, value).await?;
        Ok(result)
    }

    async fn smembers(&self, key: &str) -> Result<Vec<String>> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: Vec<String> = conn.smembers(key).await?;
        Ok(result)
    }

    async fn ping(&self) -> Result<String> {
        let mut conn = self.redis_manager.get_connection().await?;
        let result: String = bb8_redis::redis::cmd("PING")
            .query_async(&mut *conn)
            .await?;
        Ok(result)
    }

    async fn flushdb(&self) -> Result<()> {
        let mut conn = self.redis_manager.get_connection().await?;
        bb8_redis::redis::cmd("FLUSHDB")
            .query_async::<_, ()>(&mut *conn)
            .await?;
        Ok(())
    }

    async fn health_check(&self) -> Result<bool> {
        self.redis_manager.health_check().await
    }
}

// Type alias for easier use in dependency injection
pub type DynRedisService = Arc<dyn RedisServiceTrait>;

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::RedisConfig;
    use serde::{Deserialize, Serialize};

    #[derive(Serialize, Deserialize, Debug, PartialEq)]
    struct TestData {
        id: u32,
        name: String,
    }

    async fn create_test_service() -> Option<RedisService> {
        let config = RedisConfig::default();
        RedisManager::new(&config).await.ok().map(RedisService::new)
    }

    #[tokio::test]
    async fn test_basic_operations() {
        if let Some(service) = create_test_service().await {
            // Test ping
            assert!(service.ping().await.is_ok());

            // Test set/get
            assert!(service.set("test_key", "test_value", None).await.is_ok());
            let value = service.get("test_key").await.unwrap();
            assert_eq!(value, Some("test_value".to_string()));

            // Test delete
            assert!(service.delete("test_key").await.unwrap());
            let value = service.get("test_key").await.unwrap();
            assert_eq!(value, None);
        }
    }

    #[tokio::test]
    async fn test_json_operations() {
        if let Some(service) = create_test_service().await {
            let test_data = TestData {
                id: 1,
                name: "test".to_string(),
            };

            // Test JSON set/get
            assert!(
                service
                    .set_json("test_json", &test_data, None)
                    .await
                    .is_ok()
            );
            let retrieved: Option<TestData> = service.get_json("test_json").await.unwrap();
            assert_eq!(retrieved, Some(test_data));

            // Cleanup
            let _ = service.delete("test_json").await;
        }
    }
}
