use crate::{container::ServiceContainer, errors::AppError};
use axum::{Json, extract::Path, extract::State};
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Serialize, Deserialize)]
pub struct SetKeyRequest {
    pub value: String,
    pub ttl_seconds: Option<u64>,
}

#[derive(Serialize, Deserialize)]
pub struct RedisResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// GET /redis/status - Get Redis connection status
pub async fn redis_status(
    State(container): State<ServiceContainer>,
) -> Result<Json<RedisResponse>, AppError> {
    let status = container.redis_status();
    let is_available = container.is_redis_available();

    let response = RedisResponse {
        success: is_available,
        message: format!("Redis status: {status}"),
        data: Some(serde_json::json!({
            "status": status,
            "available": is_available
        })),
    };

    Ok(<PERSON><PERSON>(response))
}

/// POST /redis/{key} - Set a key-value pair
pub async fn set_key(
    State(container): State<ServiceContainer>,
    Path(key): Path<String>,
    Json(payload): Json<SetKeyRequest>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();
    let expiration = payload.ttl_seconds.map(Duration::from_secs);

    match redis_service.set(&key, &payload.value, expiration).await {
        Ok(_) => {
            let response = RedisResponse {
                success: true,
                message: format!("Key '{key}' set successfully"),
                data: Some(serde_json::json!({
                    "key": key,
                    "value": payload.value,
                    "ttl": payload.ttl_seconds
                })),
            };
            Ok(Json(response))
        }
        Err(e) => {
            let response = RedisResponse {
                success: false,
                message: format!("Failed to set key '{key}': {e}"),
                data: None,
            };
            Ok(Json(response))
        }
    }
}

/// GET /redis/{key} - Get value by key
pub async fn get_key(
    State(container): State<ServiceContainer>,
    Path(key): Path<String>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();

    match redis_service.get(&key).await {
        Ok(Some(value)) => {
            let response = RedisResponse {
                success: true,
                message: format!("Key '{key}' found"),
                data: Some(serde_json::json!({
                    "key": key,
                    "value": value
                })),
            };
            Ok(Json(response))
        }
        Ok(None) => {
            let response = RedisResponse {
                success: false,
                message: format!("Key '{key}' not found"),
                data: None,
            };
            Ok(Json(response))
        }
        Err(e) => {
            let response = RedisResponse {
                success: false,
                message: format!("Failed to get key '{key}': {e}"),
                data: None,
            };
            Ok(Json(response))
        }
    }
}

/// DELETE /redis/{key} - Delete a key
pub async fn delete_key(
    State(container): State<ServiceContainer>,
    Path(key): Path<String>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();

    match redis_service.delete(&key).await {
        Ok(deleted) => {
            let response = RedisResponse {
                success: deleted,
                message: if deleted {
                    format!("Key '{key}' deleted successfully")
                } else {
                    format!("Key '{key}' not found")
                },
                data: Some(serde_json::json!({
                    "key": key,
                    "deleted": deleted
                })),
            };
            Ok(Json(response))
        }
        Err(e) => {
            let response = RedisResponse {
                success: false,
                message: format!("Failed to delete key '{key}': {e}"),
                data: None,
            };
            Ok(Json(response))
        }
    }
}

/// GET /redis/ping - Ping Redis server
pub async fn ping_redis(
    State(container): State<ServiceContainer>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();

    match redis_service.ping().await {
        Ok(pong) => {
            let response = RedisResponse {
                success: true,
                message: "Redis ping successful".to_string(),
                data: Some(serde_json::json!({
                    "response": pong
                })),
            };
            Ok(Json(response))
        }
        Err(e) => {
            let response = RedisResponse {
                success: false,
                message: format!("Redis ping failed: {e}"),
                data: None,
            };
            Ok(Json(response))
        }
    }
}

/// GET /redis/queue/email_events - Check email events queue
pub async fn check_email_queue(
    State(container): State<ServiceContainer>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();

    // Check if key exists
    let exists = redis_service.exists("email_events").await.unwrap_or(false);

    if !exists {
        let response = RedisResponse {
            success: true,
            message: "Email events queue does not exist".to_string(),
            data: Some(serde_json::json!({
                "queue_name": "email_events",
                "exists": false,
                "length": 0,
                "items": []
            })),
        };
        return Ok(Json(response));
    }

    // Get queue length
    let length = redis_service.llen("email_events").await.unwrap_or(0);

    // Get all items in queue (limit to 10 for safety)
    let items_result: Vec<String> = if length > 0 {
        // Use lrange to get all items in the queue
        match redis_service.lrange("email_events", 0, -1).await {
            Ok(items) => items,
            Err(e) => {
                tracing::warn!("Failed to get queue items: {}", e);
                vec![]
            }
        }
    } else {
        vec![]
    };

    let response = RedisResponse {
        success: true,
        message: format!("Email events queue info retrieved. Length: {length}"),
        data: Some(serde_json::json!({
            "queue_name": "email_events",
            "exists": exists,
            "length": length,
            "items": items_result
        })),
    };

    Ok(Json(response))
}

/// GET /redis/keys - List all Redis keys
pub async fn list_keys(
    State(container): State<ServiceContainer>,
) -> Result<Json<RedisResponse>, AppError> {
    let redis_service = container.redis_service();

    // Check common keys that might exist
    let common_keys = vec![
        "email_events",
        "user:sessions",
        "rate_limit",
        "cache:permissions",
    ];

    let mut existing_keys = Vec::new();

    for key in common_keys {
        if let Ok(exists) = redis_service.exists(key).await {
            if exists {
                existing_keys.push(key.to_string());
            }
        }
    }

    let response = RedisResponse {
        success: true,
        message: format!("Found {} keys", existing_keys.len()),
        data: Some(serde_json::json!({
            "keys": existing_keys,
            "total_count": existing_keys.len()
        })),
    };

    Ok(Json(response))
}
