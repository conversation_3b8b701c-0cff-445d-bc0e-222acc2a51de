use crate::{
    errors::Result,
    modules::{
        permission::models::domain::Permission,
        role::models::{
            domain::Role,
            pagination::{PaginatedRoles, PaginatedRolesWithPermissions, RolePaginationRequest},
            requests::{CreateRoleRequest, PermissionMatrixUpdateRequest, UpdateRoleRequest},
            responses::PermissionMatrixUpdateResponse,
        },
    },
};
use async_trait::async_trait;
use std::collections::HashMap;
use uuid::Uuid;

/// Role query operations (ISP: Interface segregation)
#[async_trait]
pub trait RoleQueryTrait: Send + Sync {
    async fn get_role_by_id(&self, id: &Uuid) -> Result<Role>;
    async fn get_role_by_name(&self, name: &str) -> Result<Role>;
    async fn get_roles_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Role>>;
    async fn get_roles_by_names(&self, names: &[String]) -> Result<Vec<Role>>;
    async fn get_roles(&self, pagination: RolePaginationRequest) -> Result<PaginatedRoles>;
    async fn get_roles_with_permissions(
        &self,
        pagination: RolePaginationRequest,
    ) -> Result<PaginatedRolesWithPermissions>;
}

/// Role management operations (ISP: Interface segregation)
#[async_trait]
pub trait RoleManagementTrait: Send + Sync {
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role>;
    async fn create_role_direct(&self, role: Role) -> Result<Role>;
    async fn update_role(&self, id: &Uuid, request: UpdateRoleRequest) -> Result<Role>;
    async fn delete_role(&self, id: &Uuid) -> Result<()>;
}

/// Role-Permission relationship operations (ISP: Interface segregation)
#[async_trait]
pub trait RolePermissionTrait: Send + Sync {
    async fn get_permissions_for_role(&self, role_id: &Uuid) -> Result<Vec<Permission>>;
    async fn list_permissions_for_roles(
        &self,
        role_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, Vec<Permission>>>;
    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<String>>;
    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool>;
    async fn assign_permission_to_role(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<()>;
    async fn assign_permission_to_role_without_cache_invalidation(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
    ) -> Result<()>;
    async fn assign_permissions_to_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()>;
    async fn remove_permission_from_role(&self, role_id: &Uuid, permission_id: &Uuid)
    -> Result<()>;
    async fn replace_permissions_for_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()>;
}

/// Bulk permission matrix operations (ISP: Interface segregation)
#[async_trait]
pub trait RoleMatrixTrait: Send + Sync {
    async fn update_permission_matrix(
        &self,
        request: PermissionMatrixUpdateRequest,
    ) -> Result<PermissionMatrixUpdateResponse>;
}

/// Combined RoleService trait (DIP: Depend on abstractions)
/// Uses trait inheritance to compose smaller interfaces
pub trait RoleServiceTrait:
    RoleQueryTrait + RoleManagementTrait + RolePermissionTrait + RoleMatrixTrait + Send + Sync
{
}
