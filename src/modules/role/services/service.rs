use crate::errors::Result;
use crate::utils::validation::helpers::validate_enhanced_and_execute;
use crate::utils::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValidateRequest};
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

use crate::modules::permission::service_trait::PermissionServiceTrait;
use crate::modules::redis::RedisServiceTrait;
use crate::modules::role::database::models::UpdateRole;
use crate::modules::role::database::repository::DynRoleRepo;
use crate::modules::role::models::{
    domain::{Role, RoleWithPermissions},
    pagination::{PaginatedRoles, PaginatedRolesWithPermissions, RolePaginationRequest},
    requests::{CreateRoleRequest, PermissionMatrixUpdateRequest, UpdateRoleRequest},
    responses::PermissionMatrixUpdateResponse,
};
use crate::modules::role::traits::service_trait::{
    RoleManagementTrait, RoleMatrixTrait, <PERSON>PermissionTrait, <PERSON>QueryTrait, RoleServiceTrait,
};
use crate::utils::pagination::PaginationRequest;
use async_trait::async_trait;
use std::collections::HashMap;

#[derive(Clone)]
pub struct RoleService {
    repository: DynRoleRepo,
    permission_service: Arc<dyn PermissionServiceTrait>,
    #[allow(dead_code)]
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
}

impl RoleService {
    pub fn new(
        repository: DynRoleRepo,
        permission_service: Arc<dyn PermissionServiceTrait>,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        Self {
            repository,
            permission_service,
            redis_service,
        }
    }

    /// Get user IDs for specific roles (helper for cache invalidation)
    #[allow(dead_code)]
    async fn get_user_ids_for_roles(&self, role_ids: &[Uuid]) -> Result<Vec<Uuid>> {
        // TODO: Implement this using SQLx when user_roles repository is migrated
        if role_ids.is_empty() {
            return Ok(vec![]);
        }

        Ok(vec![])
    }

    /// Generate cache key for permission version (matching UserService pattern)
    #[allow(dead_code)]
    fn permission_version_cache_key(user_id: &Uuid) -> String {
        format!("user:perm_version:{user_id}")
    }

    /// Invalidate permission version cache for multiple users
    #[allow(dead_code)]
    async fn invalidate_permission_version_cache_bulk(&self, user_ids: &[Uuid]) -> Result<()> {
        if let Some(redis) = &self.redis_service {
            for user_id in user_ids {
                let cache_key = Self::permission_version_cache_key(user_id);
                match redis.delete(&cache_key).await {
                    Ok(deleted) => {
                        if deleted {
                            tracing::info!(
                                user_id = %user_id,
                                cache_key = %cache_key,
                                operation = "bulk_role_update",
                                "Successfully invalidated permission version cache"
                            );
                        } else {
                            tracing::debug!(
                                user_id = %user_id,
                                cache_key = %cache_key,
                                operation = "bulk_role_update",
                                "Cache key did not exist, no invalidation needed"
                            );
                        }
                    }
                    Err(e) => {
                        tracing::warn!(
                            user_id = %user_id,
                            cache_key = %cache_key,
                            operation = "bulk_role_update",
                            error = %e,
                            "Failed to invalidate permission version cache"
                        );
                        // Don't fail the operation if cache invalidation fails
                    }
                }
            }
        } else {
            tracing::debug!(
                affected_users = user_ids.len(),
                operation = "bulk_role_update",
                "Redis not available, skipping cache invalidation"
            );
        }
        Ok(())
    }

    /// Helper method to increment permission_version for all users with specific roles
    async fn increment_permission_version_for_roles(&self, role_ids: &[Uuid]) -> Result<()> {
        if role_ids.is_empty() {
            return Ok(());
        }

        // TODO: Implement this using SQLx when user_roles and users repositories are migrated
        // For now, just invalidate cache for all users (temporary solution)

        tracing::info!(
            affected_roles = role_ids.len(),
            "Temporarily skipping permission version increment - will be implemented with SQLx migration"
        );

        Ok(())
    }
}

// Implement RoleQueryTrait
#[async_trait]
impl RoleQueryTrait for RoleService {
    async fn get_role_by_id(&self, id: &Uuid) -> Result<Role> {
        let role = self.repository.find_by_id(id).await?;
        role.ok_or_else(|| ErrorHelper::not_found_with_option("Role", Some(&id.to_string())))
    }

    async fn get_role_by_name(&self, name: &str) -> Result<Role> {
        let role = self.repository.find_by_name(name).await?;
        role.ok_or_else(|| ErrorHelper::not_found_by_name("Role", name))
    }

    async fn get_roles_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Role>> {
        self.repository.find_by_ids(ids).await
    }

    async fn get_roles_by_names(&self, names: &[String]) -> Result<Vec<Role>> {
        self.repository.find_by_names(names).await
    }

    async fn get_roles(&self, pagination: RolePaginationRequest) -> Result<PaginatedRoles> {
        pagination.validate_request()?;

        let (roles, total) = self.repository.find_all(&pagination).await?;

        let pagination_meta = crate::utils::pagination::PaginationMeta::new(
            pagination.page(),
            pagination.limit(),
            total,
        );

        Ok(PaginatedRoles {
            roles,
            meta: pagination_meta,
        })
    }

    async fn get_roles_with_permissions(
        &self,
        pagination: RolePaginationRequest,
    ) -> Result<PaginatedRolesWithPermissions> {
        pagination.validate_request()?;

        let (roles, total) = self.repository.find_all(&pagination).await?;

        if roles.is_empty() {
            let pagination_meta = crate::utils::pagination::PaginationMeta::new(
                pagination.page(),
                pagination.limit(),
                total,
            );
            return Ok(PaginatedRolesWithPermissions {
                roles: vec![],
                meta: pagination_meta,
            });
        }

        let role_ids: Vec<Uuid> = roles.iter().map(|r| r.id).collect();

        let permissions_map = self
            .repository
            .list_permissions_for_roles(&role_ids)
            .await?;
        let user_counts = self.repository.count_users_for_roles(&role_ids).await?;

        let roles_with_permissions = roles
            .into_iter()
            .map(|role| {
                let permissions = permissions_map.get(&role.id).cloned().unwrap_or_default();
                let user_count = user_counts.get(&role.id).copied().unwrap_or(0);
                // Convert permission::models::Permission to role::models::domain::Permission
                let role_permissions = permissions
                    .into_iter()
                    .map(|p| crate::modules::role::models::domain::Permission {
                        id: p.id,
                        name: p.name,
                        description: p.description,
                        created_at: p.created_at,
                        updated_at: p.updated_at,
                    })
                    .collect();
                RoleWithPermissions::from_role_and_permissions(role, role_permissions, user_count)
            })
            .collect();

        let pagination_meta = crate::utils::pagination::PaginationMeta::new(
            pagination.page(),
            pagination.limit(),
            total,
        );

        Ok(PaginatedRolesWithPermissions {
            roles: roles_with_permissions,
            meta: pagination_meta,
        })
    }
}

// Implement RoleManagementTrait
#[async_trait]
impl RoleManagementTrait for RoleService {
    async fn create_role(&self, request: CreateRoleRequest) -> Result<Role> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if role name already exists
            if self.repository.exists_by_name(&req.name).await? {
                return Err(ErrorHelper::conflict("Role", "name", &req.name));
            }

            // Create new role
            let role = Role::new(req.name, req.description);

            // Save to database
            self.repository.create(role).await
        })
        .await
    }

    async fn create_role_direct(&self, role: Role) -> Result<Role> {
        // Check if role name already exists
        if self.repository.exists_by_name(&role.name).await? {
            return Err(ErrorHelper::conflict("Role", "name", &role.name));
        }

        // Save to database directly
        self.repository.create(role).await
    }

    async fn update_role(&self, id: &Uuid, request: UpdateRoleRequest) -> Result<Role> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if role exists
            let _existing_role =
                self.repository.find_by_id(id).await?.ok_or_else(|| {
                    ErrorHelper::not_found_with_option("Role", Some(&id.to_string()))
                })?;

            // Check for name conflicts (excluding current role)
            if let Some(name) = &req.name {
                if self.repository.exists_by_name_exclude_id(name, id).await? {
                    return Err(ErrorHelper::conflict("Role", "name", name));
                }
            }

            // Create update data
            let update_data = UpdateRole {
                name: req.name,
                description: req.description,
                updated_at: Utc::now(),
            };

            // Update role
            let updated_role = self
                .repository
                .update(id, update_data)
                .await?
                .ok_or_else(|| ErrorHelper::not_found_with_option("Role", Some(&id.to_string())))?;

            Ok(updated_role)
        })
        .await
    }

    async fn delete_role(&self, id: &Uuid) -> Result<()> {
        let deleted = self.repository.delete(id).await?;
        if !deleted {
            return Err(ErrorHelper::not_found_with_option(
                "Role",
                Some(&id.to_string()),
            ));
        }
        Ok(())
    }
}

// Implement RolePermissionTrait
#[async_trait]
impl RolePermissionTrait for RoleService {
    async fn get_permissions_for_role(
        &self,
        role_id: &Uuid,
    ) -> Result<Vec<crate::modules::permission::models::domain::Permission>> {
        let role_permissions = self.repository.list_permissions_for_role(role_id).await?;
        // Convert role::models::domain::Permission to permission::models::domain::Permission
        let permissions = role_permissions
            .into_iter()
            .map(
                |rp| crate::modules::permission::models::domain::Permission {
                    id: rp.id,
                    name: rp.name.clone(),
                    description: rp.description,
                    created_at: rp.created_at,
                    updated_at: rp.updated_at,
                },
            )
            .collect();
        Ok(permissions)
    }

    async fn list_permissions_for_roles(
        &self,
        role_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, Vec<crate::modules::permission::models::domain::Permission>>> {
        let role_permissions_map = self.repository.list_permissions_for_roles(role_ids).await?;
        // Convert role::models::domain::Permission to permission::models::domain::Permission
        let permissions_map = role_permissions_map
            .into_iter()
            .map(|(role_id, role_permissions)| {
                let permissions = role_permissions
                    .into_iter()
                    .map(
                        |rp| crate::modules::permission::models::domain::Permission {
                            id: rp.id,
                            name: rp.name.clone(),
                            description: rp.description,
                            created_at: rp.created_at,
                            updated_at: rp.updated_at,
                        },
                    )
                    .collect();
                (role_id, permissions)
            })
            .collect();
        Ok(permissions_map)
    }

    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<String>> {
        let permission_ids = self.repository.get_role_permissions(role_id).await?;
        if permission_ids.is_empty() {
            return Ok(vec![]);
        }

        let permissions = self
            .permission_service
            .get_permissions_by_ids(&permission_ids)
            .await?;
        let permission_names = permissions.into_iter().map(|p| p.name).collect();

        Ok(permission_names)
    }

    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool> {
        self.repository
            .role_has_permission(role_id, permission_id)
            .await
    }

    async fn assign_permission_to_role(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<()> {
        self.repository
            .assign_permission_to_role(role_id, permission_id)
            .await?;
        self.increment_permission_version_for_roles(&[*role_id])
            .await?;
        Ok(())
    }

    async fn assign_permission_to_role_without_cache_invalidation(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
    ) -> Result<()> {
        self.repository
            .assign_permission_to_role(role_id, permission_id)
            .await?;
        // Skip cache invalidation for initialization
        Ok(())
    }

    async fn assign_permissions_to_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()> {
        self.repository
            .assign_permissions_to_role(role_id, permission_ids)
            .await?;
        self.increment_permission_version_for_roles(&[*role_id])
            .await?;
        Ok(())
    }

    async fn remove_permission_from_role(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
    ) -> Result<()> {
        self.repository
            .remove_permission_from_role(role_id, permission_id)
            .await?;
        self.increment_permission_version_for_roles(&[*role_id])
            .await?;
        Ok(())
    }

    async fn replace_permissions_for_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()> {
        self.repository
            .replace_permissions_for_role(role_id, permission_ids)
            .await?;
        self.increment_permission_version_for_roles(&[*role_id])
            .await?;
        Ok(())
    }
}

// Implement RoleMatrixTrait
#[async_trait]
impl RoleMatrixTrait for RoleService {
    async fn update_permission_matrix(
        &self,
        request: PermissionMatrixUpdateRequest,
    ) -> Result<PermissionMatrixUpdateResponse> {
        // Validate request using enhanced validation
        use crate::utils::validation::ValidateRequestEnhanced;
        let validation_result = request.validate_enhanced();
        if !validation_result.is_valid() {
            return Err(crate::errors::AppError::ValidationDetailed(
                validation_result,
            ));
        }

        // Collect unique role IDs and permission IDs for validation
        let role_ids: std::collections::HashSet<Uuid> =
            request.changes.iter().map(|c| c.role_id).collect();
        let permission_ids: std::collections::HashSet<Uuid> =
            request.changes.iter().map(|c| c.permission_id).collect();

        // Validate all roles exist in one query
        let found_roles = self
            .repository
            .find_by_ids(&role_ids.iter().cloned().collect::<Vec<_>>())
            .await?;
        if found_roles.len() != role_ids.len() {
            let found_role_ids: std::collections::HashSet<Uuid> =
                found_roles.iter().map(|r| r.id).collect();
            for role_id in &role_ids {
                if !found_role_ids.contains(role_id) {
                    return Err(ErrorHelper::not_found_with_option(
                        "Role",
                        Some(&role_id.to_string()),
                    ));
                }
            }
        }

        // Validate all permissions exist in one query
        let found_permissions = self
            .permission_service
            .get_permissions_by_ids(&permission_ids.iter().cloned().collect::<Vec<_>>())
            .await?;
        if found_permissions.len() != permission_ids.len() {
            let found_permission_ids: std::collections::HashSet<Uuid> =
                found_permissions.iter().map(|p| p.id).collect();
            for permission_id in &permission_ids {
                if !found_permission_ids.contains(permission_id) {
                    return Err(ErrorHelper::not_found_with_option(
                        "Permission",
                        Some(&permission_id.to_string()),
                    ));
                }
            }
        }

        // Execute bulk permission updates in transaction
        self.repository
            .bulk_update_role_permissions(&request.changes)
            .await?;

        // Increment permission version for all affected roles in a single query
        let role_ids_vec: Vec<Uuid> = role_ids.into_iter().collect();
        self.increment_permission_version_for_roles(&role_ids_vec)
            .await?;

        // Build response with names from the data we already fetched
        let role_names: Vec<String> = found_roles.into_iter().map(|r| r.name).collect();
        let permission_names: Vec<String> = found_permissions.into_iter().map(|p| p.name).collect();

        Ok(PermissionMatrixUpdateResponse {
            changes_applied: request.changes.len() as i32,
            roles_affected: role_names,
            permissions_affected: permission_names,
        })
    }
}

// RoleServiceTrait is automatically implemented through trait inheritance
impl RoleServiceTrait for RoleService {}
