use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct SqlxRole {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== CREATE PARAMS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewRole {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateRole {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub updated_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxRole> for crate::modules::role::models::domain::Role {
    fn from(model: SqlxRole) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }
}
