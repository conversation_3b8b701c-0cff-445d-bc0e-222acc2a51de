use crate::{errors::Result, utils::pagination::PaginationRequest};
use async_trait::async_trait;
use sqlx::PgPool;
use std::collections::HashMap;
use std::sync::Arc;
use uuid::Uuid;

use crate::modules::permission::models::domain::Permission;
use crate::modules::role::database::models::{SqlxRole, UpdateRole};
use crate::modules::role::models::{
    domain::Role, pagination::RolePaginationRequest, requests::PermissionMatrixChange,
};

#[async_trait]
pub trait RoleRepositoryTrait: Send + Sync {
    async fn create(&self, role: Role) -> Result<Role>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Role>>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Role>>;
    async fn find_all(&self, pagination: &RolePaginationRequest) -> Result<(Vec<Role>, i64)>;
    async fn update(&self, id: &Uuid, update_data: UpdateRole) -> Result<Option<Role>>;
    async fn delete(&self, id: &Uuid) -> Result<bool>;
    async fn exists_by_name(&self, name: &str) -> Result<bool>;
    async fn exists_by_name_exclude_id(&self, name: &str, id: &Uuid) -> Result<bool>;
    async fn assign_permission_to_role(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<()>;
    async fn remove_permission_from_role(&self, role_id: &Uuid, permission_id: &Uuid)
    -> Result<()>;
    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<Uuid>>;
    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool>;
    async fn replace_permissions_for_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()>;
    async fn assign_permissions_to_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()>;
    async fn list_permissions_for_role(&self, role_id: &Uuid) -> Result<Vec<Permission>>;
    async fn count_users_for_role(&self, role_id: &Uuid) -> Result<i64>;

    // Bulk permission matrix operations
    async fn bulk_update_role_permissions(&self, changes: &[PermissionMatrixChange]) -> Result<()>;

    async fn list_permissions_for_roles(
        &self,
        role_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, Vec<Permission>>>;
    async fn count_users_for_roles(&self, role_ids: &[Uuid]) -> Result<HashMap<Uuid, i64>>;

    async fn find_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Role>>;

    async fn find_by_names(&self, names: &[String]) -> Result<Vec<Role>>;
}

pub type DynRoleRepo = Arc<dyn RoleRepositoryTrait>;

#[derive(Clone)]
pub struct SqlxRoleRepository {
    pool: PgPool,
}

impl SqlxRoleRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// Helper function for bulk insertion of role-permission assignments
    /// Uses optimized batch insert with PostgreSQL UNNEST for better performance
    async fn bulk_insert_role_permissions(
        &self,
        role_id: Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()> {
        if permission_ids.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await?;

        // First, get existing permissions for this role to avoid duplicates
        let existing_permissions = sqlx::query!(
            "SELECT permission_id FROM role_permissions WHERE role_id = $1 AND permission_id = ANY($2)",
            role_id,
            permission_ids
        )
        .fetch_all(&mut *tx)
        .await?;

        let existing_ids: Vec<Uuid> = existing_permissions
            .into_iter()
            .map(|r| r.permission_id)
            .collect();

        // Filter out existing permissions
        let new_permission_ids: Vec<Uuid> = permission_ids
            .iter()
            .filter(|pid| !existing_ids.contains(pid))
            .cloned()
            .collect();

        // Insert only new permissions using batch insert with UNNEST
        if !new_permission_ids.is_empty() {
            // Use PostgreSQL UNNEST for efficient batch insert
            sqlx::query!(
                "INSERT INTO role_permissions (role_id, permission_id) 
                 SELECT $1, unnest($2::uuid[])
                 ON CONFLICT (role_id, permission_id) DO NOTHING",
                role_id,
                &new_permission_ids
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }
}

#[async_trait]
impl RoleRepositoryTrait for SqlxRoleRepository {
    async fn create(&self, role: Role) -> Result<Role> {
        sqlx::query_as!(
            SqlxRole,
            r#"
            INSERT INTO roles (id, name, description)
            VALUES ($1, $2, $3)
            RETURNING id, name, description, created_at, updated_at
            "#,
            role.id,
            role.name,
            role.description
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(role)
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Role>> {
        let role = sqlx::query_as!(
            SqlxRole,
            r#"
            SELECT id, name, description, created_at, updated_at
            FROM roles
            WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(role.map(Role::from))
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Role>> {
        let role = sqlx::query_as!(
            SqlxRole,
            r#"
            SELECT id, name, description, created_at, updated_at
            FROM roles
            WHERE name = $1
            "#,
            name
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(role.map(Role::from))
    }

    async fn find_all(&self, pagination: &RolePaginationRequest) -> Result<(Vec<Role>, i64)> {
        let limit = pagination.limit();
        let offset = pagination.offset();
        let name_filter = pagination.name.clone();

        // Build count query
        let count_query = if let Some(ref name) = name_filter {
            if !name.trim().is_empty() {
                sqlx::query!(
                    "SELECT COUNT(*) as count FROM roles WHERE name ILIKE $1",
                    format!("%{}%", name)
                )
                .fetch_one(&self.pool)
                .await?
                .count
            } else {
                sqlx::query!("SELECT COUNT(*) as count FROM roles")
                    .fetch_one(&self.pool)
                    .await?
                    .count
            }
        } else {
            sqlx::query!("SELECT COUNT(*) as count FROM roles")
                .fetch_one(&self.pool)
                .await?
                .count
        };

        // Build data query
        let roles = if let Some(ref name) = name_filter {
            if !name.trim().is_empty() {
                sqlx::query_as!(
                    SqlxRole,
                    r#"
                    SELECT id, name, description, created_at, updated_at
                    FROM roles
                    WHERE name ILIKE $1
                    ORDER BY created_at DESC
                    LIMIT $2 OFFSET $3
                    "#,
                    format!("%{}%", name),
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await?
            } else {
                sqlx::query_as!(
                    SqlxRole,
                    r#"
                    SELECT id, name, description, created_at, updated_at
                    FROM roles
                    ORDER BY created_at DESC
                    LIMIT $1 OFFSET $2
                    "#,
                    limit,
                    offset
                )
                .fetch_all(&self.pool)
                .await?
            }
        } else {
            sqlx::query_as!(
                SqlxRole,
                r#"
                SELECT id, name, description, created_at, updated_at
                FROM roles
                ORDER BY created_at DESC
                LIMIT $1 OFFSET $2
                "#,
                limit,
                offset
            )
            .fetch_all(&self.pool)
            .await?
        };

        let roles: Vec<Role> = roles.into_iter().map(Role::from).collect();
        Ok((roles, count_query.unwrap_or(0)))
    }

    async fn update(&self, id: &Uuid, update_data: UpdateRole) -> Result<Option<Role>> {
        let description = update_data.description.flatten();
        let role = sqlx::query_as!(
            SqlxRole,
            r#"
            UPDATE roles
            SET name = COALESCE($2, name),
                description = COALESCE($3, description),
                updated_at = $4
            WHERE id = $1
            RETURNING id, name, description, created_at, updated_at
            "#,
            id,
            update_data.name,
            description.as_deref(),
            update_data.updated_at
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(role.map(Role::from))
    }

    async fn delete(&self, id: &Uuid) -> Result<bool> {
        let result = sqlx::query!("DELETE FROM roles WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    async fn exists_by_name(&self, name: &str) -> Result<bool> {
        let count = sqlx::query!("SELECT COUNT(*) as count FROM roles WHERE name = $1", name)
            .fetch_one(&self.pool)
            .await?
            .count;

        Ok(count.unwrap_or(0) > 0)
    }

    async fn exists_by_name_exclude_id(&self, name: &str, exclude_id: &Uuid) -> Result<bool> {
        let count = sqlx::query!(
            "SELECT COUNT(*) as count FROM roles WHERE name = $1 AND id != $2",
            name,
            exclude_id
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        Ok(count.unwrap_or(0) > 0)
    }

    async fn assign_permission_to_role(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<()> {
        // Check if assignment already exists
        let exists = sqlx::query!(
            "SELECT 1 as exists FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
            role_id,
            permission_id
        )
        .fetch_optional(&self.pool)
        .await?;

        if exists.is_some() {
            return Ok(()); // Already assigned
        }

        // Insert new assignment
        sqlx::query!(
            "INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2)",
            role_id,
            permission_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn remove_permission_from_role(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
    ) -> Result<()> {
        sqlx::query!(
            "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
            role_id,
            permission_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<Uuid>> {
        let permissions = sqlx::query!(
            "SELECT permission_id FROM role_permissions WHERE role_id = $1",
            role_id
        )
        .fetch_all(&self.pool)
        .await?;

        let permission_ids: Vec<Uuid> = permissions.into_iter().map(|r| r.permission_id).collect();
        Ok(permission_ids)
    }

    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool> {
        let exists = sqlx::query!(
            "SELECT 1 as exists FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
            role_id,
            permission_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(exists.is_some())
    }

    async fn replace_permissions_for_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        if permission_ids.is_empty() {
            // Just delete all permissions if no new permissions to assign
            sqlx::query!("DELETE FROM role_permissions WHERE role_id = $1", role_id)
                .execute(&mut *tx)
                .await?;
        } else {
            // Delete all existing permissions for the role
            sqlx::query!("DELETE FROM role_permissions WHERE role_id = $1", role_id)
                .execute(&mut *tx)
                .await?;

            // Insert new permissions using batch insert with UNNEST
            sqlx::query!(
                "INSERT INTO role_permissions (role_id, permission_id) 
                 SELECT $1, unnest($2::uuid[])",
                role_id,
                permission_ids
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    async fn assign_permissions_to_role(
        &self,
        role_id: &Uuid,
        permission_ids: &[Uuid],
    ) -> Result<()> {
        self.bulk_insert_role_permissions(*role_id, permission_ids)
            .await
    }

    async fn list_permissions_for_role(&self, role_id: &Uuid) -> Result<Vec<Permission>> {
        let permissions = sqlx::query_as!(
            Permission,
            r#"
            SELECT p.id, p.name, p.description, p.created_at, p.updated_at
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
            ORDER BY p.name
            "#,
            role_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(permissions)
    }

    async fn count_users_for_role(&self, role_id: &Uuid) -> Result<i64> {
        let count = sqlx::query!(
            "SELECT COUNT(*) as count FROM user_roles WHERE role_id = $1",
            role_id
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        Ok(count.unwrap_or(0))
    }

    async fn bulk_update_role_permissions(&self, changes: &[PermissionMatrixChange]) -> Result<()> {
        if changes.is_empty() {
            return Ok(());
        }

        let mut tx = self.pool.begin().await?;

        // Separate changes into grants and revokes for batch processing
        let grants: Vec<&PermissionMatrixChange> = changes.iter().filter(|c| c.granted).collect();
        let revokes: Vec<&PermissionMatrixChange> = changes.iter().filter(|c| !c.granted).collect();

        // Batch process grants using VALUES clause for better performance
        if !grants.is_empty() {
            let mut values_clause = String::new();
            let mut params = Vec::new();

            for (i, change) in grants.iter().enumerate() {
                if i > 0 {
                    values_clause.push_str(", ");
                }
                let param_idx = i * 2;
                values_clause.push_str(&format!("(${}, ${})", param_idx + 1, param_idx + 2));
                params.push(change.role_id);
                params.push(change.permission_id);
            }

            let query = format!(
                "INSERT INTO role_permissions (role_id, permission_id) VALUES {} ON CONFLICT (role_id, permission_id) DO NOTHING",
                values_clause
            );

            let mut query_builder = sqlx::query(&query);
            for param in params {
                query_builder = query_builder.bind(param);
            }

            query_builder.execute(&mut *tx).await?;
        }

        // Batch process revokes using IN clause for better performance
        if !revokes.is_empty() {
            // Group revokes by role_id for more efficient deletion
            use std::collections::HashMap;
            let mut revokes_by_role: HashMap<uuid::Uuid, Vec<uuid::Uuid>> = HashMap::new();

            for change in revokes {
                revokes_by_role
                    .entry(change.role_id)
                    .or_default()
                    .push(change.permission_id);
            }

            // Delete permissions for each role in batch
            for (role_id, permission_ids) in revokes_by_role {
                sqlx::query!(
                    "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = ANY($2)",
                    role_id,
                    &permission_ids
                )
                .execute(&mut *tx)
                .await?;
            }
        }

        tx.commit().await?;
        Ok(())
    }

    async fn list_permissions_for_roles(
        &self,
        role_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, Vec<Permission>>> {
        if role_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let permissions_list = sqlx::query!(
            r#"
            SELECT rp.role_id, p.id as permission_id, p.name, p.description, p.created_at, p.updated_at
            FROM role_permissions rp
            INNER JOIN permissions p ON rp.permission_id = p.id
            WHERE rp.role_id = ANY($1)
            ORDER BY rp.role_id, p.name
            "#,
            role_ids
        )
        .fetch_all(&self.pool)
        .await?;

        let mut result: HashMap<Uuid, Vec<Permission>> = HashMap::new();

        for row in permissions_list {
            let permission = Permission {
                id: row.permission_id,
                name: row.name,
                description: row.description,
                created_at: row.created_at,
                updated_at: row.updated_at,
            };

            result.entry(row.role_id).or_default().push(permission);
        }

        Ok(result)
    }

    async fn count_users_for_roles(&self, role_ids: &[Uuid]) -> Result<HashMap<Uuid, i64>> {
        if role_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let counts = sqlx::query!(
            "SELECT role_id, COUNT(*) as count FROM user_roles WHERE role_id = ANY($1) GROUP BY role_id",
            role_ids
        )
        .fetch_all(&self.pool)
        .await?;

        let mut result: HashMap<Uuid, i64> = HashMap::new();

        for row in counts {
            result.insert(row.role_id, row.count.unwrap_or(0));
        }

        // Ensure all role_ids are present in result (with count 0)
        for role_id in role_ids {
            result.entry(*role_id).or_insert(0);
        }

        Ok(result)
    }

    async fn find_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Role>> {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        let roles = sqlx::query_as!(
            SqlxRole,
            r#"
            SELECT id, name, description, created_at, updated_at
            FROM roles
            WHERE id = ANY($1)
            ORDER BY created_at DESC
            "#,
            ids
        )
        .fetch_all(&self.pool)
        .await?;

        let roles: Vec<Role> = roles.into_iter().map(Role::from).collect();
        Ok(roles)
    }

    async fn find_by_names(&self, names: &[String]) -> Result<Vec<Role>> {
        if names.is_empty() {
            return Ok(Vec::new());
        }

        let roles = sqlx::query_as!(
            SqlxRole,
            r#"
            SELECT id, name, description, created_at, updated_at
            FROM roles
            WHERE name = ANY($1)
            ORDER BY created_at DESC
            "#,
            names
        )
        .fetch_all(&self.pool)
        .await?;

        let roles: Vec<Role> = roles.into_iter().map(Role::from).collect();
        Ok(roles)
    }
}
