use crate::modules::role::traits::service_trait::RoleServiceTrait;
use crate::{
    constants::API_ROLES_PATH,
    errors::Result,
    modules::{
        permission::models::domain::Permission,
        role::models::{
            domain::Role,
            pagination::{PaginatedRolesWithPermissions, RolePaginationRequest},
            requests::{
                CreateRoleRequest, PermissionMatrixUpdateRequest, ReplaceRolePermissionsRequest,
                UpdateRoleRequest,
            },
            responses::PermissionMatrixUpdateResponse,
        },
    },
    response::{ApiResponse, ApiResponseJson},
    utils::response_helpers::{ResponseHelper, response_constants::role},
};
use axum::{
    Json,
    extract::{Path, Query, State},
    response::IntoResponse,
};
use std::sync::Arc;
use utoipa::OpenApi;
use uuid::Uuid;

#[derive(OpenApi)]
#[openapi(
    paths(create_role, get_role, get_roles, update_role, delete_role, get_role_permissions, update_role_permissions, update_permission_matrix),
    components(schemas(
        Role,
        PaginatedRolesWithPermissions,
        RolePaginationRequest,
        CreateRoleRequest,
        UpdateRoleRequest,
        ReplaceRolePermissionsRequest,
        PermissionMatrixUpdateRequest,
        PermissionMatrixUpdateResponse,
        Permission
    )),
    tags((name = "Roles", description = "Role management endpoints"))
)]
pub struct RoleApiDoc;

#[utoipa::path(
    post,
    path = "/api/roles",
    tag = "Roles",
    request_body = crate::modules::role::models::requests::CreateRoleRequest,
    responses(
        (status = 201, description = "Role created successfully", body = ApiResponse<Role>),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Role already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_role(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Json(request): Json<CreateRoleRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let role = role_service.create_role(request).await?;

    Ok(ResponseHelper::entity_created(
        API_ROLES_PATH,
        role::SUCCESS_CREATE,
        role::MSG_CREATED,
        role,
    ))
}

#[utoipa::path(
    get,
    path = "/api/roles/{id}",
    tag = "Roles",
    params(
        ("id" = String, Path, description = "Role ID")
    ),
    responses(
        (status = 200, description = "Role retrieved successfully", body = ApiResponse<Role>),
        (status = 404, description = "Role not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_role(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<axum::response::Response> {
    let path = format!("{API_ROLES_PATH}/{id}");
    let role = crate::handle_service_result!(role_service.get_role_by_id(&id), path);

    Ok(ResponseHelper::entity_retrieved(
        API_ROLES_PATH,
        Some(&id.to_string()),
        role::SUCCESS_GET,
        role::MSG_RETRIEVED,
        role,
    )
    .into_response())
}

#[utoipa::path(
    get,
    path = "/api/roles",
    tag = "Roles",
    params(
        ("page" = Option<i64>, Query, description = "Page number (default: 1)"),
        ("limit" = Option<i64>, Query, description = "Items per page (default: 10)"),
        ("name" = Option<String>, Query, description = "Filter by role name")
    ),
    responses(
        (status = 200, description = "Roles with permissions retrieved successfully", body = ApiResponse<PaginatedRolesWithPermissions>),
        (status = 400, description = "Invalid pagination parameters")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_roles(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Query(pagination): Query<RolePaginationRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let paginated_result = role_service.get_roles_with_permissions(pagination).await?;

    Ok(ResponseHelper::entity_list(
        API_ROLES_PATH,
        role::SUCCESS_LIST,
        role::MSG_LISTED,
        paginated_result,
        None,
    ))
}

#[utoipa::path(
    put,
    path = "/api/roles/{id}",
    tag = "Roles",
    params(
        ("id" = String, Path, description = "Role ID")
    ),
    request_body = crate::modules::role::models::requests::UpdateRoleRequest,
    responses(
        (status = 200, description = "Role updated successfully", body = ApiResponse<Role>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Role not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_role(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateRoleRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let role = role_service.update_role(&id, request).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_ROLES_PATH,
        Some(&id.to_string()),
        role::SUCCESS_UPDATE,
        role::MSG_UPDATED,
        role,
    ))
}

#[utoipa::path(
    delete,
    path = "/api/roles/{id}",
    tag = "Roles",
    params(
        ("id" = String, Path, description = "Role ID")
    ),
    responses(
        (status = 200, description = "Role deleted successfully", body = ApiResponseJson),
        (status = 404, description = "Role not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_role(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    role_service.delete_role(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_ROLES_PATH,
        &id.to_string(),
        role::SUCCESS_DELETE,
        role::MSG_DELETED,
    ))
}

#[utoipa::path(
    get,
    path = "/api/roles/{id}/permissions",
    tag = "Roles",
    params(
        ("id" = String, Path, description = "Role ID")
    ),
    responses(
        (status = 200, description = "Permissions for role retrieved successfully", body = ApiResponse<Vec<Permission>>),
        (status = 404, description = "Role not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_role_permissions(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    let permissions = role_service.get_permissions_for_role(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        &format!("/api/roles/{id}/permissions"),
        None,
        role::SUCCESS_GET,
        "Permissions for role retrieved successfully",
        permissions,
    ))
}

#[utoipa::path(
    put,
    path = "/api/roles/{id}/permissions",
    tag = "Roles",
    params(
        ("id" = String, Path, description = "Role ID")
    ),
    request_body = ReplaceRolePermissionsRequest,
    responses(
        (status = 200, description = "Role permissions updated successfully", body = ApiResponse<Vec<Permission>>),
        (status = 400, description = "Invalid permission IDs"),
        (status = 404, description = "Role not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_role_permissions(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<ReplaceRolePermissionsRequest>,
) -> Result<impl axum::response::IntoResponse> {
    role_service
        .replace_permissions_for_role(&id, &request.permission_ids)
        .await?;

    // Fetch the updated list of permissions to return it
    let permissions = role_service.get_permissions_for_role(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        &format!("/api/roles/{id}/permissions"),
        None,
        role::SUCCESS_UPDATE,
        "Role permissions updated successfully",
        permissions,
    ))
}

#[utoipa::path(
    put,
    path = "/api/permissions/matrix",
    tag = "Permissions",
    request_body = PermissionMatrixUpdateRequest,
    responses(
        (status = 200, description = "Permission matrix updated successfully", body = ApiResponse<PermissionMatrixUpdateResponse>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Role or permission not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_permission_matrix(
    State(role_service): State<Arc<dyn RoleServiceTrait>>,
    Json(request): Json<PermissionMatrixUpdateRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let response = role_service.update_permission_matrix(request).await?;

    Ok(ResponseHelper::entity_retrieved(
        "/api/permissions/matrix",
        None,
        role::SUCCESS_UPDATE,
        "Permission matrix updated successfully",
        response,
    ))
}
