use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;

use super::domain::{Role, RoleWithPermissions};

// ===== PAGINATION MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct RolePaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
    pub name: Option<String>,
}

impl PaginationRequest for RolePaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}

// ===== PAGINATION RESPONSE MODELS =====
#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedRoles {
    pub roles: Vec<Role>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedRolesWithPermissions {
    pub roles: Vec<RoleWithPermissions>,
    pub meta: PaginationMeta,
}

// ===== PUBLIC/PRIVATE API VIEWS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users."
}))]
pub struct RolePublicView {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users.",
    "permissions": [
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "name": "posts:read",
            "description": "Can read posts",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "user_count": 5,
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct RolePrivateView {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permissions: Vec<super::domain::Permission>,
    pub user_count: i64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "roles": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "editor",
            "description": "Can edit content, but not manage users."
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedRolesPublicView {
    pub roles: Vec<RolePublicView>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "roles": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "editor",
            "description": "Can edit content, but not manage users.",
            "permissions": [
                {
                    "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                    "name": "posts:read",
                    "description": "Can read posts",
                    "created_at": "2025-07-05T01:31:59.286900Z",
                    "updated_at": "2025-07-05T01:31:59.286900Z"
                }
            ],
            "user_count": 5,
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedRolesPrivateView {
    pub roles: Vec<RolePrivateView>,
    pub meta: PaginationMeta,
}

// ===== CONVERSION IMPLEMENTATIONS =====
impl From<Role> for RolePublicView {
    fn from(role: Role) -> Self {
        Self {
            id: role.id,
            name: role.name,
            description: role.description,
        }
    }
}

impl From<RoleWithPermissions> for RolePrivateView {
    fn from(role: RoleWithPermissions) -> Self {
        Self {
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions,
            user_count: role.user_count,
            created_at: role.created_at,
            updated_at: role.updated_at,
        }
    }
}
