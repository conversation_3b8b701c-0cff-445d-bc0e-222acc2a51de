use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

// ===== DOMAIN MODELS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users.",
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Temporary struct for Permission until permission module is migrated
#[derive(Debu<PERSON>, <PERSON><PERSON>, Ser<PERSON>ize, Deserialize, ToSchema)]
pub struct Permission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users.",
    "permissions": [
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "name": "posts:read",
            "description": "Can read posts",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        },
        {
            "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
            "name": "posts:write",
            "description": "Can write posts",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "user_count": 5,
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct RoleWithPermissions {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permissions: Vec<Permission>,
    pub user_count: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== IMPLEMENTATIONS =====
impl Role {
    pub fn new(name: String, description: Option<String>) -> Self {
        Self {
            id: Uuid::new_v4(),
            name,
            description,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

impl RoleWithPermissions {
    pub fn from_role_and_permissions(
        role: Role,
        permissions: Vec<Permission>,
        user_count: i64,
    ) -> Self {
        Self {
            id: role.id,
            name: role.name,
            description: role.description,
            permissions,
            user_count,
            created_at: role.created_at,
            updated_at: role.updated_at,
        }
    }
}
