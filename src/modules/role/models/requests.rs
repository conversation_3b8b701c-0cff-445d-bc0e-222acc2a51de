use crate::utils::validation::{
    ValidateRequestEnhanced, ValidationResult, validate_required_string, validate_string_length,
};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct CreateRoleRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Role name must be between 2 and 50 characters"
    ))]
    pub name: String,

    #[validate(length(max = 200, message = "Description must not exceed 200 characters"))]
    pub description: Option<String>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateRoleRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Role name must be between 2 and 50 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 200, message = "Description must not exceed 200 characters"))]
    pub description: Option<Option<String>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct ReplaceRolePermissionsRequest {
    pub permission_ids: Vec<Uuid>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct PermissionMatrixUpdateRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "Changes must be between 1 and 100 items"
    ))]
    pub changes: Vec<PermissionMatrixChange>,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct PermissionMatrixChange {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted: bool,
}

// ===== VALIDATION IMPLEMENTATIONS =====
impl ValidateRequestEnhanced for CreateRoleRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate name
        validate_required_string(&self.name, "name", &mut errors);
        validate_string_length(&self.name, "name", 2, 50, &mut errors);

        // Validate description if present
        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 0, 200, &mut errors);
        }

        errors
    }
}

impl ValidateRequestEnhanced for UpdateRoleRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate name if present
        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut errors);
            validate_string_length(name, "name", 2, 50, &mut errors);
        }

        // Validate description if present
        if let Some(Some(desc_value)) = &self.description {
            validate_string_length(desc_value, "description", 0, 200, &mut errors);
        }

        errors
    }
}

impl ValidateRequestEnhanced for PermissionMatrixUpdateRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate changes array
        if self.changes.is_empty() {
            errors.add_error(
                "changes",
                "EMPTY_ARRAY",
                "At least one change must be specified",
            );
        } else if self.changes.len() > 100 {
            errors.add_error(
                "changes",
                "TOO_MANY_ITEMS",
                "Maximum 100 changes allowed per request",
            );
        }

        errors
    }
}
