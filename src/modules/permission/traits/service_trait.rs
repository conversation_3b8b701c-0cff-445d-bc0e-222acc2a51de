use crate::{
    errors::Result,
    modules::permission::models::{
        domain::Permission,
        pagination::{PaginatedPermissions, PermissionPaginationRequest},
        requests::{CreatePermissionRequest, UpdatePermissionRequest},
    },
};
use async_trait::async_trait;
use uuid::Uuid;

/// Permission query operations (ISP: Interface segregation)
#[async_trait]
pub trait PermissionQueryTrait: Send + Sync {
    async fn get_permission_by_id(&self, id: &Uuid) -> Result<Permission>;
    async fn get_permission_by_name(&self, name: &str) -> Result<Permission>;
    async fn get_permissions_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Permission>>;
    async fn get_permissions_by_names(&self, names: &[String]) -> Result<Vec<Permission>>;
    async fn get_permissions(
        &self,
        pagination: PermissionPaginationRequest,
    ) -> Result<PaginatedPermissions>;
    async fn permission_exists_by_name(&self, name: &str) -> Result<bool>;
}

/// Permission management operations (ISP: Interface segregation)
#[async_trait]
pub trait PermissionManagementTrait: Send + Sync {
    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission>;
    async fn create_permission_direct(&self, permission: Permission) -> Result<Permission>;
    async fn update_permission(
        &self,
        id: &Uuid,
        request: UpdatePermissionRequest,
    ) -> Result<Permission>;
    async fn delete_permission(&self, id: &Uuid) -> Result<()>;
}

/// Combined PermissionService trait (DIP: Depend on abstractions)
/// Uses trait inheritance to compose smaller interfaces
pub trait PermissionServiceTrait:
    PermissionQueryTrait + PermissionManagementTrait + Send + Sync
{
}
