use crate::errors::Result;
use crate::modules::permission::models::{
    domain::{Permission, UpdatePermission},
    pagination::PermissionPaginationRequest,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait PermissionRepositoryTrait: Send + Sync {
    async fn create(&self, permission: &Permission) -> Result<Permission>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Permission>>;
    async fn find_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Permission>>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Permission>>;
    async fn find_by_names(&self, names: &[String]) -> Result<Vec<Permission>>;
    async fn find_all(&self) -> Result<Vec<Permission>>;
    async fn find_all_with_pagination(
        &self,
        pagination: &PermissionPaginationRequest,
    ) -> Result<(Vec<Permission>, i64)>;
    async fn update(&self, id: &Uuid, update_data: UpdatePermission) -> Result<Option<Permission>>;
    async fn delete(&self, id: &Uuid) -> Result<bool>;
    async fn exists_by_name(&self, name: &str) -> Result<bool>;
    async fn exists_by_name_exclude_id(&self, name: &str, exclude_id: &Uuid) -> Result<bool>;
}

pub type DynPermissionRepo = Arc<dyn PermissionRepositoryTrait>;
