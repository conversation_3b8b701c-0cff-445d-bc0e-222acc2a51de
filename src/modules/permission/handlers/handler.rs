use crate::modules::permission::traits::service_trait::PermissionServiceTrait;
use crate::{
    constants::API_PERMISSIONS_PATH,
    errors::Result,
    modules::permission::models::{
        domain::Permission,
        pagination::{PaginatedPermissions, PermissionPaginationRequest},
        requests::{CreatePermissionRequest, UpdatePermissionRequest},
    },
    response::{ApiResponse, ApiResponseJson},
    utils::response_helpers::{ResponseHelper, response_constants::permission},
};
use axum::{
    J<PERSON>,
    extract::{Path, Query, State},
};
use std::sync::Arc;
use utoipa::OpenApi;
use uuid::Uuid;

#[derive(OpenApi)]
#[openapi(
    paths(create_permission, get_permission, get_permissions, update_permission, delete_permission),
    components(schemas(
        Permission,
        PaginatedPermissions,
        PermissionPaginationRequest,
        CreatePermissionRequest,
        UpdatePermissionRequest,
    )),
    tags((name = "Permissions", description = "Permission management endpoints"))
)]
pub struct PermissionApiDoc;

#[utoipa::path(
    post,
    path = "/api/permissions",
    tag = "Permissions",
    request_body = crate::modules::permission::models::requests::CreatePermissionRequest,
    responses(
        (status = 201, description = "Permission created successfully", body = ApiResponse<Permission>),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Permission already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_permission(
    State(permission_service): State<Arc<dyn PermissionServiceTrait>>,
    Json(request): Json<CreatePermissionRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let permission = permission_service.create_permission(request).await?;

    Ok(ResponseHelper::entity_created(
        API_PERMISSIONS_PATH,
        permission::SUCCESS_CREATE,
        permission::MSG_CREATED,
        permission,
    ))
}

#[utoipa::path(
    get,
    path = "/api/permissions/{id}",
    tag = "Permissions",
    params(
        ("id" = String, Path, description = "Permission ID")
    ),
    responses(
        (status = 200, description = "Permission retrieved successfully", body = ApiResponse<Permission>),
        (status = 404, description = "Permission not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_permission(
    State(permission_service): State<Arc<dyn PermissionServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    let permission = permission_service.get_permission_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PERMISSIONS_PATH,
        Some(&id.to_string()),
        permission::SUCCESS_GET,
        permission::MSG_RETRIEVED,
        permission,
    ))
}

#[utoipa::path(
    get,
    path = "/api/permissions",
    tag = "Permissions",
    params(
        ("page" = Option<i64>, Query, description = "Page number (default: 1)"),
        ("limit" = Option<i64>, Query, description = "Items per page (default: 10)")
    ),
    responses(
        (status = 200, description = "Permissions retrieved successfully", body = ApiResponse<PaginatedPermissions>),
        (status = 400, description = "Invalid pagination parameters")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_permissions(
    State(permission_service): State<Arc<dyn PermissionServiceTrait>>,
    Query(pagination): Query<PermissionPaginationRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let paginated_result = permission_service.get_permissions(pagination).await?;

    Ok(ResponseHelper::entity_list(
        API_PERMISSIONS_PATH,
        permission::SUCCESS_LIST,
        permission::MSG_LISTED,
        paginated_result,
        None,
    ))
}

#[utoipa::path(
    put,
    path = "/api/permissions/{id}",
    tag = "Permissions",
    params(
        ("id" = String, Path, description = "Permission ID")
    ),
    request_body = crate::modules::permission::models::requests::UpdatePermissionRequest,
    responses(
        (status = 200, description = "Permission updated successfully", body = ApiResponse<Permission>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Permission not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_permission(
    State(permission_service): State<Arc<dyn PermissionServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdatePermissionRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let permission = permission_service.update_permission(&id, request).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PERMISSIONS_PATH,
        Some(&id.to_string()),
        permission::SUCCESS_UPDATE,
        permission::MSG_UPDATED,
        permission,
    ))
}

#[utoipa::path(
    delete,
    path = "/api/permissions/{id}",
    tag = "Permissions",
    params(
        ("id" = String, Path, description = "Permission ID")
    ),
    responses(
        (status = 200, description = "Permission deleted successfully", body = ApiResponseJson),
        (status = 404, description = "Permission not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_permission(
    State(permission_service): State<Arc<dyn PermissionServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    permission_service.delete_permission(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_PERMISSIONS_PATH,
        &id.to_string(),
        permission::SUCCESS_DELETE,
        permission::MSG_DELETED,
    ))
}
