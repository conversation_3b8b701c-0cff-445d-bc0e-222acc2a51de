use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::modules::permission::models::domain::PermissionType;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxPermission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxRolePermission {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted_by: Uuid,
    pub granted_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxPermission> for crate::modules::permission::models::domain::Permission {
    fn from(permission: SqlxPermission) -> Self {
        Self {
            id: permission.id,
            name: permission.name,
            description: permission.description,
            created_at: permission.created_at,
            updated_at: permission.updated_at,
        }
    }
}

impl From<SqlxPermission> for crate::modules::permission::models::domain::PermissionWithTracking {
    fn from(permission: SqlxPermission) -> Self {
        Self {
            id: permission.id,
            name: permission.name,
            description: permission.description,
            permission_type: PermissionType::User,
            resource: None,
            action: "read".to_string(),
            is_active: true,
            created_by: Some(Uuid::new_v4()),
            updated_by: None,
            created_at: permission.created_at,
            updated_at: permission.updated_at,
        }
    }
}

impl From<SqlxRolePermission> for crate::modules::permission::models::domain::RolePermission {
    fn from(role_perm: SqlxRolePermission) -> Self {
        Self {
            role_id: role_perm.role_id,
            permission_id: role_perm.permission_id,
            granted_by: role_perm.granted_by,
            granted_at: role_perm.granted_at,
        }
    }
}

// ===== CREATE PARAMS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePermissionParams {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateRolePermissionParams {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted_by: Uuid,
    pub granted_at: DateTime<Utc>,
}

impl From<crate::modules::permission::models::domain::NewPermission> for CreatePermissionParams {
    fn from(new_permission: crate::modules::permission::models::domain::NewPermission) -> Self {
        Self {
            id: new_permission.id,
            name: new_permission.name,
            description: new_permission.description,
            created_at: new_permission.created_at,
            updated_at: new_permission.updated_at,
        }
    }
}

impl From<crate::modules::permission::models::domain::NewRolePermission>
    for CreateRolePermissionParams
{
    fn from(new_role_perm: crate::modules::permission::models::domain::NewRolePermission) -> Self {
        Self {
            role_id: new_role_perm.role_id,
            permission_id: new_role_perm.permission_id,
            granted_by: new_role_perm.granted_by,
            granted_at: new_role_perm.granted_at,
        }
    }
}
