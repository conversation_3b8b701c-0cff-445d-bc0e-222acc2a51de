use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::permission::database::models::SqlxPermission,
    modules::permission::models::{
        domain::{Permission, UpdatePermission},
        pagination::PermissionPaginationRequest,
    },
    repository::base_repository::AsyncRepository,
    utils::pagination::PaginationRequest,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

use crate::modules::permission::traits::repository_trait::PermissionRepositoryTrait;

// ===== SQLX PERMISSION REPOSITORY =====

#[derive(Clone)]
pub struct SqlxPermissionRepository {
    base: AsyncRepository,
}

impl SqlxPermissionRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl PermissionRepositoryTrait for SqlxPermissionRepository {
    async fn create(&self, permission: &Permission) -> Result<Permission> {
        let id = permission.id;
        let name = permission.name.clone();
        let description = permission.description.clone();
        let created_at = permission.created_at;
        let updated_at = permission.updated_at;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let permission = sqlx::query_as::<_, SqlxPermission>(
                        "INSERT INTO permissions (id, name, description, created_at, updated_at) 
                         VALUES ($1, $2, $3, $4, $5) 
                         RETURNING *",
                    )
                    .bind(id)
                    .bind(&name)
                    .bind(description.as_ref())
                    .bind(created_at)
                    .bind(updated_at)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Permission::from(permission))
                })
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Permission>> {
        let id = *id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permission = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions WHERE id = $1",
                    )
                    .bind(id)
                    .fetch_optional(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permission.map(Permission::from))
                })
            })
            .await
    }

    async fn find_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Permission>> {
        if ids.is_empty() {
            return Ok(vec![]);
        }

        let ids = ids.to_vec();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permissions = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions WHERE id = ANY($1)",
                    )
                    .bind(&ids)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permissions.into_iter().map(Permission::from).collect())
                })
            })
            .await
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Permission>> {
        let name = name.to_string();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permission = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions WHERE name = $1",
                    )
                    .bind(&name)
                    .fetch_optional(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permission.map(Permission::from))
                })
            })
            .await
    }

    async fn find_by_names(&self, names: &[String]) -> Result<Vec<Permission>> {
        if names.is_empty() {
            return Ok(vec![]);
        }

        let names = names.to_vec();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permissions = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions WHERE name = ANY($1)",
                    )
                    .bind(&names)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permissions.into_iter().map(Permission::from).collect())
                })
            })
            .await
    }

    async fn find_all(&self) -> Result<Vec<Permission>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permissions = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions ORDER BY name ASC",
                    )
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permissions.into_iter().map(Permission::from).collect())
                })
            })
            .await
    }

    async fn find_all_with_pagination(
        &self,
        pagination: &PermissionPaginationRequest,
    ) -> Result<(Vec<Permission>, i64)> {
        let page = pagination.page();
        let limit = pagination.limit();
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let offset = (page - 1) * limit;

                    // Get permissions with pagination
                    let permissions = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT * FROM permissions ORDER BY name ASC LIMIT $1 OFFSET $2",
                    )
                    .bind(limit)
                    .bind(offset)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    // Get total count
                    let total_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM permissions")
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    let permissions: Vec<Permission> =
                        permissions.into_iter().map(Permission::from).collect();

                    Ok((permissions, total_count))
                })
            })
            .await
    }

    async fn update(&self, id: &Uuid, update_data: UpdatePermission) -> Result<Option<Permission>> {
        let id = *id;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let permission = sqlx::query_as::<_, SqlxPermission>(
                        "UPDATE permissions 
                         SET name = COALESCE($2, name), 
                             description = CASE WHEN $3::boolean THEN $4 ELSE description END,
                             updated_at = $5
                         WHERE id = $1 
                         RETURNING *",
                    )
                    .bind(id)
                    .bind(update_data.name.as_ref())
                    .bind(update_data.description.is_some())
                    .bind(update_data.description.flatten().as_ref())
                    .bind(update_data.updated_at)
                    .fetch_optional(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permission.map(Permission::from))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<bool> {
        let id = *id;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let rows_affected = sqlx::query("DELETE FROM permissions WHERE id = $1")
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?
                        .rows_affected();

                    Ok(rows_affected > 0)
                })
            })
            .await
    }

    async fn exists_by_name(&self, name: &str) -> Result<bool> {
        let name = name.to_string();

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let exists: bool = sqlx::query_scalar(
                        "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1)",
                    )
                    .bind(&name)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(exists)
                })
            })
            .await
    }

    async fn exists_by_name_exclude_id(&self, name: &str, exclude_id: &Uuid) -> Result<bool> {
        let name = name.to_string();
        let exclude_id = *exclude_id;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let exists: bool = sqlx::query_scalar(
                        "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1 AND id != $2)",
                    )
                    .bind(&name)
                    .bind(exclude_id)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(exists)
                })
            })
            .await
    }
}

// ===== ROLE PERMISSION REPOSITORY =====

#[async_trait]
pub trait RolePermissionRepositoryTrait: Send + Sync {
    async fn assign_permission_to_role(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
        granted_by: &Uuid,
    ) -> Result<()>;
    async fn remove_permission_from_role(&self, role_id: &Uuid, permission_id: &Uuid)
    -> Result<()>;
    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<Permission>>;
    async fn get_permission_roles(&self, permission_id: &Uuid) -> Result<Vec<Uuid>>;
    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool>;
}

pub type DynRolePermissionRepo = Arc<dyn RolePermissionRepositoryTrait>;

#[derive(Clone)]
pub struct SqlxRolePermissionRepository {
    base: AsyncRepository,
}

impl SqlxRolePermissionRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl RolePermissionRepositoryTrait for SqlxRolePermissionRepository {
    async fn assign_permission_to_role(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
        granted_by: &Uuid,
    ) -> Result<()> {
        let role_id = *role_id;
        let permission_id = *permission_id;
        let granted_by = *granted_by;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    sqlx::query(
                        "INSERT INTO role_permissions (role_id, permission_id, granted_by, granted_at) 
                         VALUES ($1, $2, $3, NOW()) 
                         ON CONFLICT (role_id, permission_id) DO NOTHING"
                    )
                    .bind(role_id)
                    .bind(permission_id)
                    .bind(granted_by)
                    .execute(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }

    async fn remove_permission_from_role(
        &self,
        role_id: &Uuid,
        permission_id: &Uuid,
    ) -> Result<()> {
        let role_id = *role_id;
        let permission_id = *permission_id;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    sqlx::query(
                        "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
                    )
                    .bind(role_id)
                    .bind(permission_id)
                    .execute(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }

    async fn get_role_permissions(&self, role_id: &Uuid) -> Result<Vec<Permission>> {
        let role_id = *role_id;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let permissions = sqlx::query_as::<_, SqlxPermission>(
                        "SELECT p.* FROM permissions p 
                         INNER JOIN role_permissions rp ON p.id = rp.permission_id 
                         WHERE rp.role_id = $1 
                         ORDER BY p.name ASC",
                    )
                    .bind(role_id)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(permissions.into_iter().map(Permission::from).collect())
                })
            })
            .await
    }

    async fn get_permission_roles(&self, permission_id: &Uuid) -> Result<Vec<Uuid>> {
        let permission_id = *permission_id;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let role_ids: Vec<Uuid> = sqlx::query_scalar(
                        "SELECT role_id FROM role_permissions WHERE permission_id = $1",
                    )
                    .bind(permission_id)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(role_ids)
                })
            })
            .await
    }

    async fn role_has_permission(&self, role_id: &Uuid, permission_id: &Uuid) -> Result<bool> {
        let role_id = *role_id;
        let permission_id = *permission_id;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let exists: bool = sqlx::query_scalar(
                        "SELECT EXISTS(SELECT 1 FROM role_permissions WHERE role_id = $1 AND permission_id = $2)"
                    )
                    .bind(role_id)
                    .bind(permission_id)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(exists)
                })
            })
            .await
    }
}
