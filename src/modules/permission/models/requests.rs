use crate::utils::validation::{
    ValidateRequestEnhanced, ValidationResult, validate_required_string, validate_string_length,
};
use serde::Deserialize;
use utoipa::ToSchema;
use validator::Validate;

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct CreatePermissionRequest {
    #[validate(length(
        min = 2,
        max = 100,
        message = "Permission name must be between 2 and 100 characters"
    ))]
    pub name: String,
    #[validate(length(max = 255, message = "Description must not exceed 255 characters"))]
    pub description: Option<String>,
}

impl ValidateRequestEnhanced for CreatePermissionRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        validate_required_string(&self.name, "name", &mut errors);
        if !self.name.trim().is_empty() {
            validate_string_length(&self.name, "name", 2, 100, &mut errors);
        }

        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 0, 255, &mut errors);
        }

        errors
    }
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdatePermissionRequest {
    #[validate(length(
        min = 2,
        max = 100,
        message = "Permission name must be between 2 and 100 characters"
    ))]
    pub name: Option<String>,
    #[validate(length(max = 255, message = "Description must not exceed 255 characters"))]
    pub description: Option<Option<String>>,
}

impl ValidateRequestEnhanced for UpdatePermissionRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut errors);
            if !name.trim().is_empty() {
                validate_string_length(name, "name", 2, 100, &mut errors);
            }
        }

        if let Some(Some(ref desc)) = self.description {
            validate_string_length(desc, "description", 0, 255, &mut errors);
        }

        errors
    }
}
