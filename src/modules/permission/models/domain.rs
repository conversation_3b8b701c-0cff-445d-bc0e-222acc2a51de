use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::{ToSchema, schema};
use uuid::Uuid;

// ===== ENUMS =====
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, ToSchema)]
pub enum PermissionType {
    #[serde(rename = "system")]
    System,
    #[serde(rename = "user")]
    User,
    #[serde(rename = "content")]
    Content,
    #[serde(rename = "admin")]
    Admin,
}

impl std::fmt::Display for PermissionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PermissionType::System => write!(f, "system"),
            PermissionType::User => write!(f, "user"),
            PermissionType::Content => write!(f, "content"),
            PermissionType::Admin => write!(f, "admin"),
        }
    }
}

impl std::str::FromStr for PermissionType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "system" => Ok(PermissionType::System),
            "user" => Ok(PermissionType::User),
            "content" => Ok(PermissionType::Content),
            "admin" => Ok(PermissionType::Admin),
            _ => Err(format!("Invalid permission type: {s}")),
        }
    }
}

// ===== DOMAIN MODELS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "name": "users:read",
    "description": "Allows reading user information",
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct Permission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== ADDITIONAL MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionWithTracking {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permission_type: PermissionType,
    pub resource: Option<String>,
    pub action: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewPermission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdatePermission {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RolePermission {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted_by: Uuid,
    pub granted_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewRolePermission {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted_by: Uuid,
    pub granted_at: DateTime<Utc>,
}

// ===== IMPLEMENTATION =====
impl Permission {
    pub fn new(name: String, description: Option<String>) -> Self {
        Self {
            id: Uuid::new_v4(),
            name,
            description,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

// ===== FROM TRAIT IMPLEMENTATIONS =====
impl From<PermissionWithTracking> for Permission {
    fn from(permission_with_tracking: PermissionWithTracking) -> Self {
        Self {
            id: permission_with_tracking.id,
            name: permission_with_tracking.name,
            description: permission_with_tracking.description,
            created_at: permission_with_tracking.created_at,
            updated_at: permission_with_tracking.updated_at,
        }
    }
}
