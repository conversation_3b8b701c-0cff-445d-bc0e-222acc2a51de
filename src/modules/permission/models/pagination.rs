use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;

use super::domain::Permission;

// ===== PAGINATION MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct PermissionPaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
}

impl PaginationRequest for PermissionPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "permissions": [
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "name": "users:read",
            "description": "Allows reading user information",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        },
        {
            "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
            "name": "users:write",
            "description": "Allows creating and updating user information",
            "created_at": "2025-07-05T01:32:59.286900Z",
            "updated_at": "2025-07-05T01:32:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 2,
        "total_pages": 1
    }
}))]
pub struct PaginatedPermissions {
    pub permissions: Vec<Permission>,
    pub meta: PaginationMeta,
}
