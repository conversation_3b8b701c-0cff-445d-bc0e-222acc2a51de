use crate::{
    errors::Result,
    modules::progression::exp_system::models::{
        Leaderboard, LeaderboardEntry, LeaderboardRequest, UserExperienceHistory, UserLevel,
    },
};
use async_trait::async_trait;
use sqlx::PgPool;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait ExpSystemRepository: Send + Sync {
    async fn create_user_level(&self, user_id: Uuid) -> Result<UserLevel>;
    async fn get_user_level(&self, user_id: &Uuid) -> Result<Option<UserLevel>>;
    async fn update_user_level(&self, user_level: &UserLevel) -> Result<UserLevel>;
    async fn get_experience_history(&self, user_id: &Uuid) -> Result<Vec<UserExperienceHistory>>;
    async fn add_experience(
        &self,
        user_id: &Uuid,
        experience_to_add: i64,
        source: Option<String>,
        metadata: Option<serde_json::Value>,
    ) -> Result<(UserLevel, UserExperienceHistory)>;

    // Leaderboard methods
    async fn get_leaderboard(&self, request: LeaderboardRequest) -> Result<Leaderboard>;
    async fn get_user_rank(&self, user_id: &Uuid) -> Result<i32>;
    async fn set_current_title(&self, user_id: &Uuid, title_id: Uuid) -> Result<UserLevel>;
}

pub type DynExpSystemRepo = Arc<dyn ExpSystemRepository>;

#[derive(Clone)]
pub struct SqlxExpSystemRepository {
    pool: PgPool,
}

impl SqlxExpSystemRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ExpSystemRepository for SqlxExpSystemRepository {
    async fn create_user_level(&self, user_id: Uuid) -> Result<UserLevel> {
        let user_level = sqlx::query_as!(
            UserLevel,
            r#"
            INSERT INTO user_levels (user_id, level, experience, total_experience)
            VALUES ($1, 1, 0, 0)
            RETURNING id, user_id, level, experience, total_experience, created_at, updated_at, current_title_id
            "#,
            user_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user_level)
    }

    async fn get_user_level(&self, user_id: &Uuid) -> Result<Option<UserLevel>> {
        let user_level = sqlx::query_as!(
            UserLevel,
            r#"
            SELECT id, user_id, level, experience, total_experience, created_at, updated_at, current_title_id
            FROM user_levels
            WHERE user_id = $1
            "#,
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user_level)
    }

    async fn update_user_level(&self, user_level: &UserLevel) -> Result<UserLevel> {
        let updated_level = sqlx::query_as!(
            UserLevel,
            r#"
            UPDATE user_levels
            SET level = $2, experience = $3, current_title_id = $4, updated_at = NOW()
            WHERE id = $1
            RETURNING id, user_id, level, experience, total_experience, created_at, updated_at, current_title_id
            "#,
            user_level.id,
            user_level.level,
            user_level.experience,
            user_level.current_title_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(updated_level)
    }

    async fn get_experience_history(&self, user_id: &Uuid) -> Result<Vec<UserExperienceHistory>> {
        let history = sqlx::query_as!(
            UserExperienceHistory,
            r#"
            SELECT id, user_id, exp_gained, source, source_id, description, created_at
            FROM user_experience_history
            WHERE user_id = $1
            ORDER BY created_at DESC
            "#,
            user_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(history)
    }

    async fn add_experience(
        &self,
        user_id: &Uuid,
        experience_to_add: i64,
        source: Option<String>,
        metadata: Option<serde_json::Value>,
    ) -> Result<(UserLevel, UserExperienceHistory)> {
        let mut tx = self.pool.begin().await?;

        // 1. Add experience and update total experience for the user
        let updated_level = sqlx::query_as!(
            UserLevel,
            r#"
            UPDATE user_levels
            SET experience = experience + $2, total_experience = total_experience + $2, updated_at = NOW()
            WHERE user_id = $1
            RETURNING id, user_id, level, experience, total_experience, created_at, updated_at, current_title_id
            "#,
            user_id,
            experience_to_add
        )
        .fetch_one(&mut *tx)
        .await?;

        // 2. Log the experience change in the history table
        let source_id = metadata
            .as_ref()
            .and_then(|m| m.get("source_id").and_then(|v| v.as_str()))
            .map(|s| s.to_string())
            .and_then(|s| s.parse::<Uuid>().ok());
        let description = metadata
            .as_ref()
            .and_then(|m| m.get("description").and_then(|v| v.as_str()))
            .map(|s| s.to_string());

        let history_entry = sqlx::query_as!(
            UserExperienceHistory,
            r#"
            INSERT INTO user_experience_history (user_id, exp_gained, source, source_id, description)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, user_id, exp_gained, source, source_id, description, created_at
            "#,
            user_id,
            experience_to_add as i32,
            source.as_deref(),
            source_id,
            description
        )
        .fetch_one(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok((updated_level, history_entry))
    }

    async fn get_leaderboard(&self, request: LeaderboardRequest) -> Result<Leaderboard> {
        // Get leaderboard entries
        let entries = sqlx::query!(
            r#"
            SELECT ul.user_id, u.username, u.fullname, ul.level, ul.total_experience
            FROM user_levels ul
            INNER JOIN users u ON ul.user_id = u.id
            ORDER BY ul.total_experience DESC
            LIMIT $1
            "#,
            request.limit as i64
        )
        .fetch_all(&self.pool)
        .await?;

        // Get total users count
        let total_users = sqlx::query!("SELECT COUNT(*) as count FROM user_levels")
            .fetch_one(&self.pool)
            .await?
            .count;

        let leaderboard_entries: Vec<LeaderboardEntry> = entries
            .into_iter()
            .enumerate()
            .map(|(index, row)| LeaderboardEntry {
                user_id: row.user_id,
                username: row.username,
                fullname: row.fullname,
                level: row.level,
                total_experience: row.total_experience,
                rank: (index + 1) as i32,
            })
            .collect();

        Ok(Leaderboard {
            entries: leaderboard_entries,
            total_users: total_users.unwrap_or(0),
        })
    }

    async fn get_user_rank(&self, user_id: &Uuid) -> Result<i32> {
        let user_experience = sqlx::query!(
            "SELECT total_experience FROM user_levels WHERE user_id = $1",
            user_id
        )
        .fetch_one(&self.pool)
        .await?
        .total_experience;

        let rank = sqlx::query!(
            "SELECT COUNT(*) as count FROM user_levels WHERE total_experience > $1",
            user_experience
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        Ok((rank.unwrap_or(0) + 1) as i32)
    }

    async fn set_current_title(&self, user_id: &Uuid, title_id: Uuid) -> Result<UserLevel> {
        let user_level = sqlx::query_as!(
            UserLevel,
            r#"
            UPDATE user_levels
            SET current_title_id = $2, updated_at = NOW()
            WHERE user_id = $1
            RETURNING id, user_id, level, experience, total_experience, created_at, updated_at, current_title_id
            "#,
            user_id,
            title_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user_level)
    }
}
