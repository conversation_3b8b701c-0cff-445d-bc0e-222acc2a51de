use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct SqlxUserExperience {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub level: i32,
    pub experience_to_next_level: i64,
    pub total_experience_earned: i64,
    pub last_activity_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct SqlxExperienceTransaction {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub transaction_type: super::models::ExperienceTransactionType,
    pub source: String,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct SqlxLevelThreshold {
    pub id: Uuid,
    pub level: i32,
    pub experience_required: i64,
    pub cumulative_experience: i64,
    pub title: Option<String>,
    pub description: Option<String>,
    pub rewards: Option<serde_json::Value>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxUserExperience> for super::models::UserExperience {
    fn from(exp: SqlxUserExperience) -> Self {
        Self {
            id: exp.id,
            user_id: exp.user_id,
            experience_points: exp.experience_points,
            level: exp.level,
            experience_to_next_level: exp.experience_to_next_level,
            total_experience_earned: exp.total_experience_earned,
            last_activity_at: exp.last_activity_at,
            created_at: exp.created_at,
            updated_at: exp.updated_at,
        }
    }
}

impl From<SqlxExperienceTransaction> for super::models::ExperienceTransaction {
    fn from(tx: SqlxExperienceTransaction) -> Self {
        Self {
            id: tx.id,
            user_id: tx.user_id,
            experience_points: tx.experience_points,
            transaction_type: tx.transaction_type,
            source: tx.source,
            description: tx.description,
            metadata: tx.metadata,
            created_at: tx.created_at,
        }
    }
}

impl From<SqlxLevelThreshold> for super::models::LevelThreshold {
    fn from(threshold: SqlxLevelThreshold) -> Self {
        Self {
            id: threshold.id,
            level: threshold.level,
            experience_required: threshold.experience_required,
            cumulative_experience: threshold.cumulative_experience,
            title: threshold.title,
            description: threshold.description,
            rewards: threshold.rewards,
            is_active: threshold.is_active,
            created_at: threshold.created_at,
            updated_at: threshold.updated_at,
        }
    }
}

// ===== CREATE PARAMS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserExperienceParams {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub level: i32,
    pub experience_to_next_level: i64,
    pub total_experience_earned: i64,
    pub last_activity_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateExperienceTransactionParams {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub transaction_type: super::models::ExperienceTransactionType,
    pub source: String,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateLevelThresholdParams {
    pub id: Uuid,
    pub level: i32,
    pub experience_required: i64,
    pub cumulative_experience: i64,
    pub title: Option<String>,
    pub description: Option<String>,
    pub rewards: Option<serde_json::Value>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl From<super::models::NewUserExperience> for CreateUserExperienceParams {
    fn from(new_exp: super::models::NewUserExperience) -> Self {
        Self {
            id: new_exp.id,
            user_id: new_exp.user_id,
            experience_points: new_exp.experience_points,
            level: new_exp.level,
            experience_to_next_level: new_exp.experience_to_next_level,
            total_experience_earned: new_exp.total_experience_earned,
            last_activity_at: new_exp.last_activity_at,
            created_at: new_exp.created_at,
            updated_at: new_exp.updated_at,
        }
    }
}

impl From<super::models::NewExperienceTransaction> for CreateExperienceTransactionParams {
    fn from(new_tx: super::models::NewExperienceTransaction) -> Self {
        Self {
            id: new_tx.id,
            user_id: new_tx.user_id,
            experience_points: new_tx.experience_points,
            transaction_type: new_tx.transaction_type,
            source: new_tx.source,
            description: new_tx.description,
            metadata: new_tx.metadata,
            created_at: new_tx.created_at,
        }
    }
}

impl From<super::models::NewLevelThreshold> for CreateLevelThresholdParams {
    fn from(new_threshold: super::models::NewLevelThreshold) -> Self {
        Self {
            id: new_threshold.id,
            level: new_threshold.level,
            experience_required: new_threshold.experience_required,
            cumulative_experience: new_threshold.cumulative_experience,
            title: new_threshold.title,
            description: new_threshold.description,
            rewards: new_threshold.rewards,
            is_active: new_threshold.is_active,
            created_at: new_threshold.created_at,
            updated_at: new_threshold.updated_at,
        }
    }
}
