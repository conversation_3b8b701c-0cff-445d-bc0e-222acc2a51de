// EXP System API Handlers

use crate::{
    errors::Result,
    modules::progression::exp_system::models::{Leaderboard, LeaderboardRequest},
    modules::progression::exp_system::service::{
        AddExperienceRequest, DynExpSystemService, LevelUpResponse,
    },
    response::{ApiResponse, success_response},
    routes::middleware::{ApiType, auth::AuthenticatedUser},
    utils::response_helpers::ResponseHelper,
};
use axum::{
    Extension, Json,
    extract::{Path, Query, State},
    response::IntoResponse,
};
use uuid::Uuid;

#[utoipa::path(
    post,
    path = "/api/users/{id}/experience",
    tag = "Progression",
    params(
        ("id" = Uuid, Path, description = "User ID")
    ),
    request_body = AddExperienceRequest,
    responses(
        (status = 200, description = "Experience added successfully", body = ApiResponse<LevelUpResponse>),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn add_experience(
    State(exp_service): State<DynExpSystemService>,
    Path(id): Path<Uuid>,
    Json(request): Json<AddExperienceRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let result = exp_service.add_experience(&id, request).await?;
    let path = format!("/api/users/{id}/level");

    Ok(success_response(
        path,
        "EXPERIENCE_ADDED",
        "User experience updated successfully.",
        result,
    ))
}

#[utoipa::path(
    get,
    path = "/api/users/{id}/level",
    tag = "Progression",
    params(
        ("id" = Uuid, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User level retrieved successfully", body = ApiResponse<crate::modules::progression::exp_system::models::UserLevel>),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_user_level(
    State(exp_service): State<DynExpSystemService>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    let user_level = exp_service.get_user_level(&id).await?;
    let path = format!("/api/users/{id}/level");

    Ok(ResponseHelper::entity_retrieved(
        &path,
        None,
        "USER_LEVEL_RETRIEVED",
        "User level information retrieved successfully.",
        user_level,
    ))
}

// ===== SHARED HANDLERS (Support both Public and Private API) =====

/// Get leaderboard
/// - Public API (no header or X-API-Type: public): Returns public leaderboard
/// - Private API (X-API-Type: private + auth): Returns same leaderboard (no difference for now)
#[utoipa::path(
    get,
    path = "/api/leaderboard",
    tag = "Progression",
    params(
        ("limit" = Option<i32>, Query, description = "Number of entries to return (default: 10)")
    ),
    responses(
        (status = 200, description = "Leaderboard retrieved successfully", body = ApiResponse<Leaderboard>)
    )
)]
pub async fn get_leaderboard(
    State(exp_service): State<DynExpSystemService>,
    Query(request): Query<LeaderboardRequest>,
) -> Result<impl IntoResponse> {
    let leaderboard = exp_service.get_leaderboard(request).await?;

    Ok(ResponseHelper::entity_retrieved(
        "/api/leaderboard",
        None,
        "LEADERBOARD_RETRIEVED",
        "Leaderboard retrieved successfully.",
        leaderboard,
    ))
}

/// Get user level
/// - Public API (no header or X-API-Type: public): Returns user level with limited fields
/// - Private API (X-API-Type: private + auth): Returns user level with detailed fields
#[utoipa::path(
    get,
    path = "/api/users/{id}/level",
    tag = "Progression",
    params(
        ("id" = Uuid, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User level retrieved successfully"),
        (status = 404, description = "User not found")
    )
)]
pub async fn get_user_level_shared(
    Extension(api_type): Extension<ApiType>,
    State(exp_service): State<DynExpSystemService>,
    Path(id): Path<Uuid>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<axum::response::Response> {
    match api_type {
        ApiType::Public => {
            // Public API: return limited user level information
            let user_level = exp_service.get_user_level_public(&id).await?;
            let path = format!("/api/users/{id}/level");

            Ok(ResponseHelper::entity_retrieved(
                &path,
                None,
                "USER_LEVEL_RETRIEVED",
                "User level retrieved successfully.",
                user_level,
            )
            .into_response())
        }
        ApiType::Private => {
            // Private API: requires authentication and permission
            let authenticated_user = user.ok_or_else(|| {
                crate::errors::AppError::Unauthorized(
                    "Authentication required for private API access".into(),
                )
            })?;

            // Check for progression:read permission
            let has_permission = authenticated_user
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == "progression:read");

            if !has_permission {
                return Err(crate::errors::AppError::Forbidden(
                    "Insufficient permissions".into(),
                ));
            }

            // Return detailed user level information
            let user_level = exp_service.get_user_level_private(&id).await?;
            let path = format!("/api/users/{id}/level");

            Ok(ResponseHelper::entity_retrieved(
                &path,
                None,
                "USER_LEVEL_RETRIEVED",
                "User level retrieved successfully.",
                user_level,
            )
            .into_response())
        }
    }
}
