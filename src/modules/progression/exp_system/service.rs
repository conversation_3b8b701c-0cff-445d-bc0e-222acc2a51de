use crate::{
    errors::Result,
    modules::{
        progression::{
            exp_system::{
                models::{
                    Leaderboard, LeaderboardRequest, UserExperienceHistory, UserLevel,
                    UserLevelPrivateView, UserLevelPublicView,
                },
                repository::DynExpSystemRepo,
            },
            title_system::service::DynTitleService,
        },
        redis::RedisServiceTrait,
        user::repository::DynUserRepo,
    },
    utils::{
        ErrorHelper,
        cache_helpers::{CacheKeyGenerator, CacheOperations, CacheTTL},
    },
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{sync::Arc, time::Duration};
use utoipa::ToSchema;
use uuid::Uuid;

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct AddExperienceRequest {
    pub experience: i64,
    pub source: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct LevelUpResponse {
    pub user_id: Uuid,
    pub new_level: i32,
    pub leveled_up: bool,
    pub experience_details: UserLevel,
}

#[async_trait]
pub trait ExpSystemServiceTrait: Send + Sync {
    async fn add_experience(
        &self,
        user_id: &Uuid,
        request: AddExperienceRequest,
    ) -> Result<LevelUpResponse>;
    async fn get_user_level(&self, user_id: &Uuid) -> Result<UserLevel>;
    async fn get_user_experience_history(
        &self,
        user_id: &Uuid,
    ) -> Result<Vec<UserExperienceHistory>>;
    // Called internally when a new user is created
    async fn on_new_user_created(&self, user_id: &Uuid) -> Result<()>;
    // Manually set a user's current title
    async fn set_current_title(&self, user_id: &Uuid, title_id: Uuid) -> Result<UserLevel>;

    // Public leaderboard methods
    async fn get_leaderboard(&self, request: LeaderboardRequest) -> Result<Leaderboard>;
    async fn get_user_level_public(&self, user_id: &Uuid) -> Result<UserLevelPublicView>;
    async fn get_user_level_private(&self, user_id: &Uuid) -> Result<UserLevelPrivateView>;
}

pub type DynExpSystemService = Arc<dyn ExpSystemServiceTrait>;

#[derive(Clone)]
pub struct ExpSystemService {
    repo: DynExpSystemRepo,
    user_repo: DynUserRepo,
    title_service: DynTitleService,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
}

impl ExpSystemService {
    pub fn new(
        repo: DynExpSystemRepo,
        user_repo: DynUserRepo,
        title_service: DynTitleService,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        Self {
            repo,
            user_repo,
            title_service,
            redis_service,
        }
    }

    // "Tu tiên" leveling formula: experience_needed = 100 * level^1.5
    fn experience_for_next_level(level: i32) -> i64 {
        (100.0 * (level as f64).powf(1.5)) as i64
    }

    /// Generate cache key for user level
    fn user_level_cache_key(user_id: &Uuid) -> String {
        CacheKeyGenerator::user_level(user_id)
    }

    /// Generate cache key for leaderboard
    fn leaderboard_cache_key(request: &LeaderboardRequest) -> String {
        CacheKeyGenerator::leaderboard(request.limit)
    }

    /// Cache TTL for user level (30 minutes)
    fn user_level_ttl() -> Duration {
        CacheTTL::user_level()
    }

    /// Cache TTL for leaderboard (15 minutes)
    fn leaderboard_ttl() -> Duration {
        CacheTTL::leaderboard()
    }

    /// Invalidate user level cache
    async fn invalidate_user_level_cache(&self, user_id: &Uuid) -> Result<()> {
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::user_level_cache_key(user_id);
            if let Err(e) = redis.delete(&cache_key).await {
                tracing::warn!(
                    user_id = %user_id,
                    cache_key = %cache_key,
                    error = %e,
                    "Failed to invalidate user level cache"
                );
            } else {
                tracing::debug!(
                    user_id = %user_id,
                    "Successfully invalidated user level cache"
                );
            }

            // Also invalidate leaderboard cache since user level changed
            // Delete all leaderboard cache keys (simple approach)
            if let Err(e) = redis.delete("leaderboard:*").await {
                tracing::warn!(
                    error = %e,
                    "Failed to invalidate leaderboard cache"
                );
            }
        }
        Ok(())
    }
}

#[async_trait]
impl ExpSystemServiceTrait for ExpSystemService {
    async fn on_new_user_created(&self, user_id: &Uuid) -> Result<()> {
        // Check if level record already exists to prevent duplicates
        if self.repo.get_user_level(user_id).await?.is_none() {
            let mut user_level = self.repo.create_user_level(*user_id).await?;

            // Unlock level 1 titles and set the first one as current if none is set
            let unlocked_titles = self
                .title_service
                .check_and_unlock_titles_for_level(*user_id, 1)
                .await?;

            if user_level.current_title_id.is_none() {
                if let Some(first_title) = unlocked_titles.first() {
                    user_level.current_title_id = Some(first_title.id);
                    self.repo.update_user_level(&user_level).await?;
                }
            }
        }
        Ok(())
    }

    async fn add_experience(
        &self,
        user_id: &Uuid,
        request: AddExperienceRequest,
    ) -> Result<LevelUpResponse> {
        // Ensure user exists
        self.user_repo.find_by_id(user_id).await?.ok_or_else(|| {
            ErrorHelper::not_found_with_option("User", Some(&user_id.to_string()))
        })?;

        // Add experience via repository
        let (mut user_level, _history) = self
            .repo
            .add_experience(
                user_id,
                request.experience,
                request.source,
                request.metadata,
            )
            .await?;

        let mut leveled_up = false;
        let mut required_exp = Self::experience_for_next_level(user_level.level);

        // Loop to handle multiple level-ups from a single large XP gain
        while user_level.experience >= required_exp {
            leveled_up = true;
            user_level.level += 1;
            user_level.experience -= required_exp;
            required_exp = Self::experience_for_next_level(user_level.level);
        }

        if leveled_up {
            // Persist the new level and remaining experience
            user_level = self.repo.update_user_level(&user_level).await?;

            // Check for new titles at the new level and set initial if needed
            let unlocked_titles = self
                .title_service
                .check_and_unlock_titles_for_level(*user_id, user_level.level)
                .await?;

            if user_level.current_title_id.is_none() {
                if let Some(first_title) = unlocked_titles.first() {
                    user_level.current_title_id = Some(first_title.id);
                    // Update the user level again to save the new title
                    user_level = self.repo.update_user_level(&user_level).await?;
                }
            }

            // Invalidate cache after level up
            self.invalidate_user_level_cache(user_id).await?;
        }

        Ok(LevelUpResponse {
            new_level: user_level.level,
            user_id: *user_id,
            leveled_up,
            experience_details: user_level,
        })
    }

    async fn get_user_level(&self, user_id: &Uuid) -> Result<UserLevel> {
        // Try to get from cache first
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::user_level_cache_key(user_id);
            if let Ok(Some(cached_json)) = redis.get(&cache_key).await {
                if let Ok(user_level) = serde_json::from_str::<UserLevel>(&cached_json) {
                    CacheOperations::log_cache_hit("User level", &user_id.to_string());
                    return Ok(user_level);
                }
            }
            CacheOperations::log_cache_miss("User level", &user_id.to_string());
        }

        // Get from database
        let user_level = self.repo.get_user_level(user_id).await?.ok_or_else(|| {
            ErrorHelper::not_found_with_option("UserLevel", Some(&user_id.to_string()))
        })?;

        // Cache the result
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::user_level_cache_key(user_id);
            let ttl = Self::user_level_ttl();
            if let Ok(json) = serde_json::to_string(&user_level) {
                if let Err(e) = redis.set(&cache_key, &json, Some(ttl)).await {
                    tracing::warn!(
                        user_id = %user_id,
                        error = %e,
                        "Failed to cache user level"
                    );
                } else {
                    tracing::debug!(
                        user_id = %user_id,
                        level = user_level.level,
                        ttl_secs = ttl.as_secs(),
                        "Successfully cached user level"
                    );
                }
            }
        }

        Ok(user_level)
    }

    async fn get_user_experience_history(
        &self,
        user_id: &Uuid,
    ) -> Result<Vec<UserExperienceHistory>> {
        self.repo.get_experience_history(user_id).await
    }

    async fn set_current_title(&self, user_id: &Uuid, title_id: Uuid) -> Result<UserLevel> {
        self.repo.set_current_title(user_id, title_id).await
    }

    async fn get_leaderboard(&self, request: LeaderboardRequest) -> Result<Leaderboard> {
        // Try to get from cache first
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::leaderboard_cache_key(&request);
            if let Ok(Some(cached_json)) = redis.get(&cache_key).await {
                if let Ok(leaderboard) = serde_json::from_str::<Leaderboard>(&cached_json) {
                    tracing::debug!(
                        limit = request.limit,
                        cache_status = "hit",
                        "Leaderboard retrieved from cache"
                    );
                    return Ok(leaderboard);
                }
            }
            tracing::debug!(
                limit = request.limit,
                cache_status = "miss",
                "Leaderboard cache miss, querying database"
            );
        }

        // Get from database
        let leaderboard = self.repo.get_leaderboard(request.clone()).await?;

        // Cache the result
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::leaderboard_cache_key(&request);
            let ttl = Self::leaderboard_ttl();
            if let Ok(json) = serde_json::to_string(&leaderboard) {
                if let Err(e) = redis.set(&cache_key, &json, Some(ttl)).await {
                    tracing::warn!(
                        limit = request.limit,
                        error = %e,
                        "Failed to cache leaderboard"
                    );
                } else {
                    tracing::debug!(
                        limit = request.limit,
                        entries_count = leaderboard.entries.len(),
                        ttl_secs = ttl.as_secs(),
                        "Successfully cached leaderboard"
                    );
                }
            }
        }

        Ok(leaderboard)
    }

    async fn get_user_level_public(&self, user_id: &Uuid) -> Result<UserLevelPublicView> {
        let user_level =
            self.repo.get_user_level(user_id).await?.ok_or_else(|| {
                crate::errors::AppError::NotFound("User level not found".to_string())
            })?;
        let rank = self.repo.get_user_rank(user_id).await?;

        Ok(UserLevelPublicView {
            user_id: user_level.user_id,
            level: user_level.level,
            total_experience: user_level.total_experience,
            rank: Some(rank),
        })
    }

    async fn get_user_level_private(&self, user_id: &Uuid) -> Result<UserLevelPrivateView> {
        let user_level =
            self.repo.get_user_level(user_id).await?.ok_or_else(|| {
                crate::errors::AppError::NotFound("User level not found".to_string())
            })?;
        let rank = self.repo.get_user_rank(user_id).await?;

        Ok(UserLevelPrivateView {
            user_id: user_level.user_id,
            level: user_level.level,
            experience: user_level.experience,
            total_experience: user_level.total_experience,
            rank: Some(rank),
            created_at: user_level.created_at,
            updated_at: user_level.updated_at,
        })
    }
}
