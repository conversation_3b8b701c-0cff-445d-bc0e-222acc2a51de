use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use utoipa::ToSchema;
use uuid::Uuid;

// ===== ENUMS =====

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize, ToSchema, sqlx::Type)]
#[sqlx(type_name = "experience_transaction_type_enum")]
pub enum ExperienceTransactionType {
    #[serde(rename = "earned")]
    #[sqlx(rename = "earned")]
    Earned,
    #[serde(rename = "spent")]
    #[sqlx(rename = "spent")]
    Spent,
    #[serde(rename = "bonus")]
    #[sqlx(rename = "bonus")]
    Bonus,
    #[serde(rename = "penalty")]
    #[sqlx(rename = "penalty")]
    Penalty,
}

impl std::fmt::Display for ExperienceTransactionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ExperienceTransactionType::Earned => write!(f, "earned"),
            ExperienceTransactionType::Spent => write!(f, "spent"),
            ExperienceTransactionType::Bonus => write!(f, "bonus"),
            ExperienceTransactionType::Penalty => write!(f, "penalty"),
        }
    }
}

impl std::str::FromStr for ExperienceTransactionType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "earned" => Ok(ExperienceTransactionType::Earned),
            "spent" => Ok(ExperienceTransactionType::Spent),
            "bonus" => Ok(ExperienceTransactionType::Bonus),
            "penalty" => Ok(ExperienceTransactionType::Penalty),
            _ => Err(format!("Invalid experience transaction type: {s}")),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow, PartialEq, ToSchema)]
pub struct UserLevel {
    pub id: Uuid,
    pub user_id: Uuid,
    pub level: i32,
    pub experience: i64,
    pub total_experience: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub current_title_id: Option<Uuid>,
}

// ===== PUBLIC API VIEWS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "user_id": "15ef5806-6578-46ca-b999-89c391079e7a",
    "username": "superadmin",
    "fullname": "Super Administrator",
    "level": 5,
    "total_experience": 1250,
    "rank": 1
}))]
pub struct LeaderboardEntry {
    pub user_id: Uuid,
    pub username: String,
    pub fullname: String,
    pub level: i32,
    pub total_experience: i64,
    pub rank: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "entries": [
        {
            "user_id": "15ef5806-6578-46ca-b999-89c391079e7a",
            "username": "superadmin",
            "fullname": "Super Administrator",
            "level": 5,
            "total_experience": 1250,
            "rank": 1
        }
    ],
    "total_users": 100
}))]
pub struct Leaderboard {
    pub entries: Vec<LeaderboardEntry>,
    pub total_users: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "user_id": "15ef5806-6578-46ca-b999-89c391079e7a",
    "level": 5,
    "total_experience": 1250,
    "rank": 1
}))]
pub struct UserLevelPublicView {
    pub user_id: Uuid,
    pub level: i32,
    pub total_experience: i64,
    pub rank: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "user_id": "15ef5806-6578-46ca-b999-89c391079e7a",
    "level": 5,
    "experience": 250,
    "total_experience": 1250,
    "rank": 1,
    "created_at": "2025-07-05T01:31:59.286900",
    "updated_at": "2025-07-05T01:31:59.286900"
}))]
pub struct UserLevelPrivateView {
    pub user_id: Uuid,
    pub level: i32,
    pub experience: i64,
    pub total_experience: i64,
    pub rank: Option<i32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeaderboardRequest {
    #[serde(default = "default_limit")]
    pub limit: i32,
}

fn default_limit() -> i32 {
    10
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewUserLevel {
    pub user_id: Uuid,
}

// ===== ADDITIONAL MODELS FOR SQLX =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserExperience {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub level: i32,
    pub experience_to_next_level: i64,
    pub total_experience_earned: i64,
    pub last_activity_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperienceTransaction {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub transaction_type: ExperienceTransactionType,
    pub source: String,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LevelThreshold {
    pub id: Uuid,
    pub level: i32,
    pub experience_required: i64,
    pub cumulative_experience: i64,
    pub title: Option<String>,
    pub description: Option<String>,
    pub rewards: Option<serde_json::Value>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewUserExperience {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub level: i32,
    pub experience_to_next_level: i64,
    pub total_experience_earned: i64,
    pub last_activity_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewExperienceTransaction {
    pub id: Uuid,
    pub user_id: Uuid,
    pub experience_points: i64,
    pub transaction_type: ExperienceTransactionType,
    pub source: String,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewLevelThreshold {
    pub id: Uuid,
    pub level: i32,
    pub experience_required: i64,
    pub cumulative_experience: i64,
    pub title: Option<String>,
    pub description: Option<String>,
    pub rewards: Option<serde_json::Value>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow, PartialEq, ToSchema)]
pub struct UserExperienceHistory {
    pub id: Uuid,
    pub user_id: Uuid,
    pub exp_gained: i32,
    pub source: String,
    pub source_id: Option<Uuid>,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewUserExperienceHistory {
    pub user_id: Uuid,
    pub exp_gained: i32,
    pub source: String,
    pub source_id: Option<Uuid>,
    pub description: Option<String>,
}
