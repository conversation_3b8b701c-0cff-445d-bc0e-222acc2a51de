use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Title {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub level_required: i32,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NewTitle {
    pub name: String,
    pub description: Option<String>,
    pub level_required: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserUnlockedTitle {
    pub user_id: Uuid,
    pub title_id: Uuid,
    pub unlocked_at: NaiveDateTime,
}
