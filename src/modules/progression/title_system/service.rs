use crate::{
    errors::Result,
    modules::progression::title_system::{
        models::Title, repository_trait::DynTitleRepo, sqlx_repository::SqlxTitleRepository,
    },
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait TitleServiceTrait: Send + Sync {
    async fn check_and_unlock_titles_for_level(
        &self,
        user_id: Uuid,
        new_level: i32,
    ) -> Result<Vec<Title>>;
    async fn get_user_unlocked_titles(&self, user_id: Uuid) -> Result<Vec<Title>>;
    async fn get_title_by_id(&self, title_id: Uuid) -> Result<Option<Title>>;
}

pub type DynTitleService = Arc<dyn TitleServiceTrait>;

#[derive(Clone)]
pub struct TitleService {
    repo: DynTitleRepo,
}

impl TitleService {
    pub fn new(repo: DynTitleRepo) -> Self {
        Self { repo }
    }

    pub fn new_with_sqlx(database: crate::database::Database) -> Self {
        Self {
            repo: Arc::new(SqlxTitleRepository::new(database)),
        }
    }
}

#[async_trait]
impl TitleServiceTrait for TitleService {
    async fn check_and_unlock_titles_for_level(
        &self,
        user_id: Uuid,
        new_level: i32,
    ) -> Result<Vec<Title>> {
        let titles_to_unlock = self.repo.get_titles_for_level(new_level).await?;

        if !titles_to_unlock.is_empty() {
            let title_ids = titles_to_unlock.iter().map(|t| t.id).collect();
            self.repo
                .unlock_multiple_titles_for_user(user_id, title_ids)
                .await?;
        }

        Ok(titles_to_unlock)
    }

    async fn get_user_unlocked_titles(&self, user_id: Uuid) -> Result<Vec<Title>> {
        self.repo.get_user_unlocked_titles(user_id).await
    }

    async fn get_title_by_id(&self, title_id: Uuid) -> Result<Option<Title>> {
        self.repo.get_title_by_id(title_id).await
    }
}
