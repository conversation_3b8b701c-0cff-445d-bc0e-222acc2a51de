use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxTitle {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub level_required: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxUserUnlockedTitle {
    pub user_id: Uuid,
    pub title_id: Uuid,
    pub unlocked_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxTitle> for super::models::Title {
    fn from(title: SqlxTitle) -> Self {
        Self {
            id: title.id,
            name: title.name,
            description: title.description,
            level_required: title.level_required,
            created_at: title.created_at.naive_utc(),
            updated_at: title.updated_at.naive_utc(),
        }
    }
}

impl From<SqlxUserUnlockedTitle> for super::models::UserUnlockedTitle {
    fn from(unlocked: SqlxUserUnlockedTitle) -> Self {
        Self {
            user_id: unlocked.user_id,
            title_id: unlocked.title_id,
            unlocked_at: unlocked.unlocked_at.naive_utc(),
        }
    }
}

// ===== CREATE PARAMS =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTitleParams {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub level_required: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserUnlockedTitleParams {
    pub user_id: Uuid,
    pub title_id: Uuid,
    pub unlocked_at: DateTime<Utc>,
}

impl From<super::models::NewTitle> for CreateTitleParams {
    fn from(new_title: super::models::NewTitle) -> Self {
        Self {
            id: Uuid::new_v4(),
            name: new_title.name,
            description: new_title.description,
            level_required: new_title.level_required,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}
