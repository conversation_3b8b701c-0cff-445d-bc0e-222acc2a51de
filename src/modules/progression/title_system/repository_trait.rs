use crate::errors::Result;
use crate::modules::progression::title_system::models::{NewTitle, Title};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait TitleRepository: Send + Sync {
    async fn get_titles_for_level(&self, level: i32) -> Result<Vec<Title>>;
    async fn unlock_multiple_titles_for_user(
        &self,
        user_id: Uuid,
        title_ids: Vec<Uuid>,
    ) -> Result<()>;
    async fn create_title(&self, new_title: NewTitle) -> Result<Title>;
    async fn get_user_unlocked_titles(&self, user_id: Uuid) -> Result<Vec<Title>>;
    async fn get_title_by_id(&self, title_id: Uuid) -> Result<Option<Title>>;
}

pub type DynTitleRepo = Arc<dyn TitleRepository>;
