use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::progression::title_system::models::{NewTitle, Title},
    modules::progression::title_system::sqlx_models::{CreateTitleParams, SqlxTitle},
    repository::base_repository::AsyncRepository,
};
use async_trait::async_trait;
use uuid::Uuid;

use super::repository_trait::TitleRepository;

// ===== SQLX TITLE REPOSITORY =====

#[derive(Clone)]
pub struct SqlxTitleRepository {
    base: AsyncRepository,
}

impl SqlxTitleRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl TitleRepository for SqlxTitleRepository {
    async fn get_titles_for_level(&self, level: i32) -> Result<Vec<Title>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let titles = sqlx::query_as::<_, SqlxTitle>(
                        "SELECT * FROM titles WHERE level_required = $1",
                    )
                    .bind(level)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(titles.into_iter().map(Title::from).collect())
                })
            })
            .await
    }

    async fn unlock_multiple_titles_for_user(
        &self,
        user_id: Uuid,
        title_ids: Vec<Uuid>,
    ) -> Result<()> {
        if title_ids.is_empty() {
            return Ok(());
        }

        self.base
            .execute_transaction(|tx| {
                Box::pin(async move {
                    // Use batch insert with UNNEST for better performance
                    // This replaces the for loop with a single efficient query
                    sqlx::query(
                        "INSERT INTO user_unlocked_titles (user_id, title_id, unlocked_at) 
                         SELECT $1, unnest($2::uuid[]), NOW()
                         ON CONFLICT (user_id, title_id) DO NOTHING",
                    )
                    .bind(user_id)
                    .bind(&title_ids)
                    .execute(&mut **tx)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(())
                })
            })
            .await
    }

    async fn create_title(&self, new_title: NewTitle) -> Result<Title> {
        let params = CreateTitleParams::from(new_title);

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let title = sqlx::query_as::<_, SqlxTitle>(
                        "INSERT INTO titles (id, name, description, level_required, created_at, updated_at) 
                         VALUES ($1, $2, $3, $4, $5, $6) 
                         RETURNING *"
                    )
                    .bind(params.id)
                    .bind(&params.name)
                    .bind(params.description.as_ref())
                    .bind(params.level_required)
                    .bind(params.created_at)
                    .bind(params.updated_at)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(Title::from(title))
                })
            })
            .await
    }

    async fn get_user_unlocked_titles(&self, user_id: Uuid) -> Result<Vec<Title>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let titles = sqlx::query_as::<_, SqlxTitle>(
                        "SELECT t.* FROM titles t 
                         INNER JOIN user_unlocked_titles ult ON t.id = ult.title_id 
                         WHERE ult.user_id = $1",
                    )
                    .bind(user_id)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(titles.into_iter().map(Title::from).collect())
                })
            })
            .await
    }

    async fn get_title_by_id(&self, title_id: Uuid) -> Result<Option<Title>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let title =
                        sqlx::query_as::<_, SqlxTitle>("SELECT * FROM titles WHERE id = $1")
                            .bind(title_id)
                            .fetch_optional(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(title.map(Title::from))
                })
            })
            .await
    }
}
