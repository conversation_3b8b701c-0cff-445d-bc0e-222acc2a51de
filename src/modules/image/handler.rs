use crate::{
    errors::Result,
    modules::image::{
        models::{ImageDeletionRequest, ImageSignatureRequest, ImageUrlValidationRequest},
        service::ImageServiceTrait,
    },
    response::success_response,
    routes::middleware::auth::AuthenticatedUser,
};
use axum::{
    Json,
    extract::{Extension, State},
    response::IntoResponse,
};
use std::sync::Arc;
use utoipa::OpenApi;
use validator::Validate;

#[derive(OpenApi)]
#[openapi(
    paths(generate_image_signature, validate_image_url, delete_image, extract_public_id, cloudinary_health_check),
    components(schemas(
        ImageSignatureRequest,
        crate::modules::image::models::ImageSignatureResponse,
        ImageUrlValidationRequest,
        ImageDeletionRequest,
        crate::modules::image::models::ImageDeletionResponse,
        crate::modules::image::service::CloudinaryHealthStatus,
    )),
    tags((name = "Image", description = "Image upload and management endpoints"))
)]
pub struct ImageApiDoc;

/// Generate signature for client-side image upload
#[utoipa::path(
    post,
    path = "/api/image/signature",
    request_body = ImageSignatureRequest,
    responses(
        (status = 200, description = "Signature generated successfully", body = crate::response::ApiResponse<crate::modules::image::models::ImageSignatureResponse>),
        (status = 400, description = "Invalid request", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Unauthorized", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Image",
    security(("bearer_auth" = []))
)]
pub async fn generate_image_signature(
    State(image_service): State<Arc<dyn ImageServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<ImageSignatureRequest>,
) -> Result<impl IntoResponse> {
    // Validate request
    request
        .validate()
        .map_err(|e| crate::errors::AppError::BadRequest(e.to_string()))?;

    tracing::info!(
        "Generating image signature for user {} with resource_type: {}, folder: {}",
        user.user_id,
        request.resource_type,
        request.folder
    );

    let signature_response = image_service.generate_signature(request).await?;

    Ok(success_response(
        "/api/image/signature".to_string(),
        "SIGNATURE_GENERATED",
        "Image upload signature generated successfully",
        signature_response,
    ))
}

/// Validate Cloudinary image URL
#[utoipa::path(
    post,
    path = "/api/image/validate",
    request_body = ImageUrlValidationRequest,
    responses(
        (status = 200, description = "URL validation result", body = crate::response::ApiResponse<bool>),
        (status = 400, description = "Invalid request", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Unauthorized", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Image",
    security(("bearer_auth" = []))
)]
pub async fn validate_image_url(
    State(image_service): State<Arc<dyn ImageServiceTrait>>,
    Extension(_user): Extension<AuthenticatedUser>,
    Json(request): Json<ImageUrlValidationRequest>,
) -> Result<impl IntoResponse> {
    // Validate request
    request
        .validate()
        .map_err(|e| crate::errors::AppError::BadRequest(e.to_string()))?;

    tracing::debug!(
        "Validating image URL: {} for resource_type: {}",
        request.image_url,
        request.resource_type
    );

    let is_valid = image_service
        .validate_cloudinary_url(&request.image_url, &request.resource_type)
        .await?;

    Ok(success_response(
        "/api/image/validate".to_string(),
        "URL_VALIDATED",
        "Image URL validation completed",
        is_valid,
    ))
}

/// Delete image from Cloudinary
#[utoipa::path(
    delete,
    path = "/api/image/delete",
    request_body = ImageDeletionRequest,
    responses(
        (status = 200, description = "Image deletion result", body = crate::response::ApiResponse<crate::modules::image::models::ImageDeletionResponse>),
        (status = 400, description = "Invalid request", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Unauthorized", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Image",
    security(("bearer_auth" = []))
)]
pub async fn delete_image(
    State(image_service): State<Arc<dyn ImageServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<ImageDeletionRequest>,
) -> Result<impl IntoResponse> {
    tracing::info!(
        "User {} requesting deletion of image with public_id: {} for resource_type: {}",
        user.user_id,
        request.public_id,
        request.resource_type
    );

    let deletion_response = image_service.delete_image(request).await?;

    let (status_code, message) = if deletion_response.success {
        ("IMAGE_DELETED", "Image deleted successfully")
    } else {
        ("IMAGE_DELETE_FAILED", "Failed to delete image")
    };

    Ok(success_response(
        "/api/image/delete".to_string(),
        status_code,
        message,
        deletion_response,
    ))
}

/// Extract public ID from Cloudinary URL
#[utoipa::path(
    post,
    path = "/api/image/extract-public-id",
    request_body = ImageUrlValidationRequest,
    responses(
        (status = 200, description = "Public ID extracted successfully", body = crate::response::ApiResponse<String>),
        (status = 400, description = "Invalid request", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Unauthorized", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Image",
    security(("bearer_auth" = []))
)]
pub async fn extract_public_id(
    State(image_service): State<Arc<dyn ImageServiceTrait>>,
    Extension(_user): Extension<AuthenticatedUser>,
    Json(request): Json<ImageUrlValidationRequest>,
) -> Result<impl IntoResponse> {
    // Validate request
    request
        .validate()
        .map_err(|e| crate::errors::AppError::BadRequest(e.to_string()))?;

    tracing::debug!("Extracting public_id from URL: {}", request.image_url);

    let public_id = image_service
        .extract_public_id_from_url(&request.image_url)
        .await?;

    Ok(success_response(
        "/api/image/extract-public-id".to_string(),
        "PUBLIC_ID_EXTRACTED",
        "Public ID extracted successfully",
        public_id,
    ))
}

/// Check Cloudinary service health
#[utoipa::path(
    get,
    path = "/api/image/health",
    responses(
        (status = 200, description = "Cloudinary health status", body = crate::response::ApiResponse<crate::modules::image::service::CloudinaryHealthStatus>),
        (status = 401, description = "Unauthorized", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Image",
    security(("bearer_auth" = []))
)]
pub async fn cloudinary_health_check(
    State(image_service): State<Arc<dyn ImageServiceTrait>>,
    Extension(_user): Extension<AuthenticatedUser>,
) -> Result<impl IntoResponse> {
    tracing::debug!("Checking Cloudinary health status");

    let health_status = image_service.health_check().await?;

    let (status_code, message) = if health_status.api_accessible {
        ("CLOUDINARY_HEALTHY", "Cloudinary service is healthy")
    } else {
        (
            "CLOUDINARY_UNHEALTHY",
            "Cloudinary service is not accessible",
        )
    };

    Ok(success_response(
        "/api/image/health".to_string(),
        status_code,
        message,
        health_status,
    ))
}
