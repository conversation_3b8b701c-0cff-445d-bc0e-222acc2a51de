#[cfg(test)]
mod tests {
    use crate::config::CloudinaryConfig;
    use crate::modules::image::{
        models::ImageSignatureRequest,
        service::{ImageService, ImageServiceTrait},
        validation::ImageValidation,
    };

    fn create_test_config() -> CloudinaryConfig {
        CloudinaryConfig {
            cloud_name: "test_cloud".to_string(),
            api_key: "test_key".to_string(),
            api_secret: "test_secret".to_string(),
            upload_preset: Some("test_preset".to_string()),
            secure: true,
        }
    }

    fn create_test_service() -> ImageService {
        ImageService::new(create_test_config())
    }

    #[test]
    fn test_validate_resource_type() {
        assert!(ImageValidation::validate_resource_type("laptop").is_ok());
        assert!(ImageValidation::validate_resource_type("category").is_ok());
        assert!(ImageValidation::validate_resource_type("user").is_ok());
        assert!(ImageValidation::validate_resource_type("invalid").is_err());
    }

    #[test]
    fn test_validate_folder_permission() {
        assert!(ImageValidation::validate_folder_permission("laptop", "laptops/123").is_ok());
        assert!(ImageValidation::validate_folder_permission("laptop", "categories/123").is_err());
        assert!(ImageValidation::validate_folder_permission("category", "categories/456").is_ok());
        assert!(ImageValidation::validate_folder_permission("user", "users/789").is_ok());
    }

    #[test]
    fn test_extract_public_id_from_url() {
        let url1 =
            "https://res.cloudinary.com/demo/image/upload/v1234567890/laptops/abc123/image.jpg";
        assert_eq!(
            ImageValidation::extract_public_id_from_url(url1).unwrap(),
            "laptops/abc123/image"
        );

        let url2 = "https://res.cloudinary.com/demo/image/upload/sample.jpg";
        assert_eq!(
            ImageValidation::extract_public_id_from_url(url2).unwrap(),
            "sample"
        );

        let invalid_url = "https://example.com/image.jpg";
        assert!(ImageValidation::extract_public_id_from_url(invalid_url).is_err());
    }

    #[tokio::test]
    async fn test_generate_signature() {
        let service = create_test_service();
        let request = ImageSignatureRequest {
            resource_type: "laptop".to_string(),
            folder: "laptops/test-123".to_string(),
            tags: Some(vec!["test".to_string(), "laptop".to_string()]),
            max_file_size: Some(5000000), // 5MB
            allowed_formats: Some(vec!["jpg".to_string(), "png".to_string()]),
        };

        let result = service.generate_signature(request).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.cloud_name, "test_cloud");
        assert_eq!(response.api_key, "test_key");
        assert_eq!(response.folder, "laptops/test-123");
        assert!(response.signature.len() == 40); // SHA1 hex string
    }

    #[tokio::test]
    async fn test_validate_cloudinary_url() {
        let service = create_test_service();

        // Valid URL
        let valid_url =
            "https://res.cloudinary.com/test_cloud/image/upload/v1234567890/laptops/test/image.jpg";
        let result = service.validate_cloudinary_url(valid_url, "laptop").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Invalid URL (wrong domain)
        let invalid_url = "https://example.com/image.jpg";
        let result = service.validate_cloudinary_url(invalid_url, "laptop").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());

        // Invalid URL (wrong resource type)
        let wrong_type_url = "https://res.cloudinary.com/test_cloud/image/upload/v1234567890/categories/test/image.jpg";
        let result = service
            .validate_cloudinary_url(wrong_type_url, "laptop")
            .await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_service_extract_public_id_from_url() {
        let service = create_test_service();

        let url = "https://res.cloudinary.com/test_cloud/image/upload/v1234567890/laptops/test-123/image.jpg";
        let result = service.extract_public_id_from_url(url).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "laptops/test-123/image");
    }

    #[test]
    fn test_validate_image_formats() {
        let valid_formats = vec!["jpg".to_string(), "png".to_string(), "webp".to_string()];
        assert!(ImageValidation::validate_image_formats(&valid_formats).is_ok());

        let invalid_formats = vec!["exe".to_string(), "txt".to_string()];
        assert!(ImageValidation::validate_image_formats(&invalid_formats).is_err());
    }

    #[test]
    fn test_validate_file_size() {
        assert!(ImageValidation::validate_file_size(1000000).is_ok()); // 1MB
        assert!(ImageValidation::validate_file_size(0).is_err()); // Too small
        assert!(ImageValidation::validate_file_size(25 * 1024 * 1024).is_err()); // Too large (25MB)
    }
}
