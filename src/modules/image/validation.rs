use crate::errors::{AppError, Result};

pub struct ImageValidation;

impl ImageValidation {
    /// Validate resource type
    pub fn validate_resource_type(resource_type: &str) -> Result<()> {
        match resource_type {
            "laptop" | "category" | "user" => Ok(()),
            _ => Err(AppError::BadRequest(format!(
                "Invalid resource type: {}. Allowed types: laptop, category, user",
                resource_type
            ))),
        }
    }

    /// Validate folder structure matches resource type
    pub fn validate_folder_permission(resource_type: &str, folder: &str) -> Result<()> {
        match resource_type {
            "laptop" => {
                if !folder.starts_with("laptops/") {
                    return Err(AppError::BadRequest(
                        "Invalid folder for laptop resource. Must start with 'laptops/'"
                            .to_string(),
                    ));
                }
            }
            "category" => {
                if !folder.starts_with("categories/") {
                    return Err(AppError::BadRequest(
                        "Invalid folder for category resource. Must start with 'categories/'"
                            .to_string(),
                    ));
                }
            }
            "user" => {
                if !folder.starts_with("users/") {
                    return Err(AppError::BadRequest(
                        "Invalid folder for user resource. Must start with 'users/'".to_string(),
                    ));
                }
            }
            _ => return Err(AppError::BadRequest("Invalid resource type".to_string())),
        }
        Ok(())
    }

    /// Validate allowed image formats
    pub fn validate_image_formats(formats: &[String]) -> Result<()> {
        let allowed_formats = ["jpg", "jpeg", "png", "webp", "gif"];

        for format in formats {
            if !allowed_formats.contains(&format.to_lowercase().as_str()) {
                return Err(AppError::BadRequest(format!(
                    "Invalid image format: {}. Allowed formats: {}",
                    format,
                    allowed_formats.join(", ")
                )));
            }
        }
        Ok(())
    }

    /// Validate file size
    pub fn validate_file_size(size_bytes: u32) -> Result<()> {
        const MAX_SIZE: u32 = 20 * 1024 * 1024; // 20MB
        const MIN_SIZE: u32 = 1; // 1 byte

        if size_bytes < MIN_SIZE {
            return Err(AppError::BadRequest(
                "File size must be at least 1 byte".to_string(),
            ));
        }

        if size_bytes > MAX_SIZE {
            return Err(AppError::BadRequest(format!(
                "File size {} bytes exceeds maximum allowed size of {} bytes (20MB)",
                size_bytes, MAX_SIZE
            )));
        }

        Ok(())
    }

    /// Validate Cloudinary URL format
    pub fn validate_cloudinary_url_format(url: &str, cloud_name: &str) -> Result<()> {
        let expected_domain = format!("res.cloudinary.com/{}", cloud_name);

        if !url.contains(&expected_domain) {
            return Err(AppError::BadRequest(format!(
                "URL must be from Cloudinary domain: {}",
                expected_domain
            )));
        }

        // Basic URL structure validation
        if !url.starts_with("https://") && !url.starts_with("http://") {
            return Err(AppError::BadRequest(
                "URL must use HTTP or HTTPS protocol".to_string(),
            ));
        }

        Ok(())
    }

    /// Extract and validate public ID from Cloudinary URL
    pub fn extract_public_id_from_url(url: &str) -> Result<String> {
        // URL format: https://res.cloudinary.com/{cloud_name}/image/upload/v{version}/{public_id}.{format}
        // or: https://res.cloudinary.com/{cloud_name}/image/upload/{public_id}.{format}

        let parts: Vec<&str> = url.split('/').collect();
        if parts.len() < 7 {
            return Err(AppError::BadRequest(
                "Invalid Cloudinary URL format. Expected format: https://res.cloudinary.com/{cloud_name}/image/upload/[v{version}/]{public_id}.{format}".to_string()
            ));
        }

        // Find the upload part
        let upload_index = parts
            .iter()
            .position(|&part| part == "upload")
            .ok_or_else(|| AppError::BadRequest("URL must contain 'upload' segment".to_string()))?;

        if upload_index + 1 >= parts.len() {
            return Err(AppError::BadRequest(
                "Invalid URL structure after 'upload'".to_string(),
            ));
        }

        // Get everything after upload/ as potential public_id parts
        let public_id_parts = &parts[upload_index + 1..];

        // Skip version if present (starts with 'v' followed by numbers)
        let public_id_parts = if public_id_parts.len() > 1
            && public_id_parts[0].starts_with('v')
            && public_id_parts[0][1..].chars().all(|c| c.is_ascii_digit())
        {
            &public_id_parts[1..]
        } else {
            public_id_parts
        };

        if public_id_parts.is_empty() {
            return Err(AppError::BadRequest(
                "Cannot extract public_id from URL".to_string(),
            ));
        }

        // Last part contains filename with extension
        let filename_with_ext = public_id_parts[public_id_parts.len() - 1];
        let filename = filename_with_ext
            .split('.')
            .next()
            .ok_or_else(|| AppError::BadRequest("Cannot extract filename from URL".to_string()))?;

        // Reconstruct full public_id including folder path
        if public_id_parts.len() == 1 {
            Ok(filename.to_string())
        } else {
            let folder_parts = &public_id_parts[..public_id_parts.len() - 1];
            Ok(format!("{}/{}", folder_parts.join("/"), filename))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_resource_type() {
        assert!(ImageValidation::validate_resource_type("laptop").is_ok());
        assert!(ImageValidation::validate_resource_type("category").is_ok());
        assert!(ImageValidation::validate_resource_type("user").is_ok());
        assert!(ImageValidation::validate_resource_type("invalid").is_err());
    }

    #[test]
    fn test_validate_folder_permission() {
        assert!(ImageValidation::validate_folder_permission("laptop", "laptops/123").is_ok());
        assert!(ImageValidation::validate_folder_permission("laptop", "categories/123").is_err());
        assert!(ImageValidation::validate_folder_permission("category", "categories/456").is_ok());
        assert!(ImageValidation::validate_folder_permission("user", "users/789").is_ok());
    }

    #[test]
    fn test_extract_public_id_from_url() {
        let url1 =
            "https://res.cloudinary.com/demo/image/upload/v1234567890/laptops/abc123/image.jpg";
        assert_eq!(
            ImageValidation::extract_public_id_from_url(url1).unwrap(),
            "laptops/abc123/image"
        );

        let url2 = "https://res.cloudinary.com/demo/image/upload/sample.jpg";
        assert_eq!(
            ImageValidation::extract_public_id_from_url(url2).unwrap(),
            "sample"
        );

        let invalid_url = "https://example.com/image.jpg";
        assert!(ImageValidation::extract_public_id_from_url(invalid_url).is_err());
    }
}
