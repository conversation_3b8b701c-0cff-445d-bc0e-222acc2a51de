use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct ImageSignatureRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Resource type must be between 1 and 50 characters"
    ))]
    pub resource_type: String, // "laptop", "category", "user"

    #[validate(length(
        min = 1,
        max = 200,
        message = "Folder must be between 1 and 200 characters"
    ))]
    pub folder: String, // "laptops/{laptop_id}"

    pub tags: Option<Vec<String>>,

    #[validate(range(
        min = 1,
        max = 20971520,
        message = "Max file size must be between 1 byte and 20MB"
    ))] // Max 20MB
    pub max_file_size: Option<u32>,

    pub allowed_formats: Option<Vec<String>>, // ["jpg", "png", "webp"]
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ImageSignatureResponse {
    pub signature: String,
    pub timestamp: i64,
    pub api_key: String,
    pub cloud_name: String,
    pub folder: String,
    pub tags: Option<String>,
    pub upload_preset: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct ImageUrlValidationRequest {
    #[validate(url(message = "Must be a valid URL"))]
    pub image_url: String,

    #[validate(length(
        min = 1,
        max = 50,
        message = "Resource type must be between 1 and 50 characters"
    ))]
    pub resource_type: String,

    pub resource_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ImageDeletionRequest {
    pub public_id: String,
    pub resource_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ImageDeletionResponse {
    pub success: bool,
    pub message: String,
    pub deleted_public_id: Option<String>,
}
