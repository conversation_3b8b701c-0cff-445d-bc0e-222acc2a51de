use crate::{
    config::TokenConfig,
    errors::Result,
    modules::{
        auth::{
            models::{LoginRequest, LoginResponse, RegisterRequest},
            oauth_providers::{
                OAuthAuthUrlResponse, OAuthProviderFactory, OAuthState, OAuthUserInfo,
            },
            utils::{EmailEventPublisher, EmailEventPublisherMixin},
        },
        email::{EmailEvent, EmailEventBus},
        permission::service_trait::PermissionServiceTrait,
        role::service_trait::RoleServiceTrait,
        user::{database::repository::DynUserRepo, traits::service_traits::UserServiceTrait},
    },
};
use std::sync::Arc;
use uuid;

use super::{
    authentication_service::AuthenticationService, authorization_service::AuthorizationService,
    oauth_providers::OAuthProvider, service_trait::AuthServiceTrait,
    services::oauth_service::OAuthService, token_service::TokenService,
};
use async_trait::async_trait;

/// Facade service that composes the smaller services (SRP: Composition over monolithic design)
#[derive(Clone)]
pub struct AuthService {
    authentication_service: AuthenticationService,
    token_service: TokenService,
    authorization_service: AuthorizationService,
    oauth_service: OAuthService,
    user_service: Arc<dyn UserServiceTrait>,
    email_publisher: EmailEventPublisherMixin,
}

impl AuthService {
    pub fn new(
        user_service: Arc<dyn UserServiceTrait>,
        user_repo: DynUserRepo,
        role_service: Arc<dyn RoleServiceTrait>,
        _permission_service: Arc<dyn PermissionServiceTrait>,
        jwt_secret: String,
        token_config: TokenConfig,
    ) -> Self {
        let token_service = TokenService::new(jwt_secret, token_config);
        let authorization_service =
            AuthorizationService::new(Arc::clone(&user_service), role_service);
        let authentication_service = AuthenticationService::new(
            Arc::clone(&user_service),
            user_repo,
            token_service.clone(),
            authorization_service.clone(),
        );
        let oauth_service = OAuthService::new(
            Arc::clone(&user_service),
            token_service.clone(),
            authorization_service.clone(),
        );

        Self {
            authentication_service,
            token_service,
            authorization_service,
            oauth_service,
            user_service,
            email_publisher: EmailEventPublisherMixin::new(),
        }
    }

    pub fn with_oauth_provider_factory(
        user_service: Arc<dyn UserServiceTrait>,
        user_repo: DynUserRepo,
        role_service: Arc<dyn RoleServiceTrait>,
        _permission_service: Arc<dyn PermissionServiceTrait>,
        jwt_secret: String,
        token_config: TokenConfig,
        oauth_provider_factory: OAuthProviderFactory,
    ) -> Self {
        Self::with_oauth_provider_factory_and_email_bus(
            user_service,
            user_repo,
            role_service,
            _permission_service,
            jwt_secret,
            token_config,
            oauth_provider_factory,
            None,
        )
    }

    #[allow(clippy::too_many_arguments)]
    pub fn with_oauth_provider_factory_and_email_bus(
        user_service: Arc<dyn UserServiceTrait>,
        user_repo: DynUserRepo,
        role_service: Arc<dyn RoleServiceTrait>,
        _permission_service: Arc<dyn PermissionServiceTrait>,
        jwt_secret: String,
        token_config: TokenConfig,
        oauth_provider_factory: OAuthProviderFactory,
        email_event_bus: Option<Arc<EmailEventBus>>,
    ) -> Self {
        let token_service = TokenService::new(jwt_secret, token_config);
        let authorization_service =
            AuthorizationService::new(Arc::clone(&user_service), role_service);
        let authentication_service = AuthenticationService::new(
            Arc::clone(&user_service),
            user_repo,
            token_service.clone(),
            authorization_service.clone(),
        );
        let oauth_service = OAuthService::with_provider_factory(
            Arc::clone(&user_service),
            token_service.clone(),
            authorization_service.clone(),
            oauth_provider_factory,
        );

        let mut auth_service = Self {
            authentication_service,
            token_service,
            authorization_service,
            oauth_service,
            user_service,
            email_publisher: EmailEventPublisherMixin::new(),
        };

        // Set email event bus if provided
        if let Some(email_bus) = email_event_bus {
            auth_service.set_email_event_bus(email_bus);
        }

        auth_service
    }

    /// Set email event bus for sending email notifications
    pub fn set_email_event_bus(&mut self, email_event_bus: Arc<EmailEventBus>) {
        tracing::info!("🔧 Setting email event bus for AuthService");
        self.email_publisher
            .set_email_event_bus(Arc::clone(&email_event_bus));
        // Also set for OAuth service
        self.oauth_service.set_email_event_bus(email_event_bus);
        tracing::info!("✅ Email event bus configured for AuthService");
    }

    /// Register một OAuth provider
    pub fn register_oauth_provider(&mut self, provider: Arc<dyn OAuthProvider>) {
        self.oauth_service.register_provider(provider);
    }

    /// Get danh sách các OAuth providers đã được đăng ký
    pub fn get_supported_oauth_providers(&self) -> Vec<&str> {
        self.oauth_service.get_supported_providers()
    }

    /// Get danh sách các OAuth providers đã được cấu hình
    pub fn get_configured_oauth_providers(&self) -> Vec<&str> {
        self.oauth_service.get_configured_providers()
    }

    /// Generic OAuth login cho bất kỳ provider nào
    pub async fn oauth_login(&self, oauth_user: OAuthUserInfo) -> Result<LoginResponse> {
        self.oauth_service.oauth_login(oauth_user).await
    }
}

#[async_trait]
impl EmailEventPublisher for AuthService {
    fn get_email_event_bus(&self) -> &Option<Arc<EmailEventBus>> {
        self.email_publisher.get_email_event_bus()
    }

    async fn publish_email_event(&self, event: EmailEvent) {
        self.email_publisher.publish_email_event(event).await;
    }
}

#[async_trait]
impl AuthServiceTrait for AuthService {
    /// Register new user with default member role
    async fn register(&self, request: RegisterRequest) -> Result<LoginResponse> {
        let response = self.authentication_service.register(request).await?;

        // Publish welcome email event using shared utility (DRY)
        self.publish_email_event(EmailEvent::WelcomeEmail {
            user_id: response.user.id.to_string(),
            email: response.user.email.clone(),
            name: response.user.fullname.clone(),
        })
        .await;

        Ok(response)
    }

    /// Login user
    async fn login(&self, request: LoginRequest) -> Result<LoginResponse> {
        self.authentication_service.login(request).await
    }

    /// Refresh access token using refresh token
    async fn refresh_token(&self, refresh_token: &str) -> Result<LoginResponse> {
        self.authentication_service
            .refresh_token(refresh_token)
            .await
    }

    /// Verify token and extract claims (delegated to TokenService)
    fn verify_token(&self, token: &str) -> Result<crate::modules::auth::models::TokenClaims> {
        self.token_service.verify_token(token)
    }

    /// Generate token from claims (delegated to TokenService)
    fn generate_token_from_claims(
        &self,
        claims: &crate::modules::auth::models::TokenClaims,
    ) -> Result<String> {
        self.token_service.generate_token_from_claims(claims)
    }

    /// Extract user ID from Authorization header (delegated to TokenService)
    fn extract_user_id_from_header(&self, auth_header: Option<&str>) -> Result<String> {
        self.token_service.extract_user_id_from_header(auth_header)
    }

    /// Check if user has specific permission (delegated to AuthorizationService)
    async fn user_has_permission(&self, user_id: &str, permission: &str) -> Result<bool> {
        let user_uuid = uuid::Uuid::parse_str(user_id)
            .map_err(|_| crate::errors::AppError::Validation("Invalid user ID format".into()))?;
        self.authorization_service
            .user_has_permission(&user_uuid, permission)
            .await
    }

    /// Check if user has specific role (delegated to AuthorizationService)
    async fn user_has_role(&self, user_id: &str, role: &str) -> Result<bool> {
        let user_uuid = uuid::Uuid::parse_str(user_id)
            .map_err(|_| crate::errors::AppError::Validation("Invalid user ID format".into()))?;
        self.authorization_service
            .user_has_role(&user_uuid, role)
            .await
    }

    /// Get user service for internal operations (DIP compliant)
    fn user_service(&self) -> Arc<dyn UserServiceTrait> {
        Arc::clone(&self.user_service)
    }

    // --- OAUTH METHODS ---

    async fn generate_oauth_url(
        &self,
        provider: &str,
    ) -> Result<(OAuthAuthUrlResponse, OAuthState)> {
        self.oauth_service.generate_oauth_url(provider).await
    }

    async fn oauth_callback(
        &self,
        provider: &str,
        code: &str,
        received_state_csrf: &str,
        stored_state: OAuthState,
    ) -> Result<LoginResponse> {
        self.oauth_service
            .oauth_callback(provider, code, received_state_csrf, stored_state)
            .await
    }
}
