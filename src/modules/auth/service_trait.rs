use crate::{
    errors::Result,
    modules::auth::{
        models::{LoginRequest, LoginResponse, RegisterRequest, TokenClaims},
        oauth_providers::{OAuthAuthUrlResponse, OAuthState},
    },
    modules::user::traits::service_traits::UserServiceTrait,
};
use async_trait::async_trait;
use std::sync::Arc;

/// AuthService trait for Dependency Inversion Principle (DIP)
#[async_trait]
pub trait AuthServiceTrait: Send + Sync {
    /// Register new user with default member role
    async fn register(&self, request: RegisterRequest) -> Result<LoginResponse>;

    /// Login user
    async fn login(&self, request: LoginRequest) -> Result<LoginResponse>;

    /// Refresh access token using refresh token
    async fn refresh_token(&self, refresh_token: &str) -> Result<LoginResponse>;

    /// Verify token and extract claims (sync method)
    fn verify_token(&self, token: &str) -> Result<TokenClaims>;

    /// Generate token from claims (for testing purposes)
    fn generate_token_from_claims(&self, claims: &TokenClaims) -> Result<String>;

    /// Extract user ID from Authorization header (sync method)
    fn extract_user_id_from_header(&self, auth_header: Option<&str>) -> Result<String>;

    /// Check if user has specific permission
    async fn user_has_permission(&self, user_id: &str, permission: &str) -> Result<bool>;

    /// Check if user has specific role
    async fn user_has_role(&self, user_id: &str, role: &str) -> Result<bool>;

    /// Get user service for internal operations (DIP compliant)
    fn user_service(&self) -> Arc<dyn UserServiceTrait>;

    // --- OAUTH METHODS ---

    /// Generate OAuth authorization URL for a given provider
    async fn generate_oauth_url(
        &self,
        provider: &str,
    ) -> Result<(OAuthAuthUrlResponse, OAuthState)>;

    /// Handle OAuth callback from the provider
    async fn oauth_callback(
        &self,
        provider: &str,
        code: &str,
        received_state_csrf: &str,
        stored_state: OAuthState,
    ) -> Result<LoginResponse>;
}
