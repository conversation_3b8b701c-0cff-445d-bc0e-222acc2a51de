use crate::{
    errors::{AppError, Result},
    modules::auth::{
        models::{LoginRequest, LoginResponse, RefreshTokenRequest, RegisterRequest},
        oauth_providers::OAuthAuthUrlResponse,
        service_trait::AuthServiceTrait,
        services::SessionStorage,
    },
    response::{created_response, success_response},
    utils::response_helpers::response_constants::auth,
};
use axum::{
    Json,
    extract::{Path, Query, State},
    response::IntoResponse,
};
use serde::Deserialize;
use std::sync::Arc;
use utoipa::{IntoParams, OpenApi};

#[derive(OpenApi)]
#[openapi(
    paths(
        login,
        register,
        refresh_token,
        oauth_login,
        oauth_callback
    ),
    components(schemas(
        super::models::LoginRequest,
        super::models::RegisterRequest,
        super::models::LoginResponse,
        super::models::UserInfo,
        super::models::RefreshTokenRequest,
        OAuthAuthUrlResponse,
        crate::response::ApiResponse<super::models::LoginResponse>,
        crate::response::ApiResponse<serde_json::Value>,
    )),
    tags((name = "Auth", description = "Authentication and authorization endpoints"))
)]
pub struct ApiDoc;

#[derive(Debug, Deserialize, IntoParams)]
pub struct AuthRedirectQuery {
    code: String,
    state: String,
}

/// State container for generic OAuth handlers
#[derive(Clone)]
pub struct OAuthHandlerState {
    pub auth_service: Arc<dyn AuthServiceTrait>,
    pub session_storage: Arc<SessionStorage>,
}

/// User registration
#[utoipa::path(
    post,
    path = "/api/auth/register",
    request_body = RegisterRequest,
    responses(
        (status = 201, description = "User registered successfully", body = crate::response::ApiResponse<LoginResponse>),
        (status = 400, description = "Invalid input", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 409, description = "User already exists", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Auth"
)]
pub async fn register(
    State(auth_service): State<Arc<dyn AuthServiceTrait>>,
    Json(request): Json<RegisterRequest>,
) -> Result<impl IntoResponse> {
    let auth_response = auth_service.register(request).await?;
    Ok(created_response(
        "/api/auth/register".to_string(),
        auth::SUCCESS_REGISTER,
        auth::MSG_REGISTER,
        auth_response,
    ))
}

/// User login
#[utoipa::path(
    post,
    path = "/api/auth/login",
    request_body = LoginRequest,
    responses(
        (status = 200, description = "Login successful", body = crate::response::ApiResponse<LoginResponse>),
        (status = 400, description = "Invalid input", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Invalid credentials", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Auth"
)]
pub async fn login(
    State(auth_service): State<Arc<dyn AuthServiceTrait>>,
    Json(request): Json<LoginRequest>,
) -> Result<impl IntoResponse> {
    let auth_response = auth_service.login(request).await?;
    Ok(success_response(
        "/api/auth/login".to_string(),
        auth::SUCCESS_LOGIN,
        auth::MSG_LOGIN,
        auth_response,
    ))
}

/// Refresh JWT token
#[utoipa::path(
    post,
    path = "/api/auth/refresh",
    request_body = RefreshTokenRequest,
    responses(
        (status = 200, description = "Token refreshed successfully", body = crate::response::ApiResponse<LoginResponse>),
        (status = 400, description = "Invalid input", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 401, description = "Invalid refresh token", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Auth"
)]
pub async fn refresh_token(
    State(auth_service): State<Arc<dyn AuthServiceTrait>>,
    Json(request): Json<RefreshTokenRequest>,
) -> Result<impl IntoResponse> {
    let auth_response = auth_service.refresh_token(&request.refresh_token).await?;
    Ok(success_response(
        "/api/auth/refresh".to_string(),
        auth::SUCCESS_REFRESH,
        auth::MSG_REFRESH,
        auth_response,
    ))
}

// --- OAUTH HANDLERS ---

/// Redirect to the OAuth provider's consent page
#[utoipa::path(
    get,
    path = "/api/auth/oauth/{provider}",
    params(
        ("provider" = String, Path, description = "The name of the OAuth provider (e.g., 'google')")
    ),
    responses(
        (status = 200, description = "OAuth URL generated successfully", body = crate::response::ApiResponse<OAuthAuthUrlResponse>),
        (status = 400, description = "Invalid provider", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Auth"
)]
pub async fn oauth_login(
    State(state): State<OAuthHandlerState>,
    Path(provider): Path<String>,
) -> Result<impl IntoResponse> {
    let (response, oauth_state) = state.auth_service.generate_oauth_url(&provider).await?;
    state
        .session_storage
        .store_oauth_state(oauth_state.csrf_token.clone(), oauth_state)
        .await?;

    // Return JSON response for frontend to handle redirection
    Ok(success_response(
        format!("/api/auth/oauth/{provider}"),
        auth::SUCCESS_OAUTH_URL,
        auth::MSG_OAUTH_URL,
        response,
    ))
}

/// Handle the OAuth provider's callback
#[utoipa::path(
    get,
    path = "/api/auth/oauth/{provider}/callback",
    params(
        ("provider" = String, Path, description = "The name of the OAuth provider"),
        AuthRedirectQuery
    ),
    responses(
        (status = 200, description = "OAuth login successful", body = crate::response::ApiResponse<LoginResponse>),
        (status = 400, description = "Invalid OAuth state or code", body = crate::response::ApiResponse<serde_json::Value>),
        (status = 500, description = "Internal server error", body = crate::response::ApiResponse<serde_json::Value>)
    ),
    tag = "Auth"
)]
pub async fn oauth_callback(
    State(state): State<OAuthHandlerState>,
    Path(provider): Path<String>,
    Query(query): Query<AuthRedirectQuery>,
) -> Result<impl IntoResponse> {
    tracing::info!(
        "OAuth callback received for provider: {} with state: {}",
        provider,
        query.state
    );

    let stored_state = state
        .session_storage
        .get_oauth_state_with_grace(&query.state)
        .await?
        .ok_or_else(|| {
            tracing::warn!("OAuth state not found or expired: {}", query.state);
            AppError::Unauthorized("Invalid or expired OAuth state".into())
        })?;

    tracing::info!("OAuth state retrieved successfully, attempting callback");

    let auth_response = match state
        .auth_service
        .oauth_callback(&provider, &query.code, &query.state, stored_state)
        .await
    {
        Ok(response) => {
            tracing::info!(
                "OAuth login successful for user: {} via provider: {}",
                response.user.email,
                provider
            );
            response
        }
        Err(e) => {
            tracing::error!("OAuth callback failed: {}", e);
            return Err(e);
        }
    };

    Ok(success_response(
        format!("/api/auth/oauth/{provider}/callback"),
        auth::SUCCESS_LOGIN,
        "OAuth login successful",
        auth_response,
    ))
}
