use anyhow::Result;
use oauth2::{
    AuthUrl, AuthorizationCode, ClientId, ClientSecret, CsrfToken, PkceCodeChallenge,
    PkceCodeVerifier, RedirectUrl, Scope, TokenResponse, TokenUrl, basic::BasicClient,
};
use reqwest::Client;
use serde_json::Value;
use std::sync::Arc;
use tracing::{error, info, warn};

use crate::config::GoogleOAuthConfig;
use crate::modules::auth::models::{
    GoogleAuthUrlResponse, GoogleOAuthState, GoogleTokenResponse, GoogleUserInfo,
};

#[derive(Clone)]
pub struct GoogleOAuthService {
    config: GoogleOAuthConfig,
    client: BasicClient,
    http_client: Arc<Client>,
}

impl GoogleOAuthService {
    pub fn new(config: GoogleOAuthConfig) -> Result<Self> {
        let client = BasicClient::new(
            ClientId::new(config.client_id.clone()),
            Some(ClientSecret::new(config.client_secret.clone())),
            AuthUrl::new(config.auth_url.clone())?,
            Some(TokenUrl::new(config.token_url.clone())?),
        )
        .set_redirect_uri(RedirectUrl::new(config.redirect_uri.clone())?);

        let http_client = Arc::new(Client::new());

        Ok(Self {
            config,
            client,
            http_client,
        })
    }

    /// Generate authorization URL with PKCE for security
    pub fn generate_auth_url(&self) -> Result<(GoogleAuthUrlResponse, GoogleOAuthState)> {
        let (pkce_challenge, pkce_verifier) = PkceCodeChallenge::new_random_sha256();
        let (auth_url, csrf_token) = self
            .client
            .authorize_url(CsrfToken::new_random)
            .add_scope(Scope::new("openid".to_string()))
            .add_scope(Scope::new("email".to_string()))
            .add_scope(Scope::new("profile".to_string()))
            .set_pkce_challenge(pkce_challenge)
            .url();

        let state = GoogleOAuthState {
            csrf_token: csrf_token.secret().clone(),
            pkce_verifier: pkce_verifier.secret().clone(),
            timestamp: chrono::Utc::now(),
        };

        let response = GoogleAuthUrlResponse {
            auth_url: auth_url.to_string(),
            state: csrf_token.secret().clone(),
        };

        info!(
            "Generated Google OAuth authorization URL for redirect_uri: {}",
            self.config.redirect_uri
        );

        Ok((response, state))
    }

    /// Exchange authorization code for access token
    pub async fn exchange_code(
        &self,
        code: &str,
        state: &GoogleOAuthState,
    ) -> Result<GoogleTokenResponse> {
        let pkce_verifier = PkceCodeVerifier::new(state.pkce_verifier.clone());

        info!("Attempting to exchange authorization code with Google OAuth2");
        info!(
            "Code length: {}, State timestamp: {}",
            code.len(),
            state.timestamp
        );

        let token_result = self
            .client
            .exchange_code(AuthorizationCode::new(code.to_string()))
            .set_pkce_verifier(pkce_verifier)
            .request_async(oauth2::reqwest::async_http_client)
            .await
            .map_err(|e| {
                error!("Failed to exchange authorization code with Google: {}", e);
                anyhow::anyhow!("OAuth2 token exchange failed: {}", e)
            })?;

        let google_token = GoogleTokenResponse {
            access_token: token_result.access_token().secret().clone(),
            token_type: token_result.token_type().as_ref().to_string(),
            expires_in: token_result
                .expires_in()
                .map(|duration| duration.as_secs() as i64),
            refresh_token: token_result
                .refresh_token()
                .map(|token| token.secret().clone()),
            scope: token_result.scopes().map(|scopes| {
                scopes
                    .iter()
                    .map(|scope| scope.to_string())
                    .collect::<Vec<_>>()
                    .join(" ")
            }),
        };

        info!("Successfully exchanged authorization code for access token");
        info!("Token scopes: {:?}", google_token.scope);
        Ok(google_token)
    }

    /// Fetch user information from Google using access token
    pub async fn get_user_info(&self, access_token: &str) -> Result<GoogleUserInfo> {
        // Using simpler UserInfo API v2 instead of People API
        let url = "https://www.googleapis.com/oauth2/v2/userinfo";

        let response = self
            .http_client
            .get(url)
            .bearer_auth(access_token)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            error!(
                "Failed to fetch user info from Google UserInfo API: {} - {}",
                status, error_text
            );
            return Err(anyhow::anyhow!(
                "Failed to fetch user info: {} - {}",
                status,
                error_text
            ));
        }

        let user_data: Value = response.json().await?;

        info!(
            "Google UserInfo API response: {}",
            serde_json::to_string_pretty(&user_data).unwrap_or_default()
        );

        // Parse UserInfo API response format (simpler structure)
        let user_info = GoogleUserInfo {
            id: user_data["id"]
                .as_str()
                .ok_or_else(|| anyhow::anyhow!("Missing user ID in Google UserInfo API response"))?
                .to_string(),
            email: user_data["email"]
                .as_str()
                .ok_or_else(|| anyhow::anyhow!("Missing email in Google UserInfo API response"))?
                .to_string(),
            verified_email: user_data["verified_email"].as_bool().unwrap_or(false),
            name: user_data["name"]
                .as_str()
                .ok_or_else(|| anyhow::anyhow!("Missing name in Google UserInfo API response"))?
                .to_string(),
            given_name: user_data["given_name"].as_str().map(|s| s.to_string()),
            family_name: user_data["family_name"].as_str().map(|s| s.to_string()),
            picture: user_data["picture"].as_str().map(|s| s.to_string()),
            locale: user_data["locale"].as_str().map(|s| s.to_string()),
        };

        info!(
            "Successfully fetched user info from Google UserInfo API for email: {}",
            user_info.email
        );

        Ok(user_info)
    }

    /// Validate OAuth state to prevent CSRF attacks
    pub fn validate_state(&self, received_state: &str, stored_state: &GoogleOAuthState) -> bool {
        if received_state != stored_state.csrf_token {
            warn!("OAuth state validation failed: state mismatch");
            return false;
        }

        let now = chrono::Utc::now();
        let state_age = now.signed_duration_since(stored_state.timestamp);

        // State should be valid for maximum 10 minutes
        if state_age.num_minutes() > 10 {
            warn!(
                "OAuth state validation failed: state expired (age: {} minutes)",
                state_age.num_minutes()
            );
            return false;
        }

        true
    }
}
