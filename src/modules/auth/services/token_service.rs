use crate::{
    config::TokenConfig,
    errors::{AppError, Result},
    modules::auth::models::TokenClaims,
    modules::user::models::User,
};

use chrono::{Duration, Utc};
use jsonwebtoken::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation, decode, encode};

/// Service responsible for JWT token operations (SRP: Single Responsibility)
#[derive(Clone)]
pub struct TokenService {
    jwt_secret: String,
    token_config: TokenConfig,
}

impl TokenService {
    pub fn new(jwt_secret: String, token_config: TokenConfig) -> Self {
        Self {
            jwt_secret,
            token_config,
        }
    }

    /// Generate access token with roles and permissions
    pub fn generate_access_token(
        &self,
        user: &User,
        roles: &[String],
        permissions: &[String],
    ) -> Result<String> {
        let now = Utc::now();
        let expires_at = now + Duration::seconds(self.token_config.access_token_expiry_secs);

        let claims = TokenClaims {
            sub: user.id.to_string(),
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
            token_type: "access".to_string(),
            roles: roles.to_vec(),
            permissions: permissions.to_vec(),
            pver: user.permission_version,
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_bytes()),
        )
        .map_err(|_| AppError::Internal(anyhow::anyhow!("Failed to generate access token")))
    }

    /// Generate refresh token with roles and permissions
    pub fn generate_refresh_token(
        &self,
        user: &User,
        roles: &[String],
        permissions: &[String],
    ) -> Result<String> {
        let now = Utc::now();
        let expires_at = now + Duration::seconds(self.token_config.refresh_token_expiry_secs);

        let claims = TokenClaims {
            sub: user.id.to_string(),
            exp: expires_at.timestamp() as usize,
            iat: now.timestamp() as usize,
            token_type: "refresh".to_string(),
            roles: roles.to_vec(),
            permissions: permissions.to_vec(),
            pver: user.permission_version,
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_bytes()),
        )
        .map_err(|_| AppError::Internal(anyhow::anyhow!("Failed to generate refresh token")))
    }

    /// Get access token expiry in seconds for use in responses
    pub fn get_access_token_expiry(&self) -> i64 {
        self.token_config.access_token_expiry_secs
    }

    /// Verify token and extract claims
    pub fn verify_token(&self, token: &str) -> Result<TokenClaims> {
        let validation = Validation::new(Algorithm::HS256);

        decode::<TokenClaims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_bytes()),
            &validation,
        )
        .map(|data| data.claims)
        .map_err(|err| match err.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => AppError::TokenExpired,
            _ => AppError::Unauthorized("Invalid token".into()),
        })
    }

    /// Generate token from claims (for testing purposes)
    pub fn generate_token_from_claims(&self, claims: &TokenClaims) -> Result<String> {
        encode(
            &Header::default(),
            claims,
            &EncodingKey::from_secret(self.jwt_secret.as_bytes()),
        )
        .map_err(|_| AppError::Internal(anyhow::anyhow!("Failed to generate token from claims")))
    }

    /// Extract user ID from Authorization header
    pub fn extract_user_id_from_header(&self, auth_header: Option<&str>) -> Result<String> {
        let token = auth_header
            .and_then(|header| header.strip_prefix("Bearer "))
            .ok_or_else(|| {
                AppError::Unauthorized("Missing or invalid Authorization header".into())
            })?;

        let claims = self.verify_token(token)?;

        // Ensure it's an access token (not refresh)
        if claims.token_type != "access" {
            return Err(AppError::Unauthorized("Invalid token type".into()));
        }

        Ok(claims.sub)
    }
}
