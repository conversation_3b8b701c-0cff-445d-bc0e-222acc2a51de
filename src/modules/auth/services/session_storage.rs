use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration as StdDuration;
use tokio::sync::RwLock;
use tokio::time::timeout;
use tracing::{error, info, warn};

use crate::modules::auth::oauth_providers::OAuthState;

#[derive(Clone)]
pub struct SessionStorage {
    store: Arc<RwLock<HashMap<String, StoredSession>>>,
    lock_timeout: StdDuration,
}

#[derive(Debug, Clone)]
pub(super) struct StoredSession {
    data: OAuthState,
    expires_at: DateTime<Utc>,
    first_used: Option<DateTime<Utc>>,
}

/// Manager for SessionStorage with automatic cleanup task management
///
/// This struct provides automatic lifecycle management for the background cleanup task.
/// It ensures that the cleanup task is properly stopped when the manager is dropped,
/// preventing memory leaks that could occur with std::mem::forget().
///
/// # Example
/// ```rust
/// use crate::modules::auth::services::SessionStorageManager;
///
/// // Create manager with automatic cleanup
/// let manager = SessionStorageManager::new();
/// let storage = manager.storage();
///
/// // Use storage for OAuth operations
/// // ...
///
/// // Manager automatically stops cleanup task when dropped
/// ```
pub struct SessionStorageManager {
    storage: Arc<SessionStorage>,
    cleanup_handle: Option<tokio::task::JoinHandle<()>>,
}

impl Default for SessionStorageManager {
    fn default() -> Self {
        Self::new()
    }
}

impl SessionStorageManager {
    /// Create a new SessionStorageManager with automatic background cleanup
    pub fn new() -> Self {
        let storage = Arc::new(SessionStorage::new());
        let cleanup_handle = Some(storage.start_background_cleanup());

        info!("Created SessionStorageManager with background cleanup");
        Self {
            storage,
            cleanup_handle,
        }
    }

    /// Create a new SessionStorageManager with custom timeout
    pub fn with_timeout(lock_timeout: StdDuration) -> Self {
        let storage = Arc::new(SessionStorage::with_timeout(lock_timeout));
        let cleanup_handle = Some(storage.start_background_cleanup());

        info!("Created SessionStorageManager with custom timeout and background cleanup");
        Self {
            storage,
            cleanup_handle,
        }
    }

    /// Get a reference to the underlying SessionStorage
    pub fn storage(&self) -> Arc<SessionStorage> {
        self.storage.clone()
    }

    /// Stop the background cleanup task gracefully
    pub fn stop_cleanup(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
            info!("SessionStorage background cleanup task stopped");
        }
    }

    /// Check if cleanup task is still running
    pub fn is_cleanup_running(&self) -> bool {
        self.cleanup_handle.is_some()
    }
}

impl Drop for SessionStorageManager {
    fn drop(&mut self) {
        self.stop_cleanup();
    }
}

impl SessionStorage {
    pub fn new() -> Self {
        Self {
            store: Arc::new(RwLock::new(HashMap::new())),
            lock_timeout: StdDuration::from_secs(5), // 5-second timeout to prevent deadlocks
        }
    }

    pub fn with_timeout(lock_timeout: StdDuration) -> Self {
        Self {
            store: Arc::new(RwLock::new(HashMap::new())),
            lock_timeout,
        }
    }

    /// Store OAuth state with 10-minute expiration
    pub async fn store_oauth_state(&self, session_id: String, state: OAuthState) -> Result<()> {
        let expires_at = Utc::now() + Duration::minutes(10);
        let session = StoredSession {
            data: state,
            expires_at,
            first_used: None,
        };

        // Use timeout to prevent deadlocks
        match timeout(self.lock_timeout, self.store.write()).await {
            Ok(mut store) => {
                store.insert(session_id.clone(), session);
                info!("Stored OAuth state for session: {}", session_id);
                Ok(())
            }
            Err(_) => {
                error!("Timeout acquiring write lock for session storage");
                Err(anyhow::anyhow!(
                    "Timeout acquiring write lock - potential deadlock prevented"
                ))
            }
        }
    }

    /// Retrieve and remove OAuth state
    pub async fn get_and_remove_oauth_state(&self, session_id: &str) -> Result<Option<OAuthState>> {
        // Use timeout to prevent deadlocks
        match timeout(self.lock_timeout, self.store.write()).await {
            Ok(mut store) => {
                if let Some(session) = store.remove(session_id) {
                    // Check if session is expired
                    if Utc::now() > session.expires_at {
                        warn!("OAuth state expired for session: {}", session_id);
                        return Ok(None);
                    }

                    info!("Retrieved OAuth state for session: {}", session_id);
                    return Ok(Some(session.data));
                }

                warn!("OAuth state not found for session: {}", session_id);
                Ok(None)
            }
            Err(_) => {
                error!("Timeout acquiring write lock for session storage");
                Err(anyhow::anyhow!(
                    "Timeout acquiring write lock - potential deadlock prevented"
                ))
            }
        }
    }

    /// Retrieve OAuth state with grace period (allows multiple retrievals)
    pub async fn get_oauth_state_with_grace(&self, session_id: &str) -> Result<Option<OAuthState>> {
        match timeout(self.lock_timeout, self.store.write()).await {
            Ok(mut store) => {
                if let Some(session) = store.get_mut(session_id) {
                    // Check if session is expired
                    if Utc::now() > session.expires_at {
                        warn!("OAuth state expired for session: {}", session_id);
                        store.remove(session_id);
                        return Ok(None);
                    }

                    // Check if we're in grace period (30 seconds after first use)
                    let now = Utc::now();
                    if let Some(first_used) = session.first_used {
                        let grace_period = Duration::seconds(30);
                        if now.signed_duration_since(first_used) > grace_period {
                            warn!(
                                "OAuth state grace period expired for session: {}",
                                session_id
                            );
                            store.remove(session_id);
                            return Ok(None);
                        }
                    } else {
                        // Mark as first used
                        session.first_used = Some(now);
                    }

                    info!(
                        "Retrieved OAuth state for session: {} (grace period)",
                        session_id
                    );
                    return Ok(Some(session.data.clone()));
                }

                warn!("OAuth state not found for session: {}", session_id);
                Ok(None)
            }
            Err(_) => {
                error!("Timeout acquiring write lock for session storage");
                Err(anyhow::anyhow!(
                    "Timeout acquiring write lock - potential deadlock prevented"
                ))
            }
        }
    }

    /// Cleanup expired sessions
    pub async fn cleanup_expired(&self) -> Result<usize> {
        // Use timeout to prevent deadlocks
        match timeout(self.lock_timeout, self.store.write()).await {
            Ok(mut store) => {
                let now = Utc::now();

                let removed_sessions: Vec<_> = store
                    .extract_if(|_, session| session.expires_at <= now)
                    .collect();
                let removed_count = removed_sessions.len();

                if removed_count > 0 {
                    info!("Cleaned up {} expired OAuth sessions", removed_count);
                }

                Ok(removed_count)
            }
            Err(_) => {
                error!("Timeout acquiring write lock for cleanup operation");
                Err(anyhow::anyhow!(
                    "Timeout acquiring write lock - cleanup skipped to prevent deadlock"
                ))
            }
        }
    }

    /// Get session count (for monitoring)
    pub async fn session_count(&self) -> Result<usize> {
        // Use timeout to prevent deadlocks
        match timeout(self.lock_timeout, self.store.read()).await {
            Ok(store) => Ok(store.len()),
            Err(_) => {
                error!("Timeout acquiring read lock for session count");
                Err(anyhow::anyhow!(
                    "Timeout acquiring read lock - count operation failed"
                ))
            }
        }
    }

    /// Start background cleanup task to automatically remove expired sessions
    /// Returns a JoinHandle that can be used to stop the task
    pub fn start_background_cleanup(&self) -> tokio::task::JoinHandle<()> {
        let storage = self.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(StdDuration::from_secs(300)); // Cleanup every 5 minutes

            loop {
                interval.tick().await;

                match storage.cleanup_expired().await {
                    Ok(removed_count) => {
                        if removed_count > 0 {
                            info!(
                                "Background cleanup removed {} expired sessions",
                                removed_count
                            );
                        }
                    }
                    Err(e) => {
                        warn!("Background cleanup failed: {}", e);
                    }
                }
            }
        })
    }

    /// Force cleanup of all sessions (useful for testing and shutdown)
    pub async fn clear_all(&self) -> Result<usize> {
        match timeout(self.lock_timeout, self.store.write()).await {
            Ok(mut store) => {
                let count = store.len();
                store.clear();
                info!("Cleared all {} sessions from storage", count);
                Ok(count)
            }
            Err(_) => {
                error!("Timeout acquiring write lock for clear operation");
                Err(anyhow::anyhow!(
                    "Timeout acquiring write lock - clear operation failed"
                ))
            }
        }
    }
}

impl Default for SessionStorage {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::modules::auth::oauth_providers::OAuthState;

    #[tokio::test]
    async fn test_store_and_retrieve_oauth_state() {
        let storage = SessionStorage::new();
        let session_id = "test_session".to_string();

        let state = OAuthState {
            provider: "google".to_string(),
            csrf_token: "csrf_123".to_string(),
            pkce_verifier: Some("pkce_456".to_string()),
            timestamp: Utc::now(),
            custom_data: None,
        };

        // Store state
        storage
            .store_oauth_state(session_id.clone(), state.clone())
            .await
            .unwrap();

        // Retrieve state
        let retrieved = storage
            .get_and_remove_oauth_state(&session_id)
            .await
            .unwrap();
        assert!(retrieved.is_some());
        if let Some(retrieved) = retrieved {
            assert_eq!(retrieved.csrf_token, state.csrf_token);
        } else {
            panic!("Expected to retrieve OAuth state but got None");
        }

        // Should be removed after retrieval
        let second_retrieval = storage
            .get_and_remove_oauth_state(&session_id)
            .await
            .unwrap();
        assert!(second_retrieval.is_none());
    }

    #[tokio::test]
    async fn test_cleanup_expired_sessions() {
        let storage = SessionStorage::new();

        // Manually insert an expired session for testing purposes.
        // This bypasses `store_oauth_state`, which would set a fresh 10-minute expiration.
        let expired_state = OAuthState {
            provider: "google".to_string(),
            csrf_token: "expired".to_string(),
            pkce_verifier: Some("expired".to_string()),
            timestamp: Utc::now(), // This timestamp is for the OAuth state, not session expiry.
            custom_data: None,
        };
        let expired_session = StoredSession {
            data: expired_state,
            expires_at: Utc::now() - Duration::minutes(1), // Set to be expired.
            first_used: None,
        };
        storage
            .store
            .write()
            .await
            .insert("expired".to_string(), expired_session);

        // We expect one session to be present before cleanup.
        assert_eq!(storage.session_count().await.unwrap(), 1);

        // Cleanup should remove the expired session.
        let removed = storage.cleanup_expired().await.unwrap();
        assert_eq!(removed, 1, "Expected cleanup to remove 1 expired session");

        // No sessions should remain.
        assert_eq!(storage.session_count().await.unwrap(), 0);
    }

    #[tokio::test]
    async fn test_session_storage_manager() {
        // Test that SessionStorageManager properly manages cleanup task
        let mut manager = SessionStorageManager::new();

        // Verify cleanup task is running
        assert!(manager.is_cleanup_running());

        // Test that we can access the storage
        let storage = manager.storage();
        assert_eq!(storage.session_count().await.unwrap(), 0);

        // Test graceful shutdown
        manager.stop_cleanup();
        assert!(!manager.is_cleanup_running());
    }

    #[tokio::test]
    async fn test_session_storage_manager_drop() {
        // Test that cleanup task is automatically stopped when manager is dropped
        let manager = SessionStorageManager::new();
        assert!(manager.is_cleanup_running());

        // Manager will be dropped here, triggering Drop implementation
        // This should automatically stop the cleanup task
    }
}
