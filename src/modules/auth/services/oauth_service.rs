use crate::{
    errors::{AppError, Result},
    modules::{
        auth::models::{LoginResponse, TOKEN_TYPE, UserInfo},
        auth::oauth_providers::{
            OAuthAuthUrlResponse, OAuthProviderFactory, OAuthState, OAuthUserInfo,
        },
        auth::utils::{EmailEventPublisher, EmailEventPublisherMixin},
        email::{EmailEvent, EmailEventBus},
        user::traits::service_traits::UserServiceTrait,
    },
};
use async_trait::async_trait;
use std::sync::Arc;
use tracing::warn;

use super::{authorization_service::AuthorizationService, token_service::TokenService};

/// Service dedicated to OAuth authentication flows (SRP: Single Responsibility)
#[derive(Clone)]
pub struct OAuthService {
    user_service: Arc<dyn UserServiceTrait>,
    token_service: TokenService,
    authorization_service: AuthorizationService,
    provider_factory: OAuthProviderFactory,
    email_publisher: EmailEventPublisherMixin,
}

impl OAuthService {
    pub fn new(
        user_service: Arc<dyn UserServiceTrait>,
        token_service: TokenService,
        authorization_service: AuthorizationService,
    ) -> Self {
        Self {
            user_service,
            token_service,
            authorization_service,
            provider_factory: OAuthProviderFactory::new(),
            email_publisher: EmailEventPublisherMixin::new(),
        }
    }

    pub fn with_provider_factory(
        user_service: Arc<dyn UserServiceTrait>,
        token_service: TokenService,
        authorization_service: AuthorizationService,
        provider_factory: OAuthProviderFactory,
    ) -> Self {
        Self {
            user_service,
            token_service,
            authorization_service,
            provider_factory,
            email_publisher: EmailEventPublisherMixin::new(),
        }
    }

    /// Set email event bus for sending email notifications
    pub fn set_email_event_bus(&mut self, email_event_bus: Arc<EmailEventBus>) {
        self.email_publisher.set_email_event_bus(email_event_bus);
    }

    /// Register một OAuth provider
    pub fn register_provider(
        &mut self,
        provider: Arc<dyn crate::modules::auth::oauth_providers::OAuthProvider>,
    ) {
        self.provider_factory.register_provider(provider);
    }

    /// Get danh sách các provider đã được đăng ký
    pub fn get_supported_providers(&self) -> Vec<&str> {
        self.provider_factory.get_registered_providers()
    }

    /// Get danh sách các provider đã được cấu hình
    pub fn get_configured_providers(&self) -> Vec<&str> {
        self.provider_factory.get_configured_providers()
    }

    /// Generate OAuth authorization URL for a given provider
    pub async fn generate_oauth_url(
        &self,
        provider_name: &str,
    ) -> Result<(OAuthAuthUrlResponse, OAuthState)> {
        let provider = self
            .provider_factory
            .get_provider(provider_name)
            .ok_or_else(|| {
                warn!(
                    "Attempted to use unsupported OAuth provider: {}",
                    provider_name
                );
                AppError::BadRequest("Unsupported OAuth provider".into())
            })?;

        if !provider.is_configured() {
            warn!(
                "Attempted to use unconfigured OAuth provider: {}",
                provider_name
            );
            return Err(AppError::BadRequest(
                "OAuth provider is not configured".into(),
            ));
        }

        provider.generate_auth_url()
    }

    /// Handle OAuth callback from the provider
    pub async fn oauth_callback(
        &self,
        provider_name: &str,
        code: &str,
        received_state_csrf: &str,
        stored_state: OAuthState,
    ) -> Result<LoginResponse> {
        let provider = self
            .provider_factory
            .get_provider(provider_name)
            .ok_or_else(|| AppError::BadRequest("Unsupported OAuth provider".into()))?;

        // 1. Validate State
        if !provider.validate_state(received_state_csrf, &stored_state) {
            return Err(AppError::Unauthorized("Invalid OAuth state".into()));
        }

        // 2. Exchange Code for Token
        let token_response = match provider.exchange_code(code, &stored_state).await {
            Ok(response) => response,
            Err(e) => {
                // Check if this is a "code already used" error from Google
                let error_msg = e.to_string().to_lowercase();
                let error_chain = format!("{e:?}").to_lowercase();

                if error_msg.contains("server returned error response")
                    || error_msg.contains("invalid_grant")
                    || error_msg.contains("authorization code")
                    || error_msg.contains("oauth2 token exchange failed")
                    || error_chain.contains("server returned error response")
                {
                    tracing::warn!("OAuth authorization code already used or invalid: {}", e);
                    return Err(AppError::BadRequest("OAuth authorization code has already been used or is invalid. Please try logging in again.".into()));
                }
                // For other errors, propagate as-is
                return Err(AppError::Internal(anyhow::anyhow!(
                    "OAuth token exchange failed: {}",
                    e
                )));
            }
        };

        // 3. Get User Info
        let user_info = provider.get_user_info(&token_response.access_token).await?;

        // 4. Login or Register User
        self.oauth_login(user_info).await
    }

    /// Helper method to create LoginResponse from user (DRY: Eliminates code duplication)
    async fn create_login_response(
        &self,
        user_with_roles: crate::modules::user::models::UserWithRoles,
    ) -> Result<LoginResponse> {
        // Get user roles and permissions for JWT
        let (roles, permissions) = self
            .authorization_service
            .get_user_roles_and_permissions(&user_with_roles.id)
            .await?;

        // Generate tokens with roles and permissions
        let user = user_with_roles.to_user();
        let access_token = self
            .token_service
            .generate_access_token(&user, &roles, &permissions)?;
        let refresh_token =
            self.token_service
                .generate_refresh_token(&user, &roles, &permissions)?;

        let access_token_expiry = self.token_service.get_access_token_expiry();
        let expires_at = chrono::Utc::now() + chrono::Duration::seconds(access_token_expiry);

        // Create user info
        let user_info = UserInfo::from(user_with_roles);

        Ok(LoginResponse {
            access_token,
            refresh_token,
            token_type: TOKEN_TYPE.to_string(),
            expires_in: access_token_expiry,
            expires_at,
            user: user_info,
        })
    }

    /// Generic OAuth login/register flow cho bất kỳ provider nào
    pub async fn oauth_login(&self, oauth_user: OAuthUserInfo) -> Result<LoginResponse> {
        // Check if user exists by email
        match self.user_service.get_user_by_email(&oauth_user.email).await {
            Ok(existing_user) => {
                self.handle_existing_oauth_user(existing_user.id, oauth_user.email)
                    .await
            }
            Err(crate::errors::AppError::NotFound(_)) => {
                self.handle_new_oauth_user(oauth_user).await
            }
            Err(e) => {
                tracing::error!("OAuth: Database error while checking user: {}", e);
                Err(e)
            }
        }
    }

    /// Handle authentication for existing OAuth user
    async fn handle_existing_oauth_user(
        &self,
        user_id: uuid::Uuid,
        email: String,
    ) -> Result<LoginResponse> {
        tracing::info!("OAuth: Existing user found for email: {}", email);

        // Get user with roles
        let user_with_roles = self.user_service.get_user_by_id(&user_id).await?;

        // Create login response using helper method (DRY)
        self.create_login_response(user_with_roles).await
    }

    /// Handle authentication for new OAuth user
    async fn handle_new_oauth_user(&self, oauth_user: OAuthUserInfo) -> Result<LoginResponse> {
        tracing::info!(
            "OAuth: Creating new user for email: {} from provider: {}",
            oauth_user.email,
            oauth_user.provider
        );

        // Create user via user_service, which now accepts generic OAuthUserInfo
        let user_with_roles = self.user_service.create_oauth_user(&oauth_user).await?;

        // Publish welcome email event for new OAuth user using shared utility (DRY)
        self.publish_email_event(EmailEvent::WelcomeEmail {
            user_id: user_with_roles.id.to_string(),
            email: user_with_roles.email.clone(),
            name: user_with_roles.fullname.clone(),
        })
        .await;

        // Create login response using helper method (DRY)
        self.create_login_response(user_with_roles).await
    }
}

#[async_trait]
impl EmailEventPublisher for OAuthService {
    fn get_email_event_bus(&self) -> &Option<Arc<EmailEventBus>> {
        self.email_publisher.get_email_event_bus()
    }

    async fn publish_email_event(&self, event: EmailEvent) {
        self.email_publisher.publish_email_event(event).await;
    }
}
