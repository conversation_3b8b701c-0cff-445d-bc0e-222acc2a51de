use crate::{
    errors::Result,
    modules::{
        role::service_trait::RoleServiceTrait, user::traits::service_traits::UserServiceTrait,
    },
};
use std::sync::Arc;
use uuid::Uuid;

/// Service responsible for authorization, roles and permissions (SRP: Single Responsibility)
#[derive(Clone)]
pub struct AuthorizationService {
    user_service: Arc<dyn UserServiceTrait>,
    role_service: Arc<dyn RoleServiceTrait>,
}

impl AuthorizationService {
    pub fn new(
        user_service: Arc<dyn UserServiceTrait>,
        role_service: Arc<dyn RoleServiceTrait>,
    ) -> Self {
        Self {
            user_service,
            role_service,
        }
    }

    /// Get user roles and their associated permissions
    pub async fn get_user_roles_and_permissions(
        &self,
        user_id: &Uuid,
    ) -> Result<(Vec<String>, Vec<String>)> {
        // Get user role IDs
        let role_ids = self.user_service.get_user_roles(user_id).await?;
        if role_ids.is_empty() {
            return Ok((vec![], vec![]));
        }

        // Get role details and permissions in bulk
        let roles = self.role_service.get_roles_by_ids(&role_ids).await?;

        let mut role_names = Vec::with_capacity(roles.len());
        let mut all_permissions = std::collections::HashSet::new();

        let permissions_map = self
            .role_service
            .list_permissions_for_roles(&role_ids)
            .await?;

        for role in roles {
            role_names.push(role.name.clone());
            if let Some(permissions) = permissions_map.get(&role.id) {
                for p in permissions {
                    all_permissions.insert(p.name.clone());
                }
            }
            if role.name == "super_admin" {
                all_permissions.insert("admin:all".to_string());
            }
        }

        Ok((role_names, all_permissions.into_iter().collect()))
    }

    /// Check if user has specific permission
    pub async fn user_has_permission(&self, user_id: &Uuid, permission: &str) -> Result<bool> {
        let (_, permissions) = self.get_user_roles_and_permissions(user_id).await?;

        // Check for admin:all permission or specific permission
        Ok(permissions.contains(&"admin:all".to_string())
            || permissions.contains(&permission.to_string()))
    }

    /// Check if user has specific role
    pub async fn user_has_role(&self, user_id: &Uuid, role: &str) -> Result<bool> {
        let (roles, _) = self.get_user_roles_and_permissions(user_id).await?;
        Ok(roles.contains(&role.to_string()))
    }

    /// Get all permissions for a user (flattened from all roles)
    pub async fn get_user_permissions(&self, user_id: &Uuid) -> Result<Vec<String>> {
        let (_, permissions) = self.get_user_roles_and_permissions(user_id).await?;
        Ok(permissions)
    }

    /// Get all roles for a user
    pub async fn get_user_roles(&self, user_id: &Uuid) -> Result<Vec<String>> {
        let (roles, _) = self.get_user_roles_and_permissions(user_id).await?;
        Ok(roles)
    }
}
