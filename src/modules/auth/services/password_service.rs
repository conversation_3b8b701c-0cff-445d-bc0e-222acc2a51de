use crate::errors::{AppError, Result};
use anyhow;
use argon2::{
    Argon2,
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString, rand_core::OsRng},
};

/// Configuration for Argon2 password hashing
/// Optimized for balance between security and performance
#[derive(Clone, Debug)]
pub struct PasswordConfig {
    pub memory_cost: u32,
    pub time_cost: u32,
    pub parallelism: u32,
}

impl Default for PasswordConfig {
    fn default() -> Self {
        Self {
            memory_cost: 12288,
            time_cost: 3,
            parallelism: 1,
        }
    }
}

impl PasswordConfig {
    pub fn high_security() -> Self {
        Self {
            memory_cost: 65536,
            time_cost: 4,
            parallelism: 2,
        }
    }

    pub fn fast() -> Self {
        Self {
            memory_cost: 4096,
            time_cost: 1,
            parallelism: 1,
        }
    }
}

/// Service for handling password hashing and verification asynchronously
/// This moves CPU-intensive operations to background threads to avoid blocking
#[derive(Clone)]
pub struct PasswordService {
    config: PasswordConfig,
}

impl PasswordService {
    pub fn new() -> Self {
        Self {
            config: PasswordConfig::default(),
        }
    }

    pub fn with_config(config: PasswordConfig) -> Self {
        Self { config }
    }

    pub async fn hash_password(&self, password: &str) -> Result<String> {
        let password = password.to_string();
        let config = self.config.clone();

        // Move CPU-intensive work to blocking thread
        let hash = tokio::task::spawn_blocking(move || -> Result<String> {
            let salt = SaltString::generate(&mut OsRng);
            let argon2 = Argon2::new(
                argon2::Algorithm::Argon2id,
                argon2::Version::V0x13,
                argon2::Params::new(
                    config.memory_cost,
                    config.time_cost,
                    config.parallelism,
                    None,
                )
                .map_err(|e| AppError::Internal(anyhow::anyhow!("Invalid Argon2 params: {}", e)))?,
            );

            let password_hash = argon2
                .hash_password(password.as_bytes(), &salt)
                .map_err(|e| {
                    AppError::Internal(anyhow::anyhow!("Failed to hash password: {}", e))
                })?;

            Ok(password_hash.to_string())
        })
        .await
        .map_err(|e| AppError::Internal(anyhow::anyhow!("Task join error: {}", e)))??;

        Ok(hash)
    }

    pub async fn verify_password(&self, password: &str, hash: &str) -> Result<bool> {
        let password = password.to_string();
        let hash = hash.to_string();

        // Move CPU-intensive work to blocking thread
        let is_valid = tokio::task::spawn_blocking(move || -> Result<bool> {
            let parsed_hash = PasswordHash::new(&hash)
                .map_err(|e| AppError::Internal(anyhow::anyhow!("Invalid password hash: {}", e)))?;

            let argon2 = Argon2::default();
            let result = argon2.verify_password(password.as_bytes(), &parsed_hash);

            Ok(result.is_ok())
        })
        .await
        .map_err(|e| AppError::Internal(anyhow::anyhow!("Task join error: {}", e)))??;

        Ok(is_valid)
    }

    pub fn config(&self) -> &PasswordConfig {
        &self.config
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_password_hashing_and_verification() {
        let service = PasswordService::new();
        let password = "test_password_123";

        // Test hashing
        let hash = service.hash_password(password).await.unwrap();
        assert!(!hash.is_empty());
        assert!(hash.starts_with("$argon2id$"));

        // Test verification with correct password
        let is_valid = service.verify_password(password, &hash).await.unwrap();
        assert!(is_valid);

        // Test verification with wrong password
        let is_invalid = service
            .verify_password("wrong_password", &hash)
            .await
            .unwrap();
        assert!(!is_invalid);
    }

    #[tokio::test]
    async fn test_different_configurations() {
        let fast_service = PasswordService::with_config(PasswordConfig::fast());
        let secure_service = PasswordService::with_config(PasswordConfig::high_security());

        let password = "test_password";

        // Both should work but with different performance characteristics
        let fast_hash = fast_service.hash_password(password).await.unwrap();
        let secure_hash = secure_service.hash_password(password).await.unwrap();

        // Both should verify correctly
        assert!(
            fast_service
                .verify_password(password, &fast_hash)
                .await
                .unwrap()
        );
        assert!(
            secure_service
                .verify_password(password, &secure_hash)
                .await
                .unwrap()
        );

        // Hashes should be different due to different salts and potentially different parameters
        assert_ne!(fast_hash, secure_hash);
    }
}
