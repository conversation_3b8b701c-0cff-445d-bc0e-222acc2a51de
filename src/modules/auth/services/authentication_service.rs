use crate::{
    errors::{AppError, Result},
    modules::{
        auth::models::{LoginRequest, LoginResponse, RegisterRequest, TOKEN_TYPE, UserInfo},
        user::{
            database::repository::DynUserRepo, models::requests::CreateUserRequest,
            traits::service_traits::UserServiceTrait,
        },
    },
};

use chrono::{Duration, Utc};
use std::sync::Arc;
use uuid;
use validator::Validate;

use super::{
    authorization_service::AuthorizationService,
    password_service::{PasswordConfig, PasswordService},
    token_service::TokenService,
};

/// Service responsible for authentication operations (SRP: Single Responsibility)
#[derive(Clone)]
pub struct AuthenticationService {
    user_service: Arc<dyn UserServiceTrait>,
    user_repo: DynUserRepo,
    token_service: TokenService,
    authorization_service: AuthorizationService,
    password_service: PasswordService,
}

impl AuthenticationService {
    pub fn new(
        user_service: Arc<dyn UserServiceTrait>,
        user_repo: DynUserRepo,
        token_service: TokenService,
        authorization_service: AuthorizationService,
    ) -> Self {
        // Create password service with environment-appropriate configuration
        let password_config = if cfg!(debug_assertions) {
            PasswordConfig::fast() // Fast for development
        } else {
            PasswordConfig::default() // Secure for production
        };
        let password_service = PasswordService::with_config(password_config);

        Self {
            user_service,
            user_repo,
            token_service,
            authorization_service,
            password_service,
        }
    }

    /// Helper method to create LoginResponse from user (DRY: Eliminates code duplication)
    async fn create_login_response(
        &self,
        user_with_roles: crate::modules::user::models::UserWithRoles,
    ) -> Result<LoginResponse> {
        // Get user roles and permissions for JWT
        let (roles, permissions) = self
            .authorization_service
            .get_user_roles_and_permissions(&user_with_roles.id)
            .await?;

        // Generate tokens with roles and permissions
        let user = user_with_roles.to_user();
        let access_token = self
            .token_service
            .generate_access_token(&user, &roles, &permissions)?;
        let refresh_token =
            self.token_service
                .generate_refresh_token(&user, &roles, &permissions)?;

        let access_token_expiry = self.token_service.get_access_token_expiry();
        let expires_at = Utc::now() + Duration::seconds(access_token_expiry);

        // Create user info
        let user_info = UserInfo::from(user_with_roles);

        Ok(LoginResponse {
            access_token,
            refresh_token,
            token_type: TOKEN_TYPE.to_string(),
            expires_in: access_token_expiry,
            expires_at,
            user: user_info,
        })
    }

    /// Register new user with default member role
    pub async fn register(&self, request: RegisterRequest) -> Result<LoginResponse> {
        // Validate request
        request
            .validate()
            .map_err(|e| AppError::Validation(e.to_string()))?;

        // Hash password using password service
        let password_hash = self
            .password_service
            .hash_password(&request.password)
            .await?;

        // Create user request for user service
        let create_user_request = CreateUserRequest {
            email: request.email,
            username: request.username,
            fullname: request.fullname,
            password: password_hash,
            role_names: None, // Will be assigned automatically as member
        };

        // Use user service to create user
        let user_with_roles = self.user_service.create_user(create_user_request).await?;

        // Create login response using helper method (DRY)
        self.create_login_response(user_with_roles).await
    }

    /// Login user
    pub async fn login(&self, request: LoginRequest) -> Result<LoginResponse> {
        // Validate request
        request
            .validate()
            .map_err(|e| AppError::Validation(e.to_string()))?;

        // Find user by email
        let user_data = self
            .user_repo
            .find_by_email_raw(&request.email)
            .await?
            .ok_or_else(|| AppError::Unauthorized("Invalid email or password".into()))?;

        // Verify password
        let password_hash = user_data.password_hash.as_ref().ok_or_else(|| {
            AppError::Unauthorized("OAuth users cannot login with password".to_string())
        })?;

        let is_valid = self
            .password_service
            .verify_password(&request.password, password_hash)
            .await?;

        if !is_valid {
            return Err(AppError::Unauthorized("Invalid email or password".into()));
        }

        // Convert to domain user model and get user with roles
        let user = crate::modules::user::models::User::from(user_data);
        let user_with_roles = self.user_service.get_user_by_id(&user.id).await?;

        // Create login response using helper method (DRY)
        self.create_login_response(user_with_roles).await
    }

    /// Refresh access token using refresh token
    pub async fn refresh_token(&self, refresh_token: &str) -> Result<LoginResponse> {
        // Verify refresh token
        let claims = self.token_service.verify_token(refresh_token)?;

        // Ensure it's a refresh token
        if claims.token_type != "refresh" {
            return Err(AppError::Unauthorized("Invalid token type".into()));
        }

        // Get updated user information with roles
        let user_id = uuid::Uuid::parse_str(&claims.sub)
            .map_err(|_| AppError::Unauthorized("Invalid user ID in token".into()))?;
        let user_with_roles = self.user_service.get_user_by_id(&user_id).await?;

        // Create login response using helper method (DRY)
        self.create_login_response(user_with_roles).await
    }
}
