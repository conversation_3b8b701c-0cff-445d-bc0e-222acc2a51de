use crate::modules::email::{EmailEvent, EmailEventBus};
use async_trait::async_trait;
use std::sync::Arc;

#[async_trait]
pub trait EmailEventPublisher: Send + Sync {
    fn get_email_event_bus(&self) -> &Option<Arc<EmailEventBus>>;

    async fn publish_email_event(&self, event: EmailEvent);
}

/// Helper struct for services that need email event publishing capabilities
#[derive(Clone)]
pub struct EmailEventPublisherMixin {
    pub email_event_bus: Option<Arc<EmailEventBus>>,
}

impl Default for EmailEventPublisherMixin {
    fn default() -> Self {
        Self::new()
    }
}

impl EmailEventPublisherMixin {
    pub fn new() -> Self {
        Self {
            email_event_bus: None,
        }
    }

    pub fn set_email_event_bus(&mut self, email_event_bus: Arc<EmailEventBus>) {
        tracing::info!("Setting email event bus in EmailEventPublisherMixin");
        self.email_event_bus = Some(email_event_bus);
    }
}

#[async_trait]
impl EmailEventPublisher for EmailEventPublisherMixin {
    fn get_email_event_bus(&self) -> &Option<Arc<EmailEventBus>> {
        &self.email_event_bus
    }

    async fn publish_email_event(&self, event: EmailEvent) {
        if let Some(email_bus) = self.get_email_event_bus() {
            tracing::info!(
                "Publishing email event: {} for user: {}",
                event.get_event_type(),
                event.get_user_id()
            );
            if let Err(e) = email_bus.publish(event).await {
                tracing::error!("Failed to publish email event: {}", e);
            } else {
                tracing::info!("Successfully published email event to Redis");
            }
        } else {
            tracing::error!(
                "Email event bus not configured! Cannot publish email event: {} for user: {}",
                event.get_event_type(),
                event.get_user_id()
            );
        }
    }
}
