use crate::errors::Result;
use crate::modules::category::models::{
    CategoryPaginationRequest, CategoryWithTracking, PaginatedCategoriesWithTracking,
    UpdateCategory,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Clone)]
pub struct CreateCategoryParams {
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: crate::modules::category::models::CategoryType,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
}

#[async_trait]
pub trait CategoryRepositoryTrait: Send + Sync {
    async fn create(&self, params: CreateCategoryParams) -> Result<CategoryWithTracking>;
    async fn get_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking>;
    async fn get_by_slug_and_type(
        &self,
        slug: &str,
        category_type: &str,
    ) -> Result<CategoryWithTracking>;
    async fn update(&self, id: &Uuid, update_data: UpdateCategory) -> Result<CategoryWithTracking>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn get_all(&self) -> Result<Vec<CategoryWithTracking>>;
    async fn get_by_type(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>>;
    async fn get_active(&self) -> Result<Vec<CategoryWithTracking>>;
    async fn get_with_pagination(
        &self,
        request: CategoryPaginationRequest,
    ) -> Result<PaginatedCategoriesWithTracking>;
    async fn exists_by_name_and_type(
        &self,
        name: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool>;
    async fn exists_by_slug_and_type(
        &self,
        slug: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool>;
}

pub type DynCategoryRepo = Arc<dyn CategoryRepositoryTrait>;
