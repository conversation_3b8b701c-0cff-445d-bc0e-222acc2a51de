use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SqlxCategory {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: crate::modules::category::models::CategoryType,
    pub is_active: bool,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxCategory> for crate::modules::category::models::CategoryWithTracking {
    fn from(category: SqlxCategory) -> Self {
        Self {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            category_type: category.category_type,
            is_active: category.is_active,
            created_by: Some(category.created_by),
            updated_by: category.updated_by,
            created_at: category.created_at,
            updated_at: category.updated_at,
        }
    }
}
