use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::category::database::models::SqlxCategory,
    modules::category::models::{
        CategoryPaginationRequest, CategoryWithTracking, PaginatedCategoriesWithTracking,
        UpdateCategory,
    },
    repository::base_repository::AsyncRepository,
    schema::queries,
    utils::pagination::PaginationMeta,
};
use async_trait::async_trait;
use uuid::Uuid;

use super::repository_trait::CategoryRepositoryTrait;

// ===== SQLX CATEGORY REPOSITORY =====

#[derive(Clone)]
pub struct SqlxCategoryRepository {
    base: AsyncRepository,
}

impl SqlxCategoryRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl CategoryRepositoryTrait for SqlxCategoryRepository {
    async fn create(
        &self,
        params: super::repository_trait::CreateCategoryParams,
    ) -> Result<CategoryWithTracking> {
        let id = Uuid::new_v4();
        let created_at = chrono::Utc::now();
        let updated_at = chrono::Utc::now();
        let category_type = params.category_type;
        let created_by = params
            .created_by
            .ok_or_else(|| AppError::BadRequest("created_by is required".into()))?;

        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let category = sqlx::query_as::<_, SqlxCategory>(queries::CREATE_CATEGORY)
                        .bind(id)
                        .bind(&params.name)
                        .bind(&params.slug)
                        .bind(params.description.as_ref())
                        .bind(category_type)
                        .bind(params.is_active)
                        .bind(created_by)
                        .bind(Option::<Uuid>::None)
                        .bind(created_at)
                        .bind(updated_at)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(CategoryWithTracking::from(category))
                })
            })
            .await
    }

    async fn get_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking> {
        let id = *id;
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let category = sqlx::query_as::<_, SqlxCategory>(queries::FIND_CATEGORY_BY_ID)
                        .bind(id)
                        .fetch_optional(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    match category {
                        Some(cat) => Ok(CategoryWithTracking::from(cat)),
                        None => Err(AppError::NotFound("Category not found".to_string())),
                    }
                })
            })
            .await
    }

    async fn get_by_slug_and_type(
        &self,
        slug: &str,
        category_type: &str,
    ) -> Result<CategoryWithTracking> {
        let slug = slug.to_string();
        let category_type: crate::modules::category::models::CategoryType =
            category_type.parse().map_err(|_| {
                AppError::BadRequest(format!("Invalid category type: {category_type}"))
            })?;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let category =
                        sqlx::query_as::<_, SqlxCategory>(queries::FIND_CATEGORY_BY_SLUG_AND_TYPE)
                            .bind(&slug)
                            .bind(category_type)
                            .fetch_optional(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    match category {
                        Some(cat) => Ok(CategoryWithTracking::from(cat)),
                        None => Err(AppError::NotFound("Category not found".to_string())),
                    }
                })
            })
            .await
    }

    async fn update(&self, id: &Uuid, update_data: UpdateCategory) -> Result<CategoryWithTracking> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let category = sqlx::query_as::<_, SqlxCategory>(queries::UPDATE_CATEGORY)
                        .bind(id)
                        .bind(&update_data.name)
                        .bind(&update_data.description)
                        .bind(update_data.is_active)
                        .bind(update_data.updated_by)
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(CategoryWithTracking::from(category))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(|pool| {
                Box::pin(async move {
                    let result = sqlx::query(queries::DELETE_CATEGORY)
                        .bind(id)
                        .execute(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    if result.rows_affected() == 0 {
                        return Err(AppError::NotFound("Category not found".to_string()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn get_all(&self) -> Result<Vec<CategoryWithTracking>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let categories =
                        sqlx::query_as::<_, SqlxCategory>(queries::FIND_ALL_CATEGORIES)
                            .fetch_all(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(categories
                        .into_iter()
                        .map(CategoryWithTracking::from)
                        .collect())
                })
            })
            .await
    }

    async fn get_by_type(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>> {
        let category_type: crate::modules::category::models::CategoryType =
            category_type.parse().map_err(|_| {
                AppError::BadRequest(format!("Invalid category type: {category_type}"))
            })?;

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let categories =
                        sqlx::query_as::<_, SqlxCategory>(queries::FIND_CATEGORIES_BY_TYPE)
                            .bind(category_type)
                            .fetch_all(pool)
                            .await
                            .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(categories
                        .into_iter()
                        .map(CategoryWithTracking::from)
                        .collect())
                })
            })
            .await
    }

    async fn get_active(&self) -> Result<Vec<CategoryWithTracking>> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let categories = sqlx::query_as::<_, SqlxCategory>(
                        "SELECT * FROM categories WHERE is_active = true ORDER BY created_at DESC",
                    )
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(categories
                        .into_iter()
                        .map(CategoryWithTracking::from)
                        .collect())
                })
            })
            .await
    }

    async fn get_with_pagination(
        &self,
        request: CategoryPaginationRequest,
    ) -> Result<PaginatedCategoriesWithTracking> {
        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let offset = (request.page - 1) * request.limit;
                    let limit = request.limit;

                    let categories = sqlx::query_as::<_, SqlxCategory>(
                        "SELECT * FROM categories ORDER BY created_at DESC LIMIT $1 OFFSET $2",
                    )
                    .bind(limit)
                    .bind(offset)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    let total_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM categories")
                        .fetch_one(pool)
                        .await
                        .map_err(|e| AppError::Internal(e.into()))?;

                    let categories: Vec<CategoryWithTracking> = categories
                        .into_iter()
                        .map(CategoryWithTracking::from)
                        .collect();

                    let pagination_meta =
                        PaginationMeta::new(request.page, request.limit, total_count);

                    Ok(PaginatedCategoriesWithTracking {
                        categories,
                        meta: pagination_meta,
                    })
                })
            })
            .await
    }

    async fn exists_by_name_and_type(
        &self,
        name: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool> {
        let name = name.to_string();
        let category_type: crate::modules::category::models::CategoryType =
            category_type.parse().map_err(|_| {
                AppError::BadRequest(format!("Invalid category type: {category_type}"))
            })?;
        let exclude_id = exclude_id.copied().unwrap_or_else(Uuid::new_v4);

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let count: i64 = sqlx::query_scalar(
                        "SELECT COUNT(*) FROM categories WHERE name = $1 AND category_type = $2 AND id != $3"
                    )
                    .bind(&name)
                    .bind(category_type)
                    .bind(exclude_id)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(count > 0)
                })
            })
            .await
    }

    async fn exists_by_slug_and_type(
        &self,
        slug: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool> {
        let slug = slug.to_string();
        let category_type: crate::modules::category::models::CategoryType =
            category_type.parse().map_err(|_| {
                AppError::BadRequest(format!("Invalid category type: {category_type}"))
            })?;
        let exclude_id = exclude_id.copied().unwrap_or_else(Uuid::new_v4);

        self.base
            .execute_readonly(|pool| {
                Box::pin(async move {
                    let count: i64 = sqlx::query_scalar(
                        "SELECT COUNT(*) FROM categories WHERE slug = $1 AND category_type = $2 AND id != $3"
                    )
                    .bind(&slug)
                    .bind(category_type)
                    .bind(exclude_id)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::Internal(e.into()))?;

                    Ok(count > 0)
                })
            })
            .await
    }
}
