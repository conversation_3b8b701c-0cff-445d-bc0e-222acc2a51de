use crate::errors::{AppError, Result};
use crate::modules::category::database::{
    CreateCategoryParams, DynCategoryRepo, SqlxCategoryRepository,
};
use crate::modules::category::models::{
    Category, CategoryPaginationRequest, CategoryWithTracking, CreateCategoryRequest,
    PaginatedCategories, PaginatedCategoriesWithTracking, UpdateCategory, UpdateCategoryRequest,
};
use crate::modules::category::traits::{
    CategoryManagementTrait, CategoryQueryTrait, CategoryValidationTrait,
};
use crate::modules::redis::RedisServiceTrait;
use crate::utils::{
    cache_helpers::{CacheKeyGenerator, CacheOperations, CacheTTL},
    validation::helpers::validate_enhanced_and_execute,
};
use async_trait::async_trait;
use chrono::Utc;
use std::{sync::Arc, time::Duration};
use uuid::Uuid;

#[derive(Clone)]
pub struct CategoryService {
    repository: DynCategoryRepo,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
}

impl CategoryService {
    pub fn new(
        repository: DynCategoryRepo,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        Self {
            repository,
            redis_service,
        }
    }

    pub fn new_with_sqlx(
        database: crate::database::Database,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
    ) -> Self {
        Self {
            repository: Arc::new(SqlxCategoryRepository::new(database)),
            redis_service,
        }
    }

    /// Generate cache key for categories by type
    fn categories_by_type_cache_key(category_type: &str) -> String {
        CacheKeyGenerator::entity_list_by_type("categories", category_type)
    }

    /// Cache TTL for category lists (1 hour)
    fn category_lists_ttl() -> Duration {
        CacheTTL::entity_lists()
    }
}

// Implement CategoryQueryTrait
#[async_trait]
impl CategoryQueryTrait for CategoryService {
    async fn get_category_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking> {
        self.repository.get_by_id(id).await
    }

    async fn get_category_by_slug_and_type(
        &self,
        slug: &str,
        category_type: &str,
    ) -> Result<CategoryWithTracking> {
        self.repository
            .get_by_slug_and_type(slug, category_type)
            .await
    }

    async fn get_all_categories(&self) -> Result<Vec<Category>> {
        let categories_with_tracking = self.repository.get_all().await?;
        Ok(categories_with_tracking
            .into_iter()
            .map(|c| Category {
                id: c.id,
                name: c.name,
                slug: c.slug,
                description: c.description,
                category_type: c.category_type,
                is_active: c.is_active,
                created_by: c.created_by.unwrap_or_default(),
                updated_by: c.updated_by,
                created_at: c.created_at,
                updated_at: c.updated_at,
            })
            .collect())
    }

    async fn get_all_categories_with_tracking(&self) -> Result<Vec<CategoryWithTracking>> {
        self.repository.get_all().await
    }

    async fn get_categories_by_type(&self, category_type: &str) -> Result<Vec<Category>> {
        // Try to get from cache first
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::categories_by_type_cache_key(category_type);
            if let Ok(Some(cached_json)) = redis.get(&cache_key).await {
                if let Ok(categories) = serde_json::from_str::<Vec<Category>>(&cached_json) {
                    CacheOperations::log_cache_hit("Categories by type", category_type);
                    return Ok(categories);
                }
            }
            CacheOperations::log_cache_miss("Categories by type", category_type);
        }

        // Get from database
        let categories = self.repository.get_by_type(category_type).await?;

        // Cache the result
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::categories_by_type_cache_key(category_type);
            let ttl = Self::category_lists_ttl();
            if let Ok(json) = serde_json::to_string(&categories) {
                if let Err(e) = redis.set(&cache_key, &json, Some(ttl)).await {
                    CacheOperations::log_cache_set_failure(
                        "Categories by type",
                        category_type,
                        &e.to_string(),
                    );
                } else {
                    CacheOperations::log_cache_set(
                        "Categories by type",
                        category_type,
                        ttl.as_secs(),
                    );
                }
            }
        }

        Ok(categories
            .into_iter()
            .map(|c| Category {
                id: c.id,
                name: c.name,
                slug: c.slug,
                description: c.description,
                category_type: c.category_type,
                is_active: c.is_active,
                created_by: c.created_by.unwrap_or_default(),
                updated_by: c.updated_by,
                created_at: c.created_at,
                updated_at: c.updated_at,
            })
            .collect())
    }

    async fn get_categories_by_type_with_tracking(
        &self,
        category_type: &str,
    ) -> Result<Vec<CategoryWithTracking>> {
        self.repository.get_by_type(category_type).await
    }

    async fn get_categories_with_pagination(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategories> {
        let result = self.repository.get_with_pagination(pagination).await?;
        Ok(PaginatedCategories {
            categories: result
                .categories
                .into_iter()
                .map(|c| Category {
                    id: c.id,
                    name: c.name,
                    slug: c.slug,
                    description: c.description,
                    category_type: c.category_type,
                    is_active: c.is_active,
                    created_by: c.created_by.unwrap_or_default(),
                    updated_by: c.updated_by,
                    created_at: c.created_at,
                    updated_at: c.updated_at,
                })
                .collect(),
            meta: result.meta,
        })
    }

    async fn get_categories_with_pagination_and_tracking(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategoriesWithTracking> {
        self.repository.get_with_pagination(pagination).await
    }
}

// Implement CategoryManagementTrait
#[async_trait]
impl CategoryManagementTrait for CategoryService {
    async fn create_category(
        &self,
        request: CreateCategoryRequest,
        created_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        // Validate request using enhanced validation
        validate_enhanced_and_execute(request, |request| async move {
            // Check for duplicate name within the same category type
            self.validate_category_name_unique(
                &request.name,
                &request.category_type.to_string(),
                None,
            )
            .await?;

            // Check for duplicate slug within the same category type
            self.validate_category_slug_unique(
                &request.slug,
                &request.category_type.to_string(),
                None,
            )
            .await?;

            // Create category
            let params = CreateCategoryParams {
                name: request.name,
                slug: request.slug,
                description: request.description,
                category_type: request.category_type,
                is_active: true, // New categories are active by default
                created_by: Some(*created_by),
            };

            self.repository.create(params).await
        })
        .await
    }

    async fn update_category(
        &self,
        id: &Uuid,
        request: UpdateCategoryRequest,
        updated_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        // Validate request using enhanced validation
        validate_enhanced_and_execute(request, |request| async move {
            // Check if category exists
            let existing_category = self.repository.get_by_id(id).await?;

            // If name is being updated, check for duplicates within the same category type
            if let Some(ref new_name) = request.name {
                self.validate_category_name_unique(
                    new_name,
                    &existing_category.category_type.to_string(),
                    Some(id),
                )
                .await?;
            }

            // Update category
            let update_data = UpdateCategory {
                name: request.name,
                description: request.description,
                is_active: request.is_active,
                updated_by: Some(*updated_by),
                updated_at: Utc::now(),
            };

            self.repository.update(id, update_data).await
        })
        .await
    }

    async fn delete_category(&self, id: &Uuid) -> Result<()> {
        // Check if category exists before deletion
        self.validate_category_exists(id).await?;

        // TODO: Check if category is being used by any items (laptops, etc.)
        // This should be implemented when we add the laptop module

        self.repository.delete(id).await
    }

    async fn toggle_category_status(
        &self,
        id: &Uuid,
        updated_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        // Get current category
        let current_category = self.repository.get_by_id(id).await?;

        // Toggle the status
        let update_data = UpdateCategory {
            name: None,
            description: None,
            is_active: Some(!current_category.is_active),
            updated_by: Some(*updated_by),
            updated_at: Utc::now(),
        };

        self.repository.update(id, update_data).await
    }
}

// Implement CategoryValidationTrait
#[async_trait]
impl CategoryValidationTrait for CategoryService {
    async fn validate_category_name_unique(
        &self,
        name: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<()> {
        let exists = self
            .repository
            .exists_by_name_and_type(name, category_type, exclude_id)
            .await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Category with name '{name}' already exists in category type '{category_type}'"
            )));
        }
        Ok(())
    }

    async fn validate_category_slug_unique(
        &self,
        slug: &str,
        category_type: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<()> {
        let exists = self
            .repository
            .exists_by_slug_and_type(slug, category_type, exclude_id)
            .await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Category with slug '{slug}' already exists in category type '{category_type}'"
            )));
        }
        Ok(())
    }

    async fn validate_category_exists(&self, id: &Uuid) -> Result<()> {
        // This will return NotFound error if category doesn't exist
        self.repository.get_by_id(id).await?;
        Ok(())
    }
}
