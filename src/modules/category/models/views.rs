use crate::utils::pagination::PaginationMeta;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

use super::domain::{Category, CategoryWithTracking};

// ===== VIEW MODELS FOR PUBLIC/PRIVATE API PATTERN =====

/// Public view for categories - limited information for unauthenticated users
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true
}))]
pub struct CategoryPublicView {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
}

/// Private view for categories - full information for authenticated users
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true,
    "created_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "updated_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "created_at": "2025-07-12T01:31:59.286900Z",
    "updated_at": "2025-07-12T01:31:59.286900Z"
}))]
pub struct CategoryPrivateView {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Paginated categories for public API
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct PaginatedCategoriesPublicView {
    pub categories: Vec<CategoryPublicView>,
    pub meta: PaginationMeta,
}

/// Paginated categories for private API  
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct PaginatedCategoriesPrivateView {
    pub categories: Vec<CategoryPrivateView>,
    pub meta: PaginationMeta,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<Category> for CategoryPublicView {
    fn from(category: Category) -> Self {
        Self {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            category_type: category.category_type.to_string(),
            is_active: category.is_active,
        }
    }
}

impl From<CategoryWithTracking> for CategoryPublicView {
    fn from(category: CategoryWithTracking) -> Self {
        Self {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            category_type: category.category_type.to_string(),
            is_active: category.is_active,
        }
    }
}

impl From<CategoryWithTracking> for CategoryPrivateView {
    fn from(category: CategoryWithTracking) -> Self {
        Self {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            category_type: category.category_type.to_string(),
            is_active: category.is_active,
            created_by: category.created_by,
            updated_by: category.updated_by,
            created_at: category.created_at,
            updated_at: category.updated_at,
        }
    }
}
