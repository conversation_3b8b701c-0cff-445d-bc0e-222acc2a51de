use crate::utils::validation::{ValidateRequestEnhanced, ValidationResult};
use crate::utils::{validate_required_string, validate_string_length};

use super::requests::{CreateCategoryRequest, UpdateCategoryRequest};

// ===== VALIDATION IMPLEMENTATION =====

impl ValidateRequestEnhanced for CreateCategoryRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Validate name
        validate_required_string(&self.name, "name", &mut result);

        // Validate slug
        validate_required_string(&self.slug, "slug", &mut result);

        // Validate slug format (alphanumeric, hyphens, underscores only)
        if !self
            .slug
            .chars()
            .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
        {
            result.add_error(
                "slug",
                "INVALID_FORMAT",
                "Slug can only contain alphanumeric characters, hyphens, and underscores",
            );
        }

        // Category type is already validated by enum type, no additional validation needed

        // Validate description if provided
        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 0, 500, &mut result);
        }

        result
    }
}

impl ValidateRequestEnhanced for UpdateCategoryRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Validate name if provided
        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut result);
        }

        // Validate description if provided
        if let Some(Some(ref desc)) = self.description {
            validate_string_length(desc, "description", 0, 500, &mut result);
        }

        result
    }
}
