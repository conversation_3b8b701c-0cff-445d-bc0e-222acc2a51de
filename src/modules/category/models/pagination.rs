use crate::utils::pagination::{Pagination<PERSON><PERSON>, PaginationRequest};
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};

use super::domain::{Category, CategoryWithTracking};

// ===== PAGINATION MODELS =====

#[derive(Debug, <PERSON>lone, Deserialize, Serialize, ToSchema, IntoParams, Default)]
pub struct CategoryPaginationRequest {
    #[serde(default = "default_page")]
    pub page: i64,
    #[serde(default = "default_limit")]
    pub limit: i64,

    /// Filter by category type
    pub category_type: Option<String>,

    /// Filter by active status
    pub is_active: Option<bool>,

    /// Search by name or description
    pub search: Option<String>,
}

impl PaginationRequest for CategoryPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "categories": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "Gaming Laptops",
            "slug": "gaming-laptops",
            "description": "High-performance laptops for gaming",
            "category_type": "laptops",
            "is_active": true,
            "created_at": "2025-07-12T01:31:59.286900Z",
            "updated_at": "2025-07-12T01:31:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedCategories {
    pub categories: Vec<Category>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedCategoriesWithTracking {
    pub categories: Vec<CategoryWithTracking>,
    pub meta: PaginationMeta,
}

// ===== HELPER FUNCTIONS =====

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    20
}
