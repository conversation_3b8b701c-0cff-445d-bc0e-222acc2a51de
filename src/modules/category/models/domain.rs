use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

// ===== ENUMS =====

#[derive(
    Debug,
    <PERSON>lone,
    Copy,
    PartialEq,
    Eq,
    Serialize,
    Deserialize,
    ToSchema,
    sqlx::Type,
    async_graphql::Enum,
)]
#[sqlx(type_name = "category_type_enum")]
pub enum CategoryType {
    #[serde(rename = "laptop")]
    #[sqlx(rename = "laptop")]
    Laptop,
    #[serde(rename = "accessory")]
    #[sqlx(rename = "accessory")]
    Accessory,
    #[serde(rename = "software")]
    #[sqlx(rename = "software")]
    Software,
}

impl std::fmt::Display for CategoryType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CategoryType::Laptop => write!(f, "laptop"),
            CategoryType::Accessory => write!(f, "accessory"),
            CategoryType::Software => write!(f, "software"),
        }
    }
}

impl std::str::FromStr for CategoryType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "laptop" => Ok(CategoryType::Laptop),
            "accessory" => Ok(CategoryType::Accessory),
            "software" => Ok(CategoryType::Software),
            _ => Err(format!("Invalid category type: {s}")),
        }
    }
}

// ===== DOMAIN MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true,
    "created_at": "2025-07-12T01:31:59.286900Z",
    "updated_at": "2025-07-12T01:31:59.286900Z"
}))]
pub struct Category {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: CategoryType,
    pub is_active: bool,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true,
    "created_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "updated_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "created_at": "2025-07-12T01:31:59.286900Z",
    "updated_at": "2025-07-12T01:31:59.286900Z"
}))]
pub struct CategoryWithTracking {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: CategoryType,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct UpdateCategory {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub is_active: Option<bool>,
    pub updated_by: Option<Uuid>,
    pub updated_at: DateTime<Utc>,
}
