use serde::Deserialize;
use utoipa::ToSchema;
use validator::Validate;

// ===== REQUEST MODELS =====

#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops"
}))]
pub struct CreateCategoryRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Category name must be between 2 and 50 characters"
    ))]
    pub name: String,

    #[validate(length(
        min = 2,
        max = 60,
        message = "Category slug must be between 2 and 60 characters"
    ))]
    pub slug: String,

    #[validate(length(max = 500, message = "Description must not exceed 500 characters"))]
    pub description: Option<String>,

    pub category_type: super::domain::CategoryType,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "name": "Updated Gaming Laptops",
    "description": "Updated description for gaming laptops",
    "is_active": false
}))]
pub struct UpdateCategoryRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Category name must be between 2 and 50 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 500, message = "Description must not exceed 500 characters"))]
    pub description: Option<Option<String>>,

    pub is_active: Option<bool>,
}
