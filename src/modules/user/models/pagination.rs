use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;

use super::domain::{User, UserWithRoles};

// ===== PAGINATION MODELS =====

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UserPaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
}

impl PaginationRequest for UserPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }
    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
}))]
pub struct PaginatedUsers {
    pub data: Vec<User>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "users": [
        {
            "id": "15ef5806-6578-46ca-b999-89c391079e7a",
            "username": "superadmin",
            "fullname": "Super Administrator",
            "email": "<EMAIL>",
            "roles": [
                "super_admin"
            ],
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedUsersWithRoles {
    pub users: Vec<UserWithRoles>,
    pub meta: PaginationMeta,
}
