use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

// ===== DOMAIN MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub email: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub oauth_provider: Option<String>,
    pub oauth_avatar_url: Option<String>,
    pub email_verified: bool,
    pub permission_version: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "15ef5806-6578-46ca-b999-89c391079e7a",
    "username": "superadmin",
    "fullname": "Super Administrator",
    "email": "<EMAIL>",
    "roles": [
        "super_admin"
    ],
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z",
    "oauth_provider": null,
    "oauth_avatar_url": null,
    "email_verified": true,
    "permission_version": 1
}))]
pub struct UserWithRoles {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub email: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub oauth_provider: Option<String>,
    pub oauth_avatar_url: Option<String>,
    pub email_verified: bool,
    pub permission_version: i32,
}

impl From<UserWithRoles> for User {
    fn from(user: UserWithRoles) -> Self {
        Self {
            id: user.id,
            username: user.username,
            fullname: user.fullname,
            email: user.email,
            created_at: user.created_at,
            updated_at: user.updated_at,
            oauth_provider: user.oauth_provider,
            oauth_avatar_url: user.oauth_avatar_url,
            email_verified: user.email_verified,
            permission_version: user.permission_version,
        }
    }
}

impl UserWithRoles {
    pub fn from_user_and_roles(user: User, roles: Vec<String>) -> Self {
        Self {
            id: user.id,
            username: user.username,
            fullname: user.fullname,
            email: user.email,
            roles,
            created_at: user.created_at,
            updated_at: user.updated_at,
            oauth_provider: user.oauth_provider,
            oauth_avatar_url: user.oauth_avatar_url,
            email_verified: user.email_verified,
            permission_version: user.permission_version,
        }
    }

    pub fn to_user(&self) -> User {
        User {
            id: self.id,
            username: self.username.clone(),
            fullname: self.fullname.clone(),
            email: self.email.clone(),
            created_at: self.created_at,
            updated_at: self.updated_at,
            oauth_provider: self.oauth_provider.clone(),
            oauth_avatar_url: self.oauth_avatar_url.clone(),
            email_verified: self.email_verified,
            permission_version: self.permission_version,
        }
    }
}
