use crate::{
    container::ServiceContainer,
    errors::AppError,
    modules::auth::service_trait::AuthServiceTrait,
    response::success_response,
    routes::middleware::{ApiType, auth::AuthenticatedUser},
    utils::response_helpers::response_constants::user as user_codes,
};
use axum::{Extension, extract::State};
use std::sync::Arc;
use uuid;

pub type SharedAuthService = Arc<dyn AuthServiceTrait>;

/// Get current user profile - Private API only
pub async fn get_profile(
    Extension(api_type): Extension<ApiType>,
    Extension(user): Extension<AuthenticatedUser>,
    State(container): State<Arc<ServiceContainer>>,
) -> Result<impl axum::response::IntoResponse, AppError> {
    // This endpoint only supports private API
    match api_type {
        ApiType::Private => {
            // User is already authenticated via middleware
            let auth_service = container.auth_service();
            let exp_system_service = container.exp_system_service();
            let title_service = container.title_service();

            // Get user from service (via auth_service)
            let user_uuid = uuid::Uuid::parse_str(&user.user_id)
                .map_err(|_| AppError::Unauthorized("Invalid user ID in token".into()))?;
            let user_data = auth_service
                .user_service()
                .get_user_by_id(&user_uuid)
                .await?;

            // Get progression info (tu tiên)
            let mut progression = match exp_system_service.get_user_level(&user_uuid).await {
                Ok(level) => level,
                Err(_) => {
                    // User chưa có level, tự động khởi tạo
                    exp_system_service.on_new_user_created(&user_uuid).await?;
                    exp_system_service.get_user_level(&user_uuid).await?
                }
            };

            // Get current title (danh hiệu hiện tại)
            let mut current_title = if let Some(title_id) = progression.current_title_id {
                title_service.get_title_by_id(title_id).await?
            } else {
                None
            };

            // BACKFILL: If user exists but has no title, try to assign the first unlocked one
            if current_title.is_none() {
                // We must re-check level 1 titles for existing users who might not have had them
                title_service
                    .check_and_unlock_titles_for_level(user_uuid, 1)
                    .await?;

                let unlocked_titles = title_service.get_user_unlocked_titles(user_uuid).await?;
                if let Some(first_title) = unlocked_titles.first() {
                    // Assign the first available title
                    progression = exp_system_service
                        .set_current_title(&user_uuid, first_title.id)
                        .await?;
                    current_title = Some(first_title.clone()); // Use the already fetched title object
                }
            }

            let profile = serde_json::json!({
                "user": user_data,
                "progression": progression,
                "current_title": current_title
            });

            Ok(success_response(
                "/api/profile/me".to_string(),
                user_codes::SUCCESS_GET,
                "Profile retrieved successfully",
                profile,
            ))
        }
        ApiType::Public => Err(AppError::Forbidden(
            "This endpoint requires private API access".into(),
        )),
    }
}
