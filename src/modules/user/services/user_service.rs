use crate::modules::auth::{
    oauth_providers::OAuthUserInfo,
    services::password_service::{PasswordConfig, PasswordService},
};
use crate::{
    config::CacheConfig,
    database::Database,
    errors::{AppError, Result},
    modules::{
        progression::exp_system::DynExpSystemService,
        redis::RedisServiceTrait,
        role::service_trait::RoleServiceTrait,
        user::{
            database::models::CreateUserParams,
            database::repository::DynUserRepo,
            database::user_role_repository::{DynUserRoleRepo, UserRoleInfo},
            models::{
                domain::{User, UserWithRoles},
                pagination::{PaginatedUsersWithRoles, UserPaginationRequest},
                requests::{CreateUserRequest, UpdateUserRequest},
            },
            traits::service_traits::{
                UserManagementTrait, UserOAuthTrait, UserQueryTrait, UserRoleTrait,
                UserServiceTrait,
            },
        },
    },
    utils::{E<PERSON>r<PERSON><PERSON><PERSON>, ValidateRequest},
};

use anyhow;
use async_trait::async_trait;
use lru::LruCache;
use std::collections::HashMap;
use std::num::NonZeroUsize;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use uuid::Uuid;

pub struct UserService {
    repo: DynUserRepo,
    user_role_repo: DynUserRoleRepo,
    role_service: Arc<dyn RoleServiceTrait>,
    #[allow(dead_code)]
    exp_system_service: DynExpSystemService,
    password_service: PasswordService,
    #[allow(dead_code)]
    database: Database,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
    cache_config: CacheConfig,
    // OPTIMIZATION: Local LRU cache for frequently accessed user data
    user_cache: Arc<Mutex<LruCache<Uuid, Arc<User>>>>,
    role_cache: Arc<Mutex<LruCache<Uuid, Arc<Vec<String>>>>>,
}

impl UserService {
    pub fn new(
        repo: DynUserRepo,
        user_role_repo: DynUserRoleRepo,
        role_service: Arc<dyn RoleServiceTrait>,
        exp_system_service: DynExpSystemService,
        database: Database,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
        cache_config: CacheConfig,
    ) -> Self {
        // Create password service with environment-appropriate configuration
        let password_config = if cfg!(debug_assertions) {
            PasswordConfig::fast() // Fast for development
        } else {
            PasswordConfig::default() // Secure for production
        };
        let password_service = PasswordService::with_config(password_config);

        // OPTIMIZATION: Initialize local LRU caches with configurable sizes
        let user_cache_size = NonZeroUsize::new(1000).unwrap(); // Cache 1000 users
        let role_cache_size = NonZeroUsize::new(2000).unwrap(); // Cache 2000 role lists

        Self {
            repo,
            user_role_repo,
            role_service,
            exp_system_service,
            password_service,
            database,
            redis_service,
            cache_config,
            user_cache: Arc::new(Mutex::new(LruCache::new(user_cache_size))),
            role_cache: Arc::new(Mutex::new(LruCache::new(role_cache_size))),
        }
    }

    /// Generate cache key for permission version
    fn permission_version_cache_key(_user_id: &Uuid) -> String {
        format!("user:perm_version:{_user_id}")
    }

    /// Cache TTL for permission version from config
    fn permission_version_ttl(&self) -> Duration {
        self.cache_config.permission_version_ttl()
    }

    /// Invalidate permission version cache for a user
    async fn invalidate_permission_version_cache(&self, _user_id: &Uuid) -> Result<()> {
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::permission_version_cache_key(_user_id);
            match redis.delete(&cache_key).await {
                Ok(deleted) => {
                    if deleted {
                        tracing::info!(
                            user_id = %_user_id,
                            cache_key = %cache_key,
                            "Successfully invalidated permission version cache"
                        );
                    } else {
                        tracing::debug!(
                            user_id = %_user_id,
                            cache_key = %cache_key,
                            "Cache key did not exist, no invalidation needed"
                        );
                    }
                }
                Err(e) => {
                    tracing::warn!(
                        user_id = %_user_id,
                        cache_key = %cache_key,
                        error = %e,
                        "Failed to invalidate permission version cache"
                    );
                    // Don't fail the operation if cache invalidation fails
                }
            }
        } else {
            tracing::debug!(
                user_id = %_user_id,
                "Redis not available, skipping cache invalidation"
            );
        }
        Ok(())
    }

    /// Helper for post-user-creation logic (DRY principle)
    async fn finalize_user_creation(
        &self,
        user: User,
        role_names_to_assign: Vec<String>,
    ) -> Result<UserWithRoles> {
        // 1. Assign roles
        if !role_names_to_assign.is_empty() {
            let roles = self
                .role_service
                .get_roles_by_names(&role_names_to_assign)
                .await?;
            if roles.len() != role_names_to_assign.len() {
                // OPTIMIZATION: Use references to avoid cloning role names
                let found_names: std::collections::HashSet<&str> =
                    roles.iter().map(|r| r.name.as_str()).collect();
                for name in &role_names_to_assign {
                    if !found_names.contains(name.as_str()) {
                        return Err(ErrorHelper::not_found_with_option("Role", Some(name)));
                    }
                }
            }
            let role_ids: Vec<Uuid> = roles.iter().map(|r| r.id).collect();
            self.user_role_repo
                .replace_roles_for_user(&user.id, &role_ids)
                .await?;
        }

        // 2. Initialize user level (temporarily disabled for debugging)
        // self.exp_system_service
        //     .on_new_user_created(&user.id)
        //     .await?;

        // 3. Return the final user object with roles
        Ok(UserWithRoles::from_user_and_roles(
            user,
            role_names_to_assign,
        ))
    }

    // Helper methods remain internal
    fn build_roles_map(roles_info: Vec<UserRoleInfo>) -> HashMap<Uuid, Vec<String>> {
        let mut roles_map: HashMap<Uuid, Vec<String>> = HashMap::new();
        for info in roles_info {
            roles_map
                .entry(info.user_id)
                .or_default()
                .push(info.role_name);
        }
        roles_map
    }

    // Helper to get role names for a user
    async fn get_user_role_names(&self, _user_id: &Uuid) -> Result<Vec<String>> {
        self.user_role_repo.get_role_names_for_user(_user_id).await
    }

    // OPTIMIZATION: Cache management methods
    async fn get_user_from_cache(&self, _user_id: &Uuid) -> Option<Arc<User>> {
        self.user_cache.lock().unwrap().get(_user_id).cloned()
    }

    async fn put_user_in_cache(&self, user: User) {
        let user_arc = Arc::new(user);
        self.user_cache
            .lock()
            .unwrap()
            .put(user_arc.id, Arc::clone(&user_arc));
    }

    async fn get_roles_from_cache(&self, _user_id: &Uuid) -> Option<Arc<Vec<String>>> {
        self.role_cache.lock().unwrap().get(_user_id).cloned()
    }

    async fn put_roles_in_cache(&self, _user_id: Uuid, roles: Vec<String>) {
        let roles_arc = Arc::new(roles);
        self.role_cache
            .lock()
            .unwrap()
            .put(_user_id, Arc::clone(&roles_arc));
    }

    async fn invalidate_user_cache(&self, _user_id: &Uuid) {
        self.user_cache.lock().unwrap().pop(_user_id);
        self.role_cache.lock().unwrap().pop(_user_id);
    }

    #[allow(dead_code)]
    async fn invalidate_all_caches(&self) {
        self.user_cache.lock().unwrap().clear();
        self.role_cache.lock().unwrap().clear();
    }

    // OPTIMIZATION: Get user roles with caching
    async fn get_user_roles_cached(&self, _user_id: &Uuid) -> Result<Arc<Vec<String>>> {
        // Check local cache first
        if let Some(cached_roles) = self.get_roles_from_cache(_user_id).await {
            return Ok(cached_roles);
        }

        // Check Redis cache
        if let Some(redis) = &self.redis_service {
            if let Ok(Some(cached_json)) = redis.get(&format!("user_roles:{_user_id}")).await {
                if let Ok(cached_roles) = serde_json::from_str::<Vec<String>>(&cached_json) {
                    self.put_roles_in_cache(*_user_id, cached_roles.clone())
                        .await;
                    return Ok(Arc::new(cached_roles));
                }
            }
        }

        // Database fallback
        let roles = self.get_user_role_names(_user_id).await?;
        self.put_roles_in_cache(*_user_id, roles.clone()).await;

        if let Some(redis) = &self.redis_service {
            if let Ok(roles_json) = serde_json::to_string(&roles) {
                let _ = redis
                    .set(
                        &format!("user_roles:{_user_id}"),
                        &roles_json,
                        Some(Duration::from_secs(300)),
                    )
                    .await;
            }
        }

        Ok(Arc::new(roles))
    }
}

// Implement UserQueryTrait
#[async_trait]
impl UserQueryTrait for UserService {
    async fn get_user_by_id(&self, _id: &Uuid) -> Result<UserWithRoles> {
        // OPTIMIZATION: Check local cache first (fastest - ~1μs)
        if let Some(cached_user) = self.get_user_from_cache(_id).await {
            let roles = self.get_user_roles_cached(_id).await?;
            return Ok(UserWithRoles::from_user_and_roles(
                (*cached_user).clone(),
                (*roles).clone(),
            ));
        }

        // Check Redis cache (medium speed - ~100μs)
        if let Some(redis) = &self.redis_service {
            if let Ok(Some(cached_json)) = redis.get(&format!("user:{_id}")).await {
                if let Ok(cached_user) = serde_json::from_str::<User>(&cached_json) {
                    // Cache locally and return
                    self.put_user_in_cache(cached_user.clone()).await;
                    let roles = self.get_user_roles_cached(_id).await?;
                    return Ok(UserWithRoles::from_user_and_roles(
                        cached_user,
                        (*roles).clone(),
                    ));
                }
            }
        }

        // Database fallback (slowest - ~1-10ms)
        let user =
            self.repo.find_by_id(_id).await?.ok_or_else(|| {
                ErrorHelper::not_found_with_option("User", Some(&_id.to_string()))
            })?;

        // Cache the result
        self.put_user_in_cache(user.clone()).await;

        if let Some(redis) = &self.redis_service {
            if let Ok(user_json) = serde_json::to_string(&user) {
                let _ = redis
                    .set(
                        &format!("user:{_id}"),
                        &user_json,
                        Some(Duration::from_secs(300)),
                    )
                    .await;
            }
        }

        let role_names = self.get_user_role_names(_id).await?;
        self.put_roles_in_cache(*_id, role_names.clone()).await;

        Ok(UserWithRoles::from_user_and_roles(user, role_names))
    }

    async fn get_user_by_email(&self, email: &str) -> Result<User> {
        let user = self
            .repo
            .find_by_email(email)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_email("User", email))?;
        Ok(user)
    }

    async fn get_user_by_username(&self, username: &str) -> Result<User> {
        let user = self
            .repo
            .find_by_username(username)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_username("User", username))?;
        Ok(user)
    }

    async fn get_all_users(&self) -> Result<Vec<UserWithRoles>> {
        // OPTIMIZATION: Use batch operations for better performance
        let users = self.repo.find_all().await?;
        let user_ids: Vec<Uuid> = users.iter().map(|u| u.id).collect();

        if user_ids.is_empty() {
            return Ok(vec![]);
        }

        // OPTIMIZATION: Batch fetch roles for all users in one query
        let roles_info = self.user_role_repo.get_roles_for_users(&user_ids).await?;
        let roles_map = Self::build_roles_map(roles_info);

        // OPTIMIZATION: Pre-allocate vector capacity for better memory efficiency
        let mut users_with_roles = Vec::with_capacity(users.len());

        for user in users {
            let roles = roles_map.get(&user.id).cloned().unwrap_or_default();
            users_with_roles.push(UserWithRoles::from_user_and_roles(user, roles));
        }

        Ok(users_with_roles)
    }

    async fn get_users_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsersWithRoles> {
        pagination.validate_request()?;

        let paginated_result = self.repo.find_all_with_pagination(pagination).await?;
        let user_ids: Vec<Uuid> = paginated_result.data.iter().map(|u| u.id).collect();

        // OPTIMIZATION: Batch fetch roles for all users in one query
        let roles_map = if !user_ids.is_empty() {
            let roles_info = self.user_role_repo.get_roles_for_users(&user_ids).await?;
            Self::build_roles_map(roles_info)
        } else {
            HashMap::new()
        };

        // OPTIMIZATION: Pre-allocate vector capacity for better memory efficiency
        let mut users_with_roles = Vec::with_capacity(paginated_result.data.len());

        for user in paginated_result.data {
            let roles = roles_map.get(&user.id).cloned().unwrap_or_default();
            users_with_roles.push(UserWithRoles::from_user_and_roles(user, roles));
        }

        Ok(PaginatedUsersWithRoles {
            users: users_with_roles,
            meta: paginated_result.meta,
        })
    }

    async fn get_permission_version(&self, _id: &Uuid) -> Result<Option<i32>> {
        // Implementation following REDIS_PVER_CACHE_PLAN:
        // 1. Try Redis cache first
        // 2. On cache miss, query database and update cache
        // 3. Handle edge cases (suspicious cache values, Redis failures)

        let cache_key = Self::permission_version_cache_key(_id);

        // Step 1: Try Redis cache first
        if let Some(redis) = &self.redis_service {
            match redis.get(&cache_key).await {
                Ok(Some(cached_value)) => {
                    match cached_value.parse::<i32>() {
                        Ok(version) => {
                            // Validate cached value is reasonable (edge case G)
                            if version >= 1 {
                                tracing::debug!(
                                    user_id = %_id,
                                    version = version,
                                    "Permission version cache hit"
                                );
                                return Ok(Some(version));
                            } else {
                                tracing::warn!(
                                    user_id = %_id,
                                    cached_value = %cached_value,
                                    "Suspicious cached permission version (< 1), falling back to DB"
                                );
                            }
                        }
                        Err(e) => {
                            tracing::warn!(
                                user_id = %_id,
                                cached_value = %cached_value,
                                error = %e,
                                "Failed to parse cached permission version, falling back to DB"
                            );
                        }
                    }
                }
                Ok(None) => {
                    tracing::debug!(
                        user_id = %_id,
                        "Permission version cache miss"
                    );
                }
                Err(e) => {
                    tracing::warn!(
                        user_id = %_id,
                        error = %e,
                        "Failed to read from Redis cache, falling back to DB"
                    );
                }
            }
        }

        // Step 2: Cache miss or invalid cache - query database
        let version = self.repo.get_permission_version(_id).await?;

        // Step 3: Update cache with fresh value from DB (best-effort)
        if let Some(version) = version {
            if let Some(redis) = &self.redis_service {
                let ttl = self.permission_version_ttl();
                match redis.set(&cache_key, &version.to_string(), Some(ttl)).await {
                    Ok(_) => tracing::debug!(
                        user_id = %_id,
                        version = version,
                        ttl_secs = ttl.as_secs(),
                        "Permission version cache updated after DB query"
                    ),
                    Err(e) => tracing::warn!(
                        user_id = %_id,
                        version = version,
                        ttl_secs = ttl.as_secs(),
                        error = %e,
                        "Failed to update permission version cache after DB query"
                    ),
                }
            }
        }

        Ok(version)
    }
}

// Implement UserManagementTrait
#[async_trait]
impl UserManagementTrait for UserService {
    async fn create_user(&self, request: CreateUserRequest) -> Result<UserWithRoles> {
        request.validate_request()?;

        // Hash password using password service
        let password_hash = self
            .password_service
            .hash_password(&request.password)
            .await?;

        // Create user using repository method signature
        // OPTIMIZATION: Use move semantics instead of cloning
        let now = chrono::Utc::now();
        let created_user = self
            .repo
            .create(CreateUserParams {
                id: uuid::Uuid::new_v4(),
                email: request.email,       // Move ownership instead of clone
                username: request.username, // Move ownership instead of clone
                fullname: request.fullname, // Move ownership instead of clone
                password_hash: Some(password_hash),
                otp: None,
                oauth_provider: None,
                oauth_provider_id: None,
                oauth_avatar_url: None,
                email_verified: false,
                created_at: now,
                updated_at: now,
                permission_version: 1,
            })
            .await?;

        // Determine roles and finalize creation
        let role_names_to_assign = request
            .role_names
            .unwrap_or_else(|| vec!["member".to_string()]);

        self.finalize_user_creation(created_user, role_names_to_assign)
            .await
    }

    async fn update_user(&self, _id: &Uuid, request: UpdateUserRequest) -> Result<UserWithRoles> {
        request.validate_request()?;

        // Hash password if provided
        let hashed_password = if let Some(password) = request.password {
            Some(self.password_service.hash_password(&password).await?)
        } else {
            None
        };

        // Update user using repository method signature
        let updated_user = self
            .repo
            .update(
                _id,
                request.email,
                request.username,
                request.fullname,
                hashed_password,
            )
            .await?;

        // OPTIMIZATION: Invalidate caches after update
        self.invalidate_user_cache(_id).await;

        // Get current roles
        let role_names = self.get_user_role_names(_id).await?;

        Ok(UserWithRoles::from_user_and_roles(updated_user, role_names))
    }

    async fn delete_user(&self, id: &Uuid) -> Result<()> {
        // First remove all roles for the user (cascade delete)
        self.user_role_repo.remove_all_roles_for_user(id).await?;

        // Then delete the user
        self.repo.delete(id).await?;

        // Clear cache entries for this user
        self.invalidate_permission_version_cache(id).await?;

        // Clear local cache
        {
            let mut user_cache = self.user_cache.lock().unwrap();
            user_cache.pop(id);
        }
        {
            let mut role_cache = self.role_cache.lock().unwrap();
            role_cache.pop(id);
        }

        tracing::info!("User {} deleted successfully", id);
        Ok(())
    }
}

// Implement UserRoleTrait
#[async_trait]
impl UserRoleTrait for UserService {
    async fn get_user_roles(&self, _user_id: &Uuid) -> Result<Vec<Uuid>> {
        self.user_role_repo.get_user_roles(_user_id).await
    }

    async fn user_has_role(&self, _user_id: &Uuid, _role_id: &Uuid) -> Result<bool> {
        self.user_role_repo.user_has_role(_user_id, _role_id).await
    }

    async fn assign_role_to_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        // Verify the role exists
        let _ = self.role_service.get_role_by_id(role_id).await?;

        // Verify the user exists
        self.repo
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| AppError::NotFound("User not found".into()))?;

        // Assign the role
        self.user_role_repo
            .assign_role_to_user(user_id, role_id)
            .await?;

        // Clear cache entries for this user
        self.invalidate_permission_version_cache(user_id).await?;

        // Clear local role cache for this user
        {
            let mut role_cache = self.role_cache.lock().unwrap();
            role_cache.pop(user_id);
        }

        tracing::info!("Role {} assigned to user {} successfully", role_id, user_id);
        Ok(())
    }

    async fn remove_role_from_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        // Verify the user exists
        self.repo
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| AppError::NotFound("User not found".into()))?;

        // Check if user has the role before removing
        let has_role = self.user_role_repo.user_has_role(user_id, role_id).await?;
        if !has_role {
            return Err(AppError::NotFound("User does not have this role".into()));
        }

        // Remove the role
        self.user_role_repo
            .remove_role_from_user(user_id, role_id)
            .await?;

        // Clear cache entries for this user
        self.invalidate_permission_version_cache(user_id).await?;

        // Clear local role cache for this user
        {
            let mut role_cache = self.role_cache.lock().unwrap();
            role_cache.pop(user_id);
        }

        tracing::info!(
            "Role {} removed from user {} successfully",
            role_id,
            user_id
        );
        Ok(())
    }

    async fn update_user_roles(&self, user_id: &Uuid, role_ids: &[Uuid]) -> Result<UserWithRoles> {
        // Verify the user exists
        let user = self
            .repo
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| AppError::NotFound("User not found".into()))?;

        // Verify all roles exist
        for role_id in role_ids {
            let _ = self.role_service.get_role_by_id(role_id).await?;
        }

        // Replace all roles for the user (this handles add/remove in one transaction)
        self.user_role_repo
            .replace_roles_for_user(user_id, role_ids)
            .await?;

        // Clear cache entries for this user
        self.invalidate_permission_version_cache(user_id).await?;

        // Clear local role cache for this user
        {
            let mut role_cache = self.role_cache.lock().unwrap();
            role_cache.pop(user_id);
        }

        // Get updated role names
        let role_names = self.get_user_role_names(user_id).await?;

        tracing::info!(
            "User {} roles updated successfully. New roles: {:?}",
            user_id,
            role_names
        );
        Ok(UserWithRoles::from_user_and_roles(user, role_names))
    }
}

// Implement UserOAuthTrait
#[async_trait]
impl UserOAuthTrait for UserService {
    async fn create_oauth_user(&self, oauth_user: &OAuthUserInfo) -> Result<UserWithRoles> {
        // Generate unique username from user's email
        let base_username = oauth_user.email.split('@').next().unwrap_or("user");
        let mut username = base_username.to_string();
        let mut counter = 1;

        // Note: We still need to check username uniqueness because we generate it dynamically
        while self.repo.exists_by_username(&username).await? {
            username = format!("{base_username}_{counter}");
            counter += 1;
        }

        // Create OAuth user - let repository handle email duplicates via database constraints
        // OPTIMIZATION: Use move semantics where possible, clone only when necessary
        let now = chrono::Utc::now();
        match self
            .repo
            .create(CreateUserParams {
                id: uuid::Uuid::new_v4(),
                email: oauth_user.email.clone(), // Need to clone as oauth_user is borrowed
                username,
                fullname: oauth_user.name.clone(), // Need to clone as oauth_user is borrowed
                password_hash: None,               // No password for OAuth users
                otp: None,
                oauth_provider: Some(oauth_user.provider.clone()), // Need to clone as oauth_user is borrowed
                oauth_provider_id: Some(oauth_user.provider_id.clone()), // Need to clone as oauth_user is borrowed
                oauth_avatar_url: oauth_user.picture.clone(), // Need to clone as oauth_user is borrowed
                email_verified: oauth_user.verified_email,    // email_verified
                created_at: now,
                updated_at: now,
                permission_version: 1,
            })
            .await
        {
            Ok(created_user) => {
                // Assign default member role
                let role_names_to_assign = vec!["member".to_string()];
                self.finalize_user_creation(created_user, role_names_to_assign)
                    .await
            }
            Err(AppError::Conflict(msg)) if msg.contains("email") => {
                // User already exists, this is expected in concurrent scenarios
                // Return the existing user instead
                let existing_user = self
                    .repo
                    .find_by_email(&oauth_user.email)
                    .await?
                    .ok_or_else(|| {
                        AppError::Internal(anyhow::anyhow!("User should exist but was not found"))
                    })?;

                // Get roles for existing user
                let role_names = self.get_user_role_names(&existing_user.id).await?;
                Ok(UserWithRoles::from_user_and_roles(
                    existing_user,
                    role_names,
                ))
            }
            Err(e) => Err(e),
        }
    }
}

// UserServiceTrait is automatically implemented through trait inheritance
impl UserServiceTrait for UserService {}

// Tests moved to test/permission_version_cache_test.rs
