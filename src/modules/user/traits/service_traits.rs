use crate::modules::auth::oauth_providers::OAuthUserInfo;
use crate::{
    errors::Result,
    modules::user::models::{
        CreateUserRequest, PaginatedUsersWithRoles, UpdateUserRequest, User, UserPaginationRequest,
        UserWithRoles,
    },
};
use async_trait::async_trait;
use uuid::Uuid;

/// User query operations (ISP: Interface segregation)
#[async_trait]
pub trait UserQueryTrait: Send + Sync {
    async fn get_user_by_id(&self, id: &Uuid) -> Result<UserWithRoles>;
    async fn get_user_by_email(&self, email: &str) -> Result<User>;
    async fn get_user_by_username(&self, username: &str) -> Result<User>;
    async fn get_all_users(&self) -> Result<Vec<UserWithRoles>>;
    async fn get_users_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsersWithRoles>;
    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>>;
}

/// User management operations (ISP: Interface segregation)
#[async_trait]
pub trait UserManagementTrait: Send + Sync {
    async fn create_user(&self, request: CreateUserRequest) -> Result<UserWithRoles>;
    async fn update_user(&self, id: &Uuid, request: UpdateUserRequest) -> Result<UserWithRoles>;
    async fn delete_user(&self, id: &Uuid) -> Result<()>;
}

/// User-Role relationship operations (ISP: Interface segregation)
#[async_trait]
pub trait UserRoleTrait: Send + Sync {
    async fn get_user_roles(&self, user_id: &Uuid) -> Result<Vec<Uuid>>;
    async fn user_has_role(&self, user_id: &Uuid, role_id: &Uuid) -> Result<bool>;
    async fn assign_role_to_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()>;
    async fn remove_role_from_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()>;
    async fn update_user_roles(&self, user_id: &Uuid, role_ids: &[Uuid]) -> Result<UserWithRoles>;
}

/// OAuth-specific user operations (ISP: Interface segregation)
#[async_trait]
pub trait UserOAuthTrait: Send + Sync {
    async fn create_oauth_user(&self, oauth_user: &OAuthUserInfo) -> Result<UserWithRoles>;
}

pub trait UserServiceTrait:
    UserQueryTrait + UserManagementTrait + UserRoleTrait + UserOAuthTrait + Send + Sync
{
}
