use crate::{
    database::Database, errors::Result, repository::base_repository::AsyncRepository,
    schema::queries,
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[derive(sqlx::FromRow, <PERSON><PERSON>, Debug)]
pub struct UserRoleInfo {
    pub user_id: Uuid,
    pub role_name: String,
}

/// UserRole repository trait for user-role relationship management (ISP: Interface Segregation)
#[async_trait]
pub trait UserRoleRepository: Send + Sync {
    async fn assign_role_to_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()>;
    async fn remove_role_from_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()>;
    async fn remove_all_roles_for_user(&self, user_id: &Uuid) -> Result<()>;
    async fn get_user_roles(&self, user_id: &Uuid) -> Result<Vec<Uuid>>;
    async fn get_role_names_for_user(&self, user_id: &Uuid) -> Result<Vec<String>>;
    async fn user_has_role(&self, user_id: &Uuid, role_id: &Uuid) -> Result<bool>;
    async fn get_roles_for_users(&self, user_ids: &[Uuid]) -> Result<Vec<UserRoleInfo>>;
    async fn replace_roles_for_user(&self, user_id: &Uuid, role_ids: &[Uuid]) -> Result<()>;
}

pub type DynUserRoleRepo = Arc<dyn UserRoleRepository>;

/// SQLx implementation of UserRoleRepository (ISP: Separate interface for role management)
#[derive(Clone)]
pub struct SqlxUserRoleRepository {
    repo: AsyncRepository,
}

impl SqlxUserRoleRepository {
    pub fn new(db: Database) -> Self {
        Self {
            repo: AsyncRepository::new(db),
        }
    }
}

#[async_trait]
impl UserRoleRepository for SqlxUserRoleRepository {
    async fn assign_role_to_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        let user_id = *user_id;
        let role_id = *role_id;

        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    sqlx::query(queries::ASSIGN_ROLE_TO_USER)
                        .bind(user_id)
                        .bind(role_id)
                        .execute(pool)
                        .await?;

                    Ok(())
                })
            })
            .await
    }

    async fn remove_role_from_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        let user_id = *user_id;
        let role_id = *role_id;

        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    sqlx::query(queries::REMOVE_ROLE_FROM_USER)
                        .bind(user_id)
                        .bind(role_id)
                        .execute(pool)
                        .await?;

                    Ok(())
                })
            })
            .await
    }

    async fn remove_all_roles_for_user(&self, user_id: &Uuid) -> Result<()> {
        let user_id = *user_id;

        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    sqlx::query(queries::REMOVE_ALL_ROLES_FOR_USER)
                        .bind(user_id)
                        .execute(pool)
                        .await?;

                    Ok(())
                })
            })
            .await
    }

    async fn get_user_roles(&self, user_id: &Uuid) -> Result<Vec<Uuid>> {
        let user_id = *user_id;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let role_ids = sqlx::query_scalar::<_, Uuid>(queries::GET_USER_ROLES)
                        .bind(user_id)
                        .fetch_all(pool)
                        .await?;

                    Ok(role_ids)
                })
            })
            .await
    }

    async fn get_role_names_for_user(&self, user_id: &Uuid) -> Result<Vec<String>> {
        let user_id = *user_id;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let role_names =
                        sqlx::query_scalar::<_, String>(queries::GET_ROLE_NAMES_FOR_USER)
                            .bind(user_id)
                            .fetch_all(pool)
                            .await?;

                    Ok(role_names)
                })
            })
            .await
    }

    async fn user_has_role(&self, user_id: &Uuid, role_id: &Uuid) -> Result<bool> {
        let user_id = *user_id;
        let role_id = *role_id;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let has_role: bool = sqlx::query_scalar(queries::USER_HAS_ROLE)
                        .bind(user_id)
                        .bind(role_id)
                        .fetch_one(pool)
                        .await?;

                    Ok(has_role)
                })
            })
            .await
    }

    async fn get_roles_for_users(&self, user_ids: &[Uuid]) -> Result<Vec<UserRoleInfo>> {
        if user_ids.is_empty() {
            return Ok(vec![]);
        }

        let user_ids = user_ids.to_vec();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let roles = sqlx::query_as::<_, UserRoleInfo>(queries::GET_ROLES_FOR_USERS)
                        .bind(&user_ids)
                        .fetch_all(pool)
                        .await?;

                    Ok(roles)
                })
            })
            .await
    }

    async fn replace_roles_for_user(&self, user_id: &Uuid, role_ids: &[Uuid]) -> Result<()> {
        let user_id = *user_id;
        let role_ids = role_ids.to_vec();

        self.repo
            .execute_transaction(move |tx| {
                Box::pin(async move {
                    if role_ids.is_empty() {
                        // Just remove all roles if no new roles to assign
                        sqlx::query(queries::REMOVE_ALL_ROLES_FOR_USER)
                            .bind(user_id)
                            .execute(tx.as_mut())
                            .await?;
                    } else {
                        // First delete all existing roles
                        sqlx::query(queries::REPLACE_ROLES_FOR_USER_DELETE)
                            .bind(user_id)
                            .execute(tx.as_mut())
                            .await?;

                        // Then insert new roles
                        sqlx::query(queries::REPLACE_ROLES_FOR_USER_INSERT)
                            .bind(user_id)
                            .bind(&role_ids)
                            .execute(tx.as_mut())
                            .await?;
                    }

                    Ok(())
                })
            })
            .await
    }
}
