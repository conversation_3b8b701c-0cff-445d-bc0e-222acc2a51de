use crate::map_db_error;
use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::user::database::models::{CreateUserParams, SqlxUser},
    modules::user::models::{
        domain::User,
        pagination::{PaginatedUsers, UserPaginationRequest},
    },
    repository::base_repository::AsyncRepository,
    schema::queries,
    utils::{QueryHelper, pagination::PaginationMeta},
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

#[async_trait]
pub trait UserRepository: Send + Sync {
    async fn create(&self, params: CreateUserParams) -> Result<User>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<User>>;
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    async fn find_by_username(&self, username: &str) -> Result<Option<User>>;
    async fn find_by_email_raw(&self, email: &str) -> Result<Option<SqlxUser>>;
    async fn find_all(&self) -> Result<Vec<User>>;
    async fn update(
        &self,
        id: &Uuid,
        email: Option<String>,
        username: Option<String>,
        fullname: Option<String>,
        password_hash: Option<String>,
    ) -> Result<User>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn find_all_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsers>;
    // Performance optimization methods
    async fn batch_find_by_ids(&self, ids: Vec<Uuid>) -> Result<Vec<User>>;
    async fn exists_by_email(&self, email: &str) -> Result<bool>;
    async fn exists_by_username(&self, username: &str) -> Result<bool>;
    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>>;
}

pub type DynUserRepo = Arc<dyn UserRepository>;

#[derive(Clone)]
pub struct SqlxUserRepository {
    repo: AsyncRepository,
}

impl SqlxUserRepository {
    pub fn new(db: Database) -> Self {
        Self {
            repo: AsyncRepository::new(db),
        }
    }
}

#[async_trait]
impl UserRepository for SqlxUserRepository {
    async fn create(&self, params: CreateUserParams) -> Result<User> {
        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    let user = sqlx::query_as::<_, SqlxUser>(queries::CREATE_USER)
                        .bind(params.id)
                        .bind(&params.username)
                        .bind(&params.fullname)
                        .bind(&params.password_hash)
                        .bind(&params.email)
                        .bind(&params.oauth_provider)
                        .bind(&params.oauth_provider_id)
                        .bind(&params.oauth_avatar_url)
                        .bind(params.email_verified)
                        .bind(params.created_at)
                        .bind(params.updated_at)
                        .bind(params.permission_version)
                        .fetch_one(pool)
                        .await?;

                    Ok(User::from(user))
                })
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<User>> {
        let id = *id;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let user = map_db_error!(
                        sqlx::query_as::<_, SqlxUser>(queries::FIND_USER_BY_ID)
                            .bind(id)
                            .fetch_optional(pool)
                            .await
                    )?;

                    Ok(user.map(User::from))
                })
            })
            .await
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
        let email = email.to_string();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let user = QueryHelper::find_by_string_field::<SqlxUser>(
                        pool,
                        queries::FIND_USER_BY_EMAIL,
                        &email,
                    )
                    .await?;

                    Ok(user.map(User::from))
                })
            })
            .await
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
        let username = username.to_string();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let user = QueryHelper::find_by_string_field::<SqlxUser>(
                        pool,
                        queries::FIND_USER_BY_USERNAME,
                        &username,
                    )
                    .await?;

                    Ok(user.map(User::from))
                })
            })
            .await
    }

    async fn find_by_email_raw(&self, email: &str) -> Result<Option<SqlxUser>> {
        let email = email.to_string();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let user = sqlx::query_as::<_, SqlxUser>(queries::FIND_USER_BY_EMAIL)
                        .bind(&email)
                        .fetch_optional(pool)
                        .await?;

                    Ok(user)
                })
            })
            .await
    }

    async fn find_all(&self) -> Result<Vec<User>> {
        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let users =
                        QueryHelper::find_all::<SqlxUser>(pool, queries::FIND_ALL_USERS).await?;

                    Ok(users.into_iter().map(User::from).collect())
                })
            })
            .await
    }

    async fn update(
        &self,
        id: &Uuid,
        email: Option<String>,
        username: Option<String>,
        fullname: Option<String>,
        password_hash: Option<String>,
    ) -> Result<User> {
        let id = *id;

        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    let user = sqlx::query_as::<_, SqlxUser>(queries::UPDATE_USER)
                        .bind(id)
                        .bind(&email)
                        .bind(&username)
                        .bind(&fullname)
                        .bind(&password_hash)
                        .fetch_one(pool)
                        .await?;

                    Ok(User::from(user))
                })
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;

        self.repo
            .execute(move |pool| {
                Box::pin(async move {
                    let result = sqlx::query(queries::DELETE_USER)
                        .bind(id)
                        .execute(pool)
                        .await?;

                    if result.rows_affected() == 0 {
                        return Err(AppError::NotFound("User not found".into()));
                    }

                    Ok(())
                })
            })
            .await
    }

    async fn find_all_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsers> {
        let offset = (pagination.page - 1) * pagination.limit;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    // Get total count
                    let total: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
                        .fetch_one(pool)
                        .await?;

                    // Get paginated users
                    let users = sqlx::query_as::<_, SqlxUser>(
                        "SELECT * FROM users ORDER BY created_at DESC LIMIT $1 OFFSET $2",
                    )
                    .bind(pagination.limit)
                    .bind(offset)
                    .fetch_all(pool)
                    .await?;

                    let total_pages = (total + pagination.limit - 1) / pagination.limit;
                    let meta = PaginationMeta {
                        page: pagination.page,
                        limit: pagination.limit,
                        total,
                        total_pages,
                    };

                    Ok(PaginatedUsers {
                        data: users.into_iter().map(User::from).collect(),
                        meta,
                    })
                })
            })
            .await
    }

    async fn batch_find_by_ids(&self, ids: Vec<Uuid>) -> Result<Vec<User>> {
        if ids.is_empty() {
            return Ok(vec![]);
        }

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let users = sqlx::query_as::<_, SqlxUser>(
                        "SELECT * FROM users WHERE id = ANY($1) ORDER BY created_at DESC",
                    )
                    .bind(&ids)
                    .fetch_all(pool)
                    .await?;

                    Ok(users.into_iter().map(User::from).collect())
                })
            })
            .await
    }

    async fn exists_by_email(&self, email: &str) -> Result<bool> {
        let email = email.to_string();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let exists: bool = sqlx::query_scalar(queries::EXISTS_USER_BY_EMAIL)
                        .bind(&email)
                        .fetch_one(pool)
                        .await?;

                    Ok(exists)
                })
            })
            .await
    }

    async fn exists_by_username(&self, username: &str) -> Result<bool> {
        let username = username.to_string();

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let exists: bool = sqlx::query_scalar(queries::EXISTS_USER_BY_USERNAME)
                        .bind(&username)
                        .fetch_one(pool)
                        .await?;

                    Ok(exists)
                })
            })
            .await
    }

    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>> {
        let id = *id;

        self.repo
            .execute_readonly(move |pool| {
                Box::pin(async move {
                    let version: Option<i32> =
                        sqlx::query_scalar("SELECT permission_version FROM users WHERE id = $1")
                            .bind(id)
                            .fetch_optional(pool)
                            .await?;

                    Ok(version)
                })
            })
            .await
    }
}
