use chrono::{DateTime, Utc};
use sqlx::FromRow;
use uuid::Uuid;

// ===== SQLX DATABASE MODELS =====

#[derive(Debug, FromRow)]
pub struct SqlxUser {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub password_hash: Option<String>, // Nullable for OAuth users
    pub email: String,
    pub otp: Option<String>,
    pub oauth_provider: Option<String>,
    pub oauth_provider_id: Option<String>,
    pub oauth_avatar_url: Option<String>,
    pub email_verified: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub permission_version: i32,
}

// ===== CREATE PARAMS =====

#[derive(Debug)]
pub struct CreateUserParams {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub password_hash: Option<String>, // Nullable for OAuth users
    pub email: String,
    pub otp: Option<String>,
    pub oauth_provider: Option<String>,
    pub oauth_provider_id: Option<String>,
    pub oauth_avatar_url: Option<String>,
    pub email_verified: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub permission_version: i32,
}

// ===== CONVERSION IMPLEMENTATIONS =====

impl From<SqlxUser> for crate::modules::user::models::domain::User {
    fn from(sqlx_user: SqlxUser) -> Self {
        Self {
            id: sqlx_user.id,
            username: sqlx_user.username,
            fullname: sqlx_user.fullname,
            email: sqlx_user.email,
            created_at: sqlx_user.created_at,
            updated_at: sqlx_user.updated_at,
            oauth_provider: sqlx_user.oauth_provider,
            oauth_avatar_url: sqlx_user.oauth_avatar_url,
            email_verified: sqlx_user.email_verified,
            permission_version: sqlx_user.permission_version,
        }
    }
}
