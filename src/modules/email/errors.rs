use thiserror::Error;

/// Custom error types for email operations
#[derive(Error, Debug)]
pub enum EmailError {
    #[error("Template error: {message}")]
    TemplateError { message: String },

    #[error("Template '{template_name}' not found")]
    TemplateNotFound { template_name: String },

    #[error("Strategy error: {message}")]
    StrategyError { message: String },

    #[error("No strategy found for event type: {event_type}")]
    StrategyNotFound { event_type: String },

    #[error("SMTP error: {message}")]
    SmtpError { message: String },

    #[error("Email validation error: {message}")]
    ValidationError { message: String },

    #[error("Event bus error: {message}")]
    EventBusError { message: String },

    #[error("Serialization error: {message}")]
    SerializationError { message: String },

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },

    #[error("Connection timeout: {message}")]
    TimeoutError { message: String },

    #[error("Invalid email address: {email}")]
    InvalidEmailAddress { email: String },

    #[error("Email service not initialized")]
    ServiceNotInitialized,

    #[error("Redis connection error: {message}")]
    RedisError { message: String },
}

impl EmailError {
    /// Convert EmailError to AppError for compatibility with existing error handling
    pub fn into_app_error(self) -> crate::errors::AppError {
        crate::errors::AppError::Internal(anyhow::anyhow!(self.to_string()))
    }

    /// Create template error
    pub fn template_error(message: impl Into<String>) -> Self {
        Self::TemplateError {
            message: message.into(),
        }
    }

    /// Create template not found error
    pub fn template_not_found(template_name: impl Into<String>) -> Self {
        Self::TemplateNotFound {
            template_name: template_name.into(),
        }
    }

    /// Create strategy error
    pub fn strategy_error(message: impl Into<String>) -> Self {
        Self::StrategyError {
            message: message.into(),
        }
    }

    /// Create strategy not found error
    pub fn strategy_not_found(event_type: impl Into<String>) -> Self {
        Self::StrategyNotFound {
            event_type: event_type.into(),
        }
    }

    /// Create SMTP error
    pub fn smtp_error(message: impl Into<String>) -> Self {
        Self::SmtpError {
            message: message.into(),
        }
    }

    /// Create validation error
    pub fn validation_error(message: impl Into<String>) -> Self {
        Self::ValidationError {
            message: message.into(),
        }
    }

    /// Create event bus error
    pub fn event_bus_error(message: impl Into<String>) -> Self {
        Self::EventBusError {
            message: message.into(),
        }
    }

    /// Create serialization error
    pub fn serialization_error(message: impl Into<String>) -> Self {
        Self::SerializationError {
            message: message.into(),
        }
    }

    /// Create configuration error
    pub fn configuration_error(message: impl Into<String>) -> Self {
        Self::ConfigurationError {
            message: message.into(),
        }
    }

    /// Create timeout error
    pub fn timeout_error(message: impl Into<String>) -> Self {
        Self::TimeoutError {
            message: message.into(),
        }
    }

    /// Create invalid email address error
    pub fn invalid_email_address(email: impl Into<String>) -> Self {
        Self::InvalidEmailAddress {
            email: email.into(),
        }
    }

    /// Create Redis error
    pub fn redis_error(message: impl Into<String>) -> Self {
        Self::RedisError {
            message: message.into(),
        }
    }
}

/// Result type alias for email operations
pub type EmailResult<T> = Result<T, EmailError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_email_error_creation() {
        let error = EmailError::template_not_found("welcome_email");
        assert!(matches!(error, EmailError::TemplateNotFound { .. }));
        assert_eq!(error.to_string(), "Template 'welcome_email' not found");
    }

    #[test]
    fn test_error_conversion() {
        let email_error = EmailError::smtp_error("Connection failed");
        let app_error = email_error.into_app_error();
        // Check that conversion works (actual test would depend on AppError implementation)
        assert!(!app_error.to_string().is_empty());
    }
}
