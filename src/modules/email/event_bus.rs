use crate::errors::AppError;
use crate::modules::email::events::EmailEvent;
use crate::modules::redis::RedisServiceTrait;
use anyhow::Result;
use serde_json;
use std::sync::Arc;
use tracing::{error, info};

#[derive(Clone)]
pub struct EmailEventBus {
    redis: Arc<dyn RedisServiceTrait>,
}

impl EmailEventBus {
    pub fn new(redis: Arc<dyn RedisServiceTrait>) -> Self {
        Self { redis }
    }

    pub async fn publish(&self, event: EmailEvent) -> Result<(), AppError> {
        info!(
            "Publishing email event: {} for user: {}",
            event.get_event_type(),
            event.get_user_id()
        );

        let json = serde_json::to_string(&event)
            .map_err(|e| AppError::Internal(anyhow::anyhow!("Failed to serialize event: {}", e)))?;

        match self.redis.rpush("email_events", &json).await {
            Ok(_) => {
                info!("Email event published successfully to Redis");
                Ok(())
            }
            Err(e) => {
                error!("Failed to publish email event to Redis: {}", e);
                Err(AppError::Internal(anyhow::anyhow!(
                    "Failed to publish email event: {}",
                    e
                )))
            }
        }
    }

    pub async fn pop_event(&self) -> Result<Option<EmailEvent>, AppError> {
        match self.redis.brpop("email_events", 30).await {
            Ok(Some(json)) => {
                let event: EmailEvent = serde_json::from_str(&json).map_err(|e| {
                    AppError::Internal(anyhow::anyhow!("Failed to deserialize event: {}", e))
                })?;
                Ok(Some(event))
            }
            Ok(None) => Ok(None),
            Err(e) => Err(AppError::Internal(anyhow::anyhow!(
                "Failed to pop event: {}",
                e
            ))),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_email_event_bus_requires_redis() {
        // This test would require a mock Redis service
        // For now, we just verify the struct compiles without fallback
        let _bus = EmailEventBus::new(Arc::new(MockRedisService));
    }

    // Mock Redis service for testing
    struct MockRedisService;

    #[async_trait::async_trait]
    impl RedisServiceTrait for MockRedisService {
        async fn set(
            &self,
            _key: &str,
            _value: &str,
            _expiration: Option<std::time::Duration>,
        ) -> anyhow::Result<()> {
            Ok(())
        }

        async fn get(&self, _key: &str) -> anyhow::Result<Option<String>> {
            Ok(None)
        }

        async fn delete(&self, _key: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn exists(&self, _key: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn expire(
            &self,
            _key: &str,
            _expiration: std::time::Duration,
        ) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn ttl(&self, _key: &str) -> anyhow::Result<i64> {
            Ok(-1)
        }

        async fn increment(&self, _key: &str, _delta: i64) -> anyhow::Result<i64> {
            Ok(0)
        }

        async fn hset(&self, _key: &str, _field: &str, _value: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn hget(&self, _key: &str, _field: &str) -> anyhow::Result<Option<String>> {
            Ok(None)
        }

        async fn hdel(&self, _key: &str, _field: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn hgetall(&self, _key: &str) -> anyhow::Result<Vec<(String, String)>> {
            Ok(vec![])
        }

        async fn lpush(&self, _key: &str, _value: &str) -> anyhow::Result<i64> {
            Ok(0)
        }

        async fn rpush(&self, _key: &str, _value: &str) -> anyhow::Result<i64> {
            Ok(0)
        }

        async fn lpop(&self, _key: &str) -> anyhow::Result<Option<String>> {
            Ok(None)
        }

        async fn rpop(&self, _key: &str) -> anyhow::Result<Option<String>> {
            Ok(None)
        }

        async fn llen(&self, _key: &str) -> anyhow::Result<i64> {
            Ok(0)
        }

        async fn lrange(&self, _key: &str, _start: i64, _stop: i64) -> anyhow::Result<Vec<String>> {
            Ok(vec![])
        }

        async fn brpop(&self, _key: &str, _timeout_secs: u32) -> anyhow::Result<Option<String>> {
            Ok(None)
        }

        async fn sadd(&self, _key: &str, _value: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn srem(&self, _key: &str, _value: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn sismember(&self, _key: &str, _value: &str) -> anyhow::Result<bool> {
            Ok(false)
        }

        async fn smembers(&self, _key: &str) -> anyhow::Result<Vec<String>> {
            Ok(vec![])
        }

        async fn ping(&self) -> anyhow::Result<String> {
            Ok("PONG".to_string())
        }

        async fn flushdb(&self) -> anyhow::Result<()> {
            Ok(())
        }

        async fn health_check(&self) -> anyhow::Result<bool> {
            Ok(true)
        }
    }
}
