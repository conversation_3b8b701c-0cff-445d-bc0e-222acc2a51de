use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum EmailEvent {
    WelcomeEmail {
        user_id: String,
        email: String,
        name: String,
    },
    PasswordReset {
        user_id: String,
        email: String,
        reset_token: String,
    },
    EmailVerification {
        user_id: String,
        email: String,
        verification_token: String,
    },
    PermissionChanged {
        user_id: String,
        email: String,
        permissions: Vec<String>,
    },
    RoleChanged {
        user_id: String,
        email: String,
        old_roles: Vec<String>,
        new_roles: Vec<String>,
    },
}

impl EmailEvent {
    pub fn get_recipient_email(&self) -> &str {
        match self {
            EmailEvent::WelcomeEmail { email, .. } => email,
            EmailEvent::PasswordReset { email, .. } => email,
            EmailEvent::EmailVerification { email, .. } => email,
            EmailEvent::PermissionChanged { email, .. } => email,
            EmailEvent::RoleChanged { email, .. } => email,
        }
    }

    pub fn get_user_id(&self) -> &str {
        match self {
            EmailEvent::WelcomeEmail { user_id, .. } => user_id,
            EmailEvent::PasswordReset { user_id, .. } => user_id,
            EmailEvent::EmailVerification { user_id, .. } => user_id,
            EmailEvent::PermissionChanged { user_id, .. } => user_id,
            EmailEvent::RoleChanged { user_id, .. } => user_id,
        }
    }

    pub fn get_event_type(&self) -> &'static str {
        match self {
            EmailEvent::WelcomeEmail { .. } => "welcome_email",
            EmailEvent::PasswordReset { .. } => "password_reset",
            EmailEvent::EmailVerification { .. } => "email_verification",
            EmailEvent::PermissionChanged { .. } => "permission_changed",
            EmailEvent::RoleChanged { .. } => "role_changed",
        }
    }
}
