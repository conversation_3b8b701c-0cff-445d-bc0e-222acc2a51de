use crate::errors::AppError;
use crate::modules::email::errors::{EmailError, EmailResult};
use crate::modules::email::models::{EmailContext, EmailMessage, EmailTemplate};
use handlebars::Handlebars;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use tracing::{info, warn};

pub struct TemplateEngine {
    handlebars: Handlebars<'static>,
    templates: HashMap<String, EmailTemplate>,
    template_dir: String,
}

impl Default for TemplateEngine {
    fn default() -> Self {
        Self::new()
    }
}

impl TemplateEngine {
    pub fn new() -> Self {
        Self::with_template_dir("templates/email".to_string())
    }

    pub fn with_template_dir(template_dir: String) -> Self {
        let mut handlebars = Handlebars::new();
        handlebars.set_strict_mode(true);

        let mut engine = Self {
            handlebars,
            templates: HashMap::new(),
            template_dir,
        };

        // Try to load templates from files first, fallback to defaults
        if let Err(e) = engine.load_templates_from_files() {
            warn!("Failed to load templates from files: {}, using defaults", e);
            engine.load_default_templates();
        }

        engine
    }

    pub fn render_email(
        &self,
        template_name: &str,
        context: &EmailContext,
    ) -> Result<EmailMessage, AppError> {
        let template = self.templates.get(template_name).ok_or_else(|| {
            AppError::Internal(anyhow::anyhow!("Template '{}' not found", template_name))
        })?;

        let context_json = serde_json::to_value(context).map_err(|e| {
            AppError::Internal(anyhow::anyhow!("Failed to serialize context: {}", e))
        })?;

        let subject = self
            .handlebars
            .render_template(&template.subject_template, &context_json)
            .map_err(|e| AppError::Internal(anyhow::anyhow!("Failed to render subject: {}", e)))?;

        let html_body = self
            .handlebars
            .render_template(&template.html_template, &context_json)
            .map_err(|e| {
                AppError::Internal(anyhow::anyhow!("Failed to render HTML body: {}", e))
            })?;

        let text_body = if let Some(text_template) = &template.text_template {
            Some(
                self.handlebars
                    .render_template(text_template, &context_json)
                    .map_err(|e| {
                        AppError::Internal(anyhow::anyhow!("Failed to render text body: {}", e))
                    })?,
            )
        } else {
            None
        };

        info!("Successfully rendered email template: {}", template_name);

        Ok(EmailMessage {
            to: context.user_email.clone(),
            subject,
            html_body,
            text_body,
        })
    }

    fn load_default_templates(&mut self) {
        // Welcome Email Template
        let welcome_template = EmailTemplate {
            name: "welcome_email".to_string(),
            subject_template: "Welcome to {{app_name}}, {{user_name}}!".to_string(),
            html_template: r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to {{app_name}}!</h1>
        </div>
        <div class="content">
            <h2>Hello {{user_name}}!</h2>
            <p>Thank you for joining {{app_name}}. We're excited to have you on board!</p>
            <p>Your account has been successfully created with email: <strong>{{user_email}}</strong></p>
            <p>You can now access all the features of our platform.</p>
            <p style="text-align: center;">
                <a href="{{app_url}}" class="button">Get Started</a>
            </p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{app_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            "#.to_string(),
            text_template: Some(r#"
Welcome to {{app_name}}, {{user_name}}!

Thank you for joining {{app_name}}. We're excited to have you on board!

Your account has been successfully created with email: {{user_email}}

You can now access all the features of our platform.

Visit: {{app_url}}

© 2024 {{app_name}}. All rights reserved.
            "#.to_string()),
        };

        // Password Reset Template
        let password_reset_template = EmailTemplate {
            name: "password_reset".to_string(),
            subject_template: "Reset your {{app_name}} password".to_string(),
            html_template: r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>
        <div class="content">
            <h2>Hello {{user_name}},</h2>
            <p>We received a request to reset your password for your {{app_name}} account.</p>
            <div class="warning">
                <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email.
            </div>
            <p>Click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="{{app_url}}/auth/reset-password?token={{reset_token}}" class="button">Reset Password</a>
            </p>
            <p><small>This link will expire in 1 hour for security reasons.</small></p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{app_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            "#.to_string(),
            text_template: Some(r#"
Password Reset Request - {{app_name}}

Hello {{user_name}},

We received a request to reset your password for your {{app_name}} account.

SECURITY NOTICE: If you didn't request this password reset, please ignore this email.

Reset your password by visiting:
{{app_url}}/auth/reset-password?token={{reset_token}}

This link will expire in 1 hour for security reasons.

© 2024 {{app_name}}. All rights reserved.
            "#.to_string()),
        };

        // Email Verification Template
        let email_verification_template = EmailTemplate {
            name: "email_verification".to_string(),
            subject_template: "Verify your {{app_name}} email address".to_string(),
            html_template: r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Verify Your Email</h1>
        </div>
        <div class="content">
            <h2>Hello {{user_name}},</h2>
            <p>Please verify your email address to complete your {{app_name}} account setup.</p>
            <p>Click the button below to verify your email:</p>
            <p style="text-align: center;">
                <a href="{{app_url}}/auth/verify-email?token={{verification_token}}" class="button">Verify Email</a>
            </p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{app_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            "#.to_string(),
            text_template: Some(r#"
Verify Your Email - {{app_name}}

Hello {{user_name}},

Please verify your email address to complete your {{app_name}} account setup.

Verify your email by visiting:
{{app_url}}/auth/verify-email?token={{verification_token}}

© 2024 {{app_name}}. All rights reserved.
            "#.to_string()),
        };

        // Permission Changed Template
        let permission_changed_template = EmailTemplate {
            name: "permission_changed".to_string(),
            subject_template: "Your {{app_name}} permissions have been updated".to_string(),
            html_template: r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #ffc107; color: #212529; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .permissions { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Permissions Updated</h1>
        </div>
        <div class="content">
            <h2>Hello {{user_name}},</h2>
            <p>Your permissions on {{app_name}} have been updated by an administrator.</p>
            <div class="permissions">
                <h3>Current Permissions:</h3>
                <ul>
                    {{#each permissions}}
                    <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
            <p>If you have any questions about these changes, please contact your administrator.</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{app_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            "#.to_string(),
            text_template: Some(r#"
Permissions Updated - {{app_name}}

Hello {{user_name}},

Your permissions on {{app_name}} have been updated by an administrator.

Current Permissions:
{{#each permissions}}
- {{this}}
{{/each}}

If you have any questions about these changes, please contact your administrator.

© 2024 {{app_name}}. All rights reserved.
            "#.to_string()),
        };

        // Role Changed Template
        let role_changed_template = EmailTemplate {
            name: "role_changed".to_string(),
            subject_template: "Your {{app_name}} roles have been updated".to_string(),
            html_template: r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6f42c1; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .roles { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #6f42c1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Roles Updated</h1>
        </div>
        <div class="content">
            <h2>Hello {{user_name}},</h2>
            <p>Your roles on {{app_name}} have been updated by an administrator.</p>
            
            {{#if old_roles}}
            <div class="roles">
                <h3>Previous Roles:</h3>
                <ul>
                    {{#each old_roles}}
                    <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
            {{/if}}
            
            <div class="roles">
                <h3>New Roles:</h3>
                <ul>
                    {{#each new_roles}}
                    <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
            
            <p>If you have any questions about these changes, please contact your administrator.</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{app_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            "#.to_string(),
            text_template: Some(r#"
Roles Updated - {{app_name}}

Hello {{user_name}},

Your roles on {{app_name}} have been updated by an administrator.

{{#if old_roles}}
Previous Roles:
{{#each old_roles}}
- {{this}}
{{/each}}
{{/if}}

New Roles:
{{#each new_roles}}
- {{this}}
{{/each}}

If you have any questions about these changes, please contact your administrator.

© 2024 {{app_name}}. All rights reserved.
            "#.to_string()),
        };

        // Store templates
        self.templates
            .insert("welcome_email".to_string(), welcome_template);
        self.templates
            .insert("password_reset".to_string(), password_reset_template);
        self.templates.insert(
            "email_verification".to_string(),
            email_verification_template,
        );
        self.templates.insert(
            "permission_changed".to_string(),
            permission_changed_template,
        );
        self.templates
            .insert("role_changed".to_string(), role_changed_template);

        info!("Loaded {} email templates", self.templates.len());
    }

    pub fn add_template(&mut self, template: EmailTemplate) {
        self.templates.insert(template.name.clone(), template);
    }

    pub fn get_template_names(&self) -> Vec<String> {
        self.templates.keys().cloned().collect()
    }

    /// Load email templates from external files
    fn load_templates_from_files(&mut self) -> EmailResult<()> {
        let template_names = [
            "welcome_email",
            "password_reset",
            "email_verification",
            "permission_changed",
            "role_changed",
        ];

        for template_name in &template_names {
            if let Err(e) = self.load_template_from_file(template_name) {
                return Err(EmailError::template_error(format!(
                    "Failed to load template '{template_name}': {e}"
                )));
            }
        }

        info!("Loaded {} email templates from files", template_names.len());
        Ok(())
    }

    /// Load a single template from file
    fn load_template_from_file(&mut self, template_name: &str) -> EmailResult<()> {
        let html_path = Path::new(&self.template_dir).join(format!("{template_name}.hbs"));
        let text_path = Path::new(&self.template_dir).join(format!("{template_name}.txt"));

        // Read HTML template (required)
        let html_template = fs::read_to_string(&html_path).map_err(|e| {
            EmailError::template_error(format!("Failed to read HTML template: {e}"))
        })?;

        // Read text template (optional)
        let text_template = match fs::read_to_string(&text_path) {
            Ok(content) => Some(content),
            Err(_) => {
                warn!(
                    "No text template found for {}, using HTML only",
                    template_name
                );
                None
            }
        };

        // Generate subject from template name
        let subject_template = match template_name {
            "welcome_email" => "Welcome to {{app_name}}, {{user_name}}!",
            "password_reset" => "Password Reset Request - {{app_name}}",
            "email_verification" => "Verify your {{app_name}} email address",
            "permission_changed" => "Permissions Updated - {{app_name}}",
            "role_changed" => "Roles Updated - {{app_name}}",
            _ => "{{app_name}} Notification",
        };

        let template = EmailTemplate {
            name: template_name.to_string(),
            subject_template: subject_template.to_string(),
            html_template,
            text_template,
        };

        self.templates.insert(template_name.to_string(), template);
        Ok(())
    }
}
