use crate::errors::AppError;
use crate::modules::email::{
    EmailContextBuilder, EmailError, EmailEvent, EmailMessage, TemplateEngine,
};
use async_trait::async_trait;
use std::sync::Arc;

/// Trait định nghĩa strategy cho việc tạo email từ event
#[async_trait]
pub trait EmailStrategy: Send + Sync {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError>;

    fn supports_event(&self, event: &EmailEvent) -> bool;
    fn get_strategy_name(&self) -> &'static str;
}

/// Strategy cho Welcome Email
pub struct WelcomeEmailStrategy;

#[async_trait]
impl EmailStrategy for WelcomeEmailStrategy {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError> {
        if let EmailEvent::WelcomeEmail { email, name, .. } = event {
            let context =
                EmailContextBuilder::new(email.clone(), app_url.to_string(), app_name.to_string())
                    .with_user_name(name.clone())
                    .build();

            template_engine.render_email("welcome_email", &context)
        } else {
            Err(
                EmailError::strategy_error("WelcomeEmailStrategy called with wrong event type")
                    .into_app_error(),
            )
        }
    }

    fn supports_event(&self, event: &EmailEvent) -> bool {
        matches!(event, EmailEvent::WelcomeEmail { .. })
    }

    fn get_strategy_name(&self) -> &'static str {
        "welcome_email"
    }
}

/// Strategy cho Password Reset Email
pub struct PasswordResetEmailStrategy;

#[async_trait]
impl EmailStrategy for PasswordResetEmailStrategy {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError> {
        if let EmailEvent::PasswordReset {
            email, reset_token, ..
        } = event
        {
            let context =
                EmailContextBuilder::new(email.clone(), app_url.to_string(), app_name.to_string())
                    .build()
                    .with_reset_token(reset_token.clone());

            template_engine.render_email("password_reset", &context)
        } else {
            Err(EmailError::strategy_error(
                "PasswordResetEmailStrategy called with wrong event type",
            )
            .into_app_error())
        }
    }

    fn supports_event(&self, event: &EmailEvent) -> bool {
        matches!(event, EmailEvent::PasswordReset { .. })
    }

    fn get_strategy_name(&self) -> &'static str {
        "password_reset"
    }
}

/// Strategy cho Email Verification
pub struct EmailVerificationStrategy;

#[async_trait]
impl EmailStrategy for EmailVerificationStrategy {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError> {
        if let EmailEvent::EmailVerification {
            email,
            verification_token,
            ..
        } = event
        {
            let context =
                EmailContextBuilder::new(email.clone(), app_url.to_string(), app_name.to_string())
                    .build()
                    .with_verification_token(verification_token.clone());

            template_engine.render_email("email_verification", &context)
        } else {
            Err(EmailError::strategy_error(
                "EmailVerificationStrategy called with wrong event type",
            )
            .into_app_error())
        }
    }

    fn supports_event(&self, event: &EmailEvent) -> bool {
        matches!(event, EmailEvent::EmailVerification { .. })
    }

    fn get_strategy_name(&self) -> &'static str {
        "email_verification"
    }
}

/// Strategy cho Permission Changed Email
pub struct PermissionChangedEmailStrategy;

#[async_trait]
impl EmailStrategy for PermissionChangedEmailStrategy {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError> {
        if let EmailEvent::PermissionChanged {
            email, permissions, ..
        } = event
        {
            let context =
                EmailContextBuilder::new(email.clone(), app_url.to_string(), app_name.to_string())
                    .build()
                    .with_permissions(permissions.clone());

            template_engine.render_email("permission_changed", &context)
        } else {
            Err(EmailError::strategy_error(
                "PermissionChangedEmailStrategy called with wrong event type",
            )
            .into_app_error())
        }
    }

    fn supports_event(&self, event: &EmailEvent) -> bool {
        matches!(event, EmailEvent::PermissionChanged { .. })
    }

    fn get_strategy_name(&self) -> &'static str {
        "permission_changed"
    }
}

/// Strategy cho Role Changed Email
pub struct RoleChangedEmailStrategy;

#[async_trait]
impl EmailStrategy for RoleChangedEmailStrategy {
    async fn create_email(
        &self,
        event: &EmailEvent,
        template_engine: &TemplateEngine,
        app_url: &str,
        app_name: &str,
    ) -> Result<EmailMessage, AppError> {
        if let EmailEvent::RoleChanged {
            email,
            old_roles,
            new_roles,
            ..
        } = event
        {
            let context =
                EmailContextBuilder::new(email.clone(), app_url.to_string(), app_name.to_string())
                    .build()
                    .with_role_change(old_roles.clone(), new_roles.clone());

            template_engine.render_email("role_changed", &context)
        } else {
            Err(
                EmailError::strategy_error("RoleChangedEmailStrategy called with wrong event type")
                    .into_app_error(),
            )
        }
    }

    fn supports_event(&self, event: &EmailEvent) -> bool {
        matches!(event, EmailEvent::RoleChanged { .. })
    }

    fn get_strategy_name(&self) -> &'static str {
        "role_changed"
    }
}

/// Factory để quản lý các EmailStrategy
pub struct EmailStrategyFactory {
    strategies: Vec<Arc<dyn EmailStrategy>>,
}

impl EmailStrategyFactory {
    pub fn new() -> Self {
        let mut factory = Self {
            strategies: Vec::new(),
        };

        // Đăng ký các strategy mặc định
        factory.register_strategy(Arc::new(WelcomeEmailStrategy));
        factory.register_strategy(Arc::new(PasswordResetEmailStrategy));
        factory.register_strategy(Arc::new(EmailVerificationStrategy));
        factory.register_strategy(Arc::new(PermissionChangedEmailStrategy));
        factory.register_strategy(Arc::new(RoleChangedEmailStrategy));

        factory
    }

    pub fn register_strategy(&mut self, strategy: Arc<dyn EmailStrategy>) {
        self.strategies.push(strategy);
    }

    pub fn get_strategy(&self, event: &EmailEvent) -> Option<Arc<dyn EmailStrategy>> {
        self.strategies
            .iter()
            .find(|strategy| strategy.supports_event(event))
            .cloned()
    }

    pub fn get_registered_strategies(&self) -> Vec<&'static str> {
        self.strategies
            .iter()
            .map(|strategy| strategy.get_strategy_name())
            .collect()
    }
}

impl Default for EmailStrategyFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_welcome_email_strategy() {
        let strategy = WelcomeEmailStrategy;
        let event = EmailEvent::WelcomeEmail {
            user_id: "test-user".to_string(),
            email: "<EMAIL>".to_string(),
            name: "Test User".to_string(),
        };

        assert!(strategy.supports_event(&event));
        assert_eq!(strategy.get_strategy_name(), "welcome_email");
    }

    #[test]
    fn test_email_strategy_factory() {
        let factory = EmailStrategyFactory::new();
        let strategies = factory.get_registered_strategies();

        assert_eq!(strategies.len(), 5);
        assert!(strategies.contains(&"welcome_email"));
        assert!(strategies.contains(&"password_reset"));
        assert!(strategies.contains(&"email_verification"));
        assert!(strategies.contains(&"permission_changed"));
        assert!(strategies.contains(&"role_changed"));
    }

    #[test]
    fn test_strategy_selection() {
        let factory = EmailStrategyFactory::new();

        let welcome_event = EmailEvent::WelcomeEmail {
            user_id: "test".to_string(),
            email: "<EMAIL>".to_string(),
            name: "Test".to_string(),
        };

        let strategy = factory.get_strategy(&welcome_event);
        assert!(strategy.is_some());
        if let Some(strategy) = strategy {
            assert_eq!(strategy.get_strategy_name(), "welcome_email");
        } else {
            // This should never happen since we just asserted strategy.is_some()
            unreachable!("Expected to find a strategy for welcome email event");
        }
    }
}
