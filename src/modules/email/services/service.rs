use crate::errors::AppError;
use crate::modules::email::{
    EmailConfig, EmailEvent, EmailEventBus, EmailMessage, EmailStrategyFactory, TemplateEngine,
};
use crate::utils::error::circuit_breaker::CircuitBreaker;
use anyhow::anyhow;
use lettre::message::Mailbox;
use lettre::{
    AsyncSmtpTransport, AsyncTransport, Message, Tokio1Executor, message::MultiPart,
    transport::smtp::authentication::Credentials,
};
use std::sync::Arc;
use std::time::Duration;
use tracing::{error, info, warn};

pub struct EmailService {
    event_bus: Arc<EmailEventBus>,
    smtp_transport: Option<AsyncSmtpTransport<Tokio1Executor>>,
    template_engine: Arc<TemplateEngine>,
    strategy_factory: EmailStrategyFactory,
    config: EmailConfig,
    app_url: String,
    app_name: String,
    circuit_breaker: CircuitBreaker,
}

impl EmailService {
    pub async fn new(
        event_bus: Arc<EmailEventBus>,
        config: EmailConfig,
        app_url: String,
        app_name: String,
    ) -> Self {
        let circuit_breaker = CircuitBreaker::new_simple(3, Duration::from_secs(60));
        Self {
            event_bus,
            smtp_transport: None,
            template_engine: Arc::new(TemplateEngine::new()),
            strategy_factory: EmailStrategyFactory::new(),
            config,
            app_url,
            app_name,
            circuit_breaker,
        }
    }

    pub async fn with_custom_strategy_factory(
        event_bus: Arc<EmailEventBus>,
        config: EmailConfig,
        app_url: String,
        app_name: String,
        strategy_factory: EmailStrategyFactory,
    ) -> Self {
        let circuit_breaker = CircuitBreaker::new_simple(3, Duration::from_secs(60));
        Self {
            event_bus,
            smtp_transport: None,
            template_engine: Arc::new(TemplateEngine::new()),
            strategy_factory,
            config,
            app_url,
            app_name,
            circuit_breaker,
        }
    }

    pub async fn initialize(&mut self) -> Result<(), AppError> {
        info!("Initializing email service...");

        // Initialize SMTP transport
        if !self.config.smtp_username.is_empty() && !self.config.smtp_password.is_empty() {
            let credentials = Credentials::new(
                self.config.smtp_username.clone(),
                self.config.smtp_password.clone(),
            );

            let transport = if self.config.use_tls {
                // Use STARTTLS for Gmail (port 587)
                AsyncSmtpTransport::<Tokio1Executor>::starttls_relay(&self.config.smtp_host)
                    .map_err(|e| {
                        AppError::Internal(anyhow::anyhow!(
                            "Failed to create STARTTLS relay: {}",
                            e
                        ))
                    })?
                    .port(self.config.smtp_port)
                    .credentials(credentials)
                    .timeout(Some(Duration::from_secs(30)))
                    .build()
            } else {
                AsyncSmtpTransport::<Tokio1Executor>::builder_dangerous(&self.config.smtp_host)
                    .port(self.config.smtp_port)
                    .credentials(credentials)
                    .timeout(Some(Duration::from_secs(30)))
                    .build()
            };

            self.smtp_transport = Some(transport);
            info!("SMTP transport initialized successfully");
        } else {
            warn!(
                "SMTP credentials not provided. Email service will run in debug mode (emails will be logged only)"
            );
        }

        Ok(())
    }

    pub async fn start_listening(&self) -> Result<(), AppError> {
        info!("Starting email service event listener...");

        // Add 30 second delay before starting to listen - for Redis inspection
        if cfg!(debug_assertions) {
            info!(
                "Waiting 30 seconds before starting email event listener to allow Redis inspection..."
            );
            tokio::time::sleep(Duration::from_secs(30)).await;
        }
        info!("Now starting to listen for email events");

        loop {
            match self.event_bus.pop_event().await {
                Ok(Some(event)) => {
                    if let Err(e) = self.handle_event(event).await {
                        error!("Failed to handle email event: {}", e);
                    }
                }
                Ok(None) => {
                    // No event, continue looping (brpop timed out)
                }
                Err(e) => {
                    error!("Failed to pop email event: {}", e);
                    // Optional: add delay or break on fatal error
                }
            }
        }
    }

    async fn handle_event(&self, event: EmailEvent) -> Result<(), AppError> {
        info!(
            "Handling email event: {} for user: {} - Adding 15s delay for Redis inspection",
            event.get_event_type(),
            event.get_user_id()
        );

        // Add 15 second delay to allow Redis inspection
        if cfg!(debug_assertions) {
            info!("Waiting 15 seconds before processing email event...");
            tokio::time::sleep(Duration::from_secs(15)).await;
        }
        info!("Delay completed, now processing email event");
        info!(
            "Processing email event: {} for user: {} with details: {:?}",
            event.get_event_type(),
            event.get_user_id(),
            event
        );

        let email_message = self.create_email_from_event(&event).await?;
        self.send_email(email_message).await?;

        info!(
            "Successfully processed email event: {} for user: {}",
            event.get_event_type(),
            event.get_user_id()
        );

        Ok(())
    }

    async fn create_email_from_event(&self, event: &EmailEvent) -> Result<EmailMessage, AppError> {
        // Sử dụng strategy factory thay vì match block
        let strategy = self.strategy_factory.get_strategy(event).ok_or_else(|| {
            AppError::Internal(anyhow::anyhow!(
                "No email strategy found for event type: {}",
                event.get_event_type()
            ))
        })?;

        strategy
            .create_email(event, &self.template_engine, &self.app_url, &self.app_name)
            .await
    }

    pub async fn send_email(&self, email_message: EmailMessage) -> Result<(), AppError> {
        let message_clone = email_message.clone();
        let message_clone2 = email_message.clone();
        let smtp_transport = self.smtp_transport.clone();
        let config = self.config.clone();

        self.circuit_breaker
            .execute(
                move || {
                    let email_message = message_clone;
                    let smtp_transport = smtp_transport.clone();
                    let config = config.clone();
                    async move {
                        if let Some(ref transport) = smtp_transport {
                            let email = Message::builder()
                                .from(Mailbox::new(
                                    Some(config.from_name.clone()),
                                    config.from_email.parse()?,
                                ))
                                .to(Mailbox::new(None, email_message.to.parse()?))
                                .subject(&email_message.subject)
                                .multipart(MultiPart::alternative_plain_html(
                                    email_message.text_body.unwrap_or_default(),
                                    email_message.html_body,
                                ))?;
                            transport.send(email).await?;
                            info!("Email sent to {}", email_message.to);
                            Ok(())
                        } else {
                            warn!("SMTP not configured: {:?}", email_message);
                            Ok(())
                        }
                    }
                },
                move || {
                    let message_clone = message_clone2;
                    async move {
                        error!("SMTP fallback: Re-queuing for {}", message_clone.to);
                        Err(AppError::Internal(anyhow!("SMTP open - queued")))
                    }
                },
            )
            .await
    }

    // Comment out or remove:
    /*
    fn build_lettre_message(&self, email_message: &EmailMessage) -> Result<Message, AppError> {
        let from_address = format!("{} <{}>", self.config.from_name, self.config.from_email);

        let message_builder =
            Message::builder()
                .from(from_address.parse().map_err(|e| {
                    AppError::Internal(anyhow::anyhow!("Invalid from address: {}", e))
                })?)
                .to(email_message.to.parse().map_err(|e| {
                    AppError::Internal(anyhow::anyhow!("Invalid to address: {}", e))
                })?)
                .subject(&email_message.subject);

        let message = if let Some(text_body) = &email_message.text_body {
            // Both HTML and text parts
            message_builder.multipart(
                MultiPart::alternative()
                    .singlepart(
                        SinglePart::builder()
                            .header(ContentType::TEXT_PLAIN)
                            .body(text_body.clone()),
                    )
                    .singlepart(
                        SinglePart::builder()
                            .header(ContentType::TEXT_HTML)
                            .body(email_message.html_body.clone()),
                    ),
            )
        } else {
            // HTML only
            message_builder
                .header(ContentType::TEXT_HTML)
                .body(email_message.html_body.clone())
        }
        .map_err(|e| AppError::Internal(anyhow::anyhow!("Failed to build email: {}", e)))?;

        Ok(message)
    }

    fn log_email_debug(&self, email_message: &EmailMessage) {
        info!("=== EMAIL DEBUG MODE ===");
        info!("To: {}", email_message.to);
        info!("Subject: {}", email_message.subject);
        info!("HTML Body Length: {} chars", email_message.html_body.len());
        if let Some(text_body) = &email_message.text_body {
            info!("Text Body Length: {} chars", text_body.len());
        }
        info!("========================");
    }
    */

    pub fn get_template_names(&self) -> Vec<String> {
        self.template_engine.get_template_names()
    }

    pub fn get_supported_strategies(&self) -> Vec<&'static str> {
        self.strategy_factory.get_registered_strategies()
    }
}

// Health check for email service
impl EmailService {
    pub fn is_configured(&self) -> bool {
        !self.config.smtp_username.is_empty() && !self.config.smtp_password.is_empty()
    }

    pub async fn health_check(&self) -> Result<(), AppError> {
        if let Some(transport) = &self.smtp_transport {
            match transport.test_connection().await {
                Ok(is_connected) => {
                    if is_connected {
                        info!("SMTP connection healthy");
                        Ok(())
                    } else {
                        warn!("SMTP connection test failed");
                        Err(AppError::Internal(anyhow::anyhow!(
                            "SMTP connection test failed"
                        )))
                    }
                }
                Err(e) => {
                    error!("SMTP health check error: {}", e);
                    Err(AppError::Internal(anyhow::anyhow!(
                        "SMTP health check error: {}",
                        e
                    )))
                }
            }
        } else {
            info!("Email service in debug mode - health check passed");
            Ok(())
        }
    }
}
