use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct EmailConfig {
    pub smtp_host: String,
    pub smtp_port: u16,
    pub smtp_username: String,
    pub smtp_password: String,
    pub from_email: String,
    pub from_name: String,
    pub use_tls: bool,
}

impl Default for EmailConfig {
    fn default() -> Self {
        Self {
            smtp_host: "localhost".to_string(),
            smtp_port: 587,
            smtp_username: "".to_string(),
            smtp_password: "".to_string(),
            from_email: "<EMAIL>".to_string(),
            from_name: "Platform Rust".to_string(),
            use_tls: true,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct EmailMessage {
    pub to: String,
    pub subject: String,
    pub html_body: String,
    pub text_body: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct EmailTemplate {
    pub name: String,
    pub subject_template: String,
    pub html_template: String,
    pub text_template: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailContext {
    pub user_name: String,
    pub user_email: String,
    pub reset_token: Option<String>,
    pub verification_token: Option<String>,
    pub permissions: Option<Vec<String>>,
    pub old_roles: Option<Vec<String>>,
    pub new_roles: Option<Vec<String>>,
    pub app_url: String,
    pub app_name: String,
}

impl EmailContext {
    pub fn new(user_name: String, user_email: String, app_url: String, app_name: String) -> Self {
        Self {
            user_name,
            user_email,
            reset_token: None,
            verification_token: None,
            permissions: None,
            old_roles: None,
            new_roles: None,
            app_url,
            app_name,
        }
    }

    pub fn with_reset_token(mut self, token: String) -> Self {
        self.reset_token = Some(token);
        self
    }

    pub fn with_verification_token(mut self, token: String) -> Self {
        self.verification_token = Some(token);
        self
    }

    pub fn with_permissions(mut self, permissions: Vec<String>) -> Self {
        self.permissions = Some(permissions);
        self
    }

    pub fn with_role_change(mut self, old_roles: Vec<String>, new_roles: Vec<String>) -> Self {
        self.old_roles = Some(old_roles);
        self.new_roles = Some(new_roles);
        self
    }
}
