use crate::{constants::system as system_codes, response::ApiResponse};
use axum::{
    J<PERSON>,
    http::{Method, StatusCode, Uri},
    response::IntoResponse,
};

// Handler for 404 Not Found - when route doesn't exist
pub async fn handler_404(uri: Uri) -> impl IntoResponse {
    let response = ApiResponse::<()>::error(
        uri.path().to_string(),
        404,
        system_codes::ERROR_NOT_FOUND.to_string(),
        format!("Route '{}' not found", uri.path()),
    );

    (StatusCode::NOT_FOUND, <PERSON><PERSON>(response))
}

// Generic fallback handler that determines the appropriate error
pub async fn fallback_handler(_method: Method, uri: Uri) -> impl IntoResponse {
    // For now, we'll treat everything as 404
    // In a more sophisticated setup, you could check if the route exists
    // but with wrong method to return 405
    handler_404(uri).await
}
